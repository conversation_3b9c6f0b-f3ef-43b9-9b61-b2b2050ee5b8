// Configuration file for VidyaArena app
import { Platform } from 'react-native';

// Get the local IP address for development
const getApiUrl = () => {
  if (Platform.OS === 'web') {
    return 'http://localhost:5000/api';
  }

  // For Expo Go on physical devices, we need to use the computer's IP
  // You can find your IP by running: ipconfig (Windows) or ifconfig (Mac/Linux)
  // For now, using localhost for simulator/emulator
  if (Platform.OS === 'android') {
    return 'http://********:5000/api'; // Android emulator
  }

  if (Platform.OS === 'ios') {
    return 'http://localhost:5000/api'; // iOS simulator
  }

  return 'http://localhost:5000/api';
};

export const config = {
  // Backend API URL - automatically configured based on platform
  API_BASE_URL: getApiUrl(),

  // App configuration
  APP_NAME: 'VidyaArena',
  APP_VERSION: '1.0.0',

  // API timeout
  API_TIMEOUT: 10000, // 10 seconds
};
