{"version": 3, "file": "customizeFrame.js", "sourceRoot": "", "sources": ["../src/customizeFrame.ts"], "names": [], "mappings": ";;;AAmFA,4DAyDC;AA1ID,6BAA0B;AAI1B,wFAAwF;AAC3E,QAAA,wBAAwB,GAAG,IAAI,MAAM,CAChD;IACE,8CAA8C;IAC9C,6CAA6C;IAC7C,+BAA+B;IAC/B,4BAA4B;IAC5B,iCAAiC;IACjC,2CAA2C;IAC3C,qCAAqC;IACrC,iCAAiC;IACjC,sDAAsD;IACtD,oDAAoD;IACpD,sCAAsC;IACtC,iCAAiC;IACjC,yCAAyC;IACzC,uCAAuC;IACvC,6DAA6D;IAC7D,qCAAqC;IACrC,8CAA8C;IAC9C,kGAAkG;IAClG,iCAAiC;IACjC,iCAAiC;IACjC,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAC5C,iDAAiD;IACjD,sCAAsC;IACtC,gCAAgC;IAChC,+CAA+C;IAC/C,gCAAgC;IAChC,mBAAmB;IACnB,uBAAuB;IACvB,iCAAiC;IACjC,+BAA+B;IAC/B,2CAA2C;IAC3C,6BAA6B;IAC7B,6BAA6B;IAC7B,sBAAsB;IACtB,+BAA+B;IAC/B,yBAAyB;IACzB,sCAAsC;IACtC,+BAA+B;IAE/B,6BAA6B;IAC7B,8BAA8B;IAC9B,6EAA6E;IAC7E,iCAAiC;IACjC,mBAAmB;IACnB,eAAe;IACf,0CAA0C;IAC1C,sBAAsB;IACtB,+FAA+F;IAC/F,iCAAiC;IACjC,8GAA8G;IAC9G,gDAAgD;IAEhD,0BAA0B;IAC1B,kBAAkB;CACnB,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;AAEF,SAAS,KAAK,CAAC,KAAa;IAC1B,IAAI,CAAC;QACH,kCAAkC;QAClC,IAAI,SAAG,CAAC,KAAK,CAAC,CAAC;QACf,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,wBAAwB;IACtC,OAAO,CAAC,KAAwC,EAAE,EAAE;QAClD,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,OAAO;gBACL,GAAG,KAAK;gBACR,qFAAqF;gBACrF,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,IAAI;gBACZ,+DAA+D;gBAC/D,QAAQ,EAAE,IAAI;aACf,CAAC;QACJ,CAAC;QACD,IAAI,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,gCAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QAEhF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,qDAAqD;YACrD,oCAAoC;YACpC,+FAA+F;YAC/F,IACE,KAAK,CAAC,MAAM,KAAK,CAAC;gBAClB,KAAK,CAAC,UAAU;gBAChB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC;gBACpD,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,eAAe,CAAC,EAClC,CAAC;gBACD,QAAQ,GAAG,IAAI,CAAC;YAClB,CAAC;iBAAM,IACL,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC;gBAC1D,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,EAC7C,CAAC;gBACD,kFAAkF;gBAClF,qDAAqD;gBACrD,QAAQ,GAAG,IAAI,CAAC;YAClB,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBACrC,QAAQ,GAAG,IAAI,CAAC;YAClB,CAAC;iBAAM;YACL,+DAA+D;YAC/D,KAAK,CAAC,IAAI,KAAK,aAAa;gBAC5B,KAAK,CAAC,UAAU;gBAChB;oBACE,QAAQ;oBACR,UAAU;oBACV,eAAe;oBACf,SAAS;oBACT,eAAe;oBACf,sBAAsB;oBACtB,uBAAuB;oBACvB,WAAW;oBACX,yBAAyB;oBACzB,8BAA8B;iBAC/B,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,EAC5B,CAAC;gBACD,QAAQ,GAAG,IAAI,CAAC;YAClB,CAAC;QACH,CAAC;QAED,OAAO,EAAE,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC;IACxC,CAAC,CAAC;AACJ,CAAC"}