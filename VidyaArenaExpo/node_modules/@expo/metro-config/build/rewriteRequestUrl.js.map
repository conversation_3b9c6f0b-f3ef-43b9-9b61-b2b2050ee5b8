{"version": 3, "file": "rewriteRequestUrl.js", "sourceRoot": "", "sources": ["../src/rewriteRequestUrl.ts"], "names": [], "mappings": ";;;;;AAmDA,gDASC;AAED,oDAmFC;AAjJD,qEAAqE;AACrE,yCAAqD;AACrD,8CAA2E;AAC3E,kDAA0B;AAC1B,4CAAoB;AACpB,gDAAwB;AAExB,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,qCAAqC,CAAC,CAAC;AAEtE,SAAS,mBAAmB,CAAC,IAAY;IACvC,IAAI,CAAC;QACH,OAAO,YAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,IAAI,KAAK,CAAC;IACnD,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,SAAS,qBAAqB,CAC5B,UAAqE,EACrE,QAAgB;IAEhB,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,SAAS,CAAC,CAAC,CAAC;YACf,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC;QACzE,CAAC;QACD,KAAK,KAAK,CAAC,CAAC,CAAC;YACX,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC;QACrE,CAAC;QACD;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC;AACD,SAAS,4BAA4B,CAAC,GAAe,EAAE,IAAY,EAAE,QAAgB;IACnF,IAAI,kBAAkB,CAAC;IAEvB,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;QACnC,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,CAAC;QACnD,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,WAAW,CAAC,EAAE,CAAC;YACvD,kBAAkB,GAAG,WAAW,CAAC;QACnC,CAAC;aAAM,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;YAC3C,kBAAkB,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC;QACpE,CAAC;IACH,CAAC;IAED,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,sCAAsC,CAAC,WAAmB,EAAE,GAAe;IAClF,OAAO,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,kBAAkB,CAAC,WAAW,CAAC,CAAC;AACpE,CAAC;AAED,SAAgB,kBAAkB,CAAC,WAAmB;IACpD,kCAAkC;IAClC,IAAI,mBAAmB,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QAC9D,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC9D,OAAO,cAAI,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,kDAAkD,CAAC,CAAC;IAC1D,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAgB,oBAAoB,CAAC,WAAmB;IACtD,SAAS,qBAAqB,CAAC,GAAW;QACxC,iJAAiJ;QACjJ,oNAAoN;QACpN,IAAI,GAAG,CAAC,QAAQ,CAAC,qCAAqC,CAAC,EAAE,CAAC;YACxD,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAA,kBAAS,EAAC,WAAW,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,CAAC,CAAC;YACjF,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YACtF,gEAAgE;YAChE,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;YAEjC,MAAM,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;YAClF,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;YAEvD,KAAK,CAAC,4CAA4C,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC;YAEvE,MAAM,KAAK,GAAG,IAAA,yBAAiB,EAAC,WAAW,EAAE;gBAC3C,QAAQ;gBACR,GAAG;aACJ,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,KAAK,CACb,IAAA,eAAK,EAAA,2DAA2D,QAAQ,WAAW,WAAW,uDAAuD,CACtJ,CAAC;YACJ,CAAC;YAED,mFAAmF;YACnF,0CAA0C;YAC1C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,CAAC;gBACtD,OAAO,CAAC,YAAY,CAAC,GAAG,CACtB,sBAAsB,EACtB,sCAAsC,CAAC,WAAW,EAAE,GAAG,CAAC,CACzD,CAAC;YACJ,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE,aAAa,EAAE,CAAC;gBAC3F,OAAO,CAAC,YAAY,CAAC,GAAG,CACtB,yBAAyB,EACzB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC,CACzC,CAAC;YACJ,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACvD,MAAM,WAAW,GAAG,4BAA4B,CAC9C,GAAG,EACH,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,EACpC,QAAQ,CACT,CAAC;gBACF,IAAI,WAAW,EAAE,CAAC;oBAChB,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;gBACzE,CAAC;YACH,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAClD,MAAM,eAAe,GAAG,qBAAqB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;gBAC7D,IAAI,eAAe,EAAE,CAAC;oBACpB,KAAK,CAAC,qCAAqC,CAAC,CAAC;oBAC7C,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;oBACvD,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;oBACpD,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,2BAA2B,EAAE,eAAe,CAAC,CAAC;gBACzE,CAAC;YACH,CAAC;YAED,MAAM,UAAU,GAAG,IAAA,0BAAkB,EAAC,WAAW,CAAC,CAAC;YACnD,MAAM,aAAa,GAAG,cAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;YACjF,KAAK,CAAC,sBAAsB,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,CAAC;YAEpE,gDAAgD;YAChD,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxB,4HAA4H;gBAC5H,OAAO,GAAG,GAAG,aAAa,GAAG,UAAU,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;YACpE,CAAC;YAED,6DAA6D;YAC7D,OAAO,CAAC,QAAQ,GAAG,GAAG,GAAG,aAAa,GAAG,SAAS,CAAC;YAEnD,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrC,KAAK,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YAChC,kJAAkJ;YAClJ,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IACD,OAAO,qBAAqB,CAAC;AAC/B,CAAC"}