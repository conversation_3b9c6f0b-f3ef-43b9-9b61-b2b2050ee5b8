{"version": 3, "file": "loadBabelConfig.js", "sourceRoot": "", "sources": ["../src/loadBabelConfig.ts"], "names": [], "mappings": ";;;;;;AAAA;;;;;;GAMG;AACH,sDAAyB;AACzB,0DAA6B;AAI7B;;;;GAIG;AACU,QAAA,eAAe,GAAG,CAAC;IAC9B,IAAI,OAAO,GAAyD,IAAI,CAAC;IAEzE,OAAO,SAAS,WAAW,CAAC,EAAE,WAAW,EAA2B;QAClE,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YACrB,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,GAAG,EAAE,CAAC;QAEb,IAAI,WAAW,EAAE,CAAC;YAChB,2DAA2D;YAC3D,iHAAiH;YACjH,MAAM,oBAAoB,GAAG,CAAC,UAAU,EAAE,aAAa,EAAE,iBAAiB,CAAC,CAAC;YAE5E,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE,CACpE,iBAAE,CAAC,UAAU,CAAC,mBAAI,CAAC,OAAO,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,CACzD,CAAC;YAEF,oDAAoD;YACpD,IAAI,gBAAgB,EAAE,CAAC;gBACrB,OAAO,CAAC,OAAO,GAAG,mBAAI,CAAC,OAAO,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QAED,2EAA2E;QAC3E,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACrB,IAAI,CAAC;gBACH,OAAO,CAAC,OAAO,GAAG,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC,CAAC;YAC5D,CAAC;YAAC,MAAM,CAAC;gBACP,mEAAmE;gBACnE,sDAAsD;gBACtD,OAAO,CAAC,OAAO,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;AACJ,CAAC,CAAC,EAAE,CAAC"}