"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.importExportPlugin = exports.importExportLiveBindingsPlugin = void 0;
var importExportLiveBindings_1 = require("./importExportLiveBindings");
Object.defineProperty(exports, "importExportLiveBindingsPlugin", { enumerable: true, get: function () { return importExportLiveBindings_1.importExportLiveBindingsPlugin; } });
var importExportStatic_1 = require("./importExportStatic");
Object.defineProperty(exports, "importExportPlugin", { enumerable: true, get: function () { return importExportStatic_1.importExportPlugin; } });
//# sourceMappingURL=index.js.map