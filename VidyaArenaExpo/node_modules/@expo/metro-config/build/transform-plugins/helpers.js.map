{"version": 3, "file": "helpers.js", "sourceRoot": "", "sources": ["../../src/transform-plugins/helpers.ts"], "names": [], "mappings": ";;;AA6JS,oCAAY;AA3Jd,MAAM,iBAAiB,GAAG,CAAC,EAAE,SAAS,EAAmB,EAAE,IAAY,EAAmB,EAAE,CACjG,SAAS,CAAC;;;;GAIT,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AALF,QAAA,iBAAiB,qBAKf;AAER,MAAM,mBAAmB,GAAG,CACjC,EAAE,SAAS,EAAmB,EAC9B,IAAY,EACK,EAAE;IACnB,sFAAsF;IACtF,OAAO,SAAS,CAAC;;;;;;;;;;;;;;GAchB,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AACf,CAAC,CAAC;AApBW,QAAA,mBAAmB,uBAoB9B;AAEK,MAAM,mBAAmB,GAAG,CACjC,EAAE,SAAS,EAAmB,EAC9B,EAAU,EACO,EAAE;IACnB,OAAO,SAAS,CAAC;;;;;;;;;GAShB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACb,CAAC,CAAC;AAdW,QAAA,mBAAmB,uBAc9B;AAEK,MAAM,gBAAgB,GAAG,CAC9B,CAAe,EACf,UAAkB,EAClB,IAAsB,EACL,EAAE;IACnB,OAAO,CAAC,CAAC,mBAAmB,CAC1B,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,EAAE;QAC3F,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC;QACvB,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC;QAC3B,CAAC,CAAC,gBAAgB,CAAC;YACjB,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpE,CAAC,CAAC,cAAc,CACd,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,EACnB,CAAC,CAAC,kBAAkB,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAC5E;SACF,CAAC;KACH,CAAC,CACH,CAAC;AACJ,CAAC,CAAC;AAlBW,QAAA,gBAAgB,oBAkB3B;AAEK,MAAM,kBAAkB,GAAG,CAChC,CAAe,EACf,UAAkB,EAClB,IAAsB,EACL,EAAE;IACnB,IAAI,UAAU,KAAK,WAAW,EAAE,CAAC;QAC/B,sGAAsG;QACtG,oGAAoG;QACpG,gBAAgB;QAChB,OAAO,IAAA,wBAAgB,EAAC,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IAC/C,CAAC;IACD,MAAM,MAAM,GAAG,CAAC,CAAC,iBAAiB,CAAC,UAAU,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IAChC,OAAO,CAAC,CAAC,mBAAmB,CAC1B,CAAC,CAAC,oBAAoB,CAAC,GAAG,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,CACvF,CAAC;AACJ,CAAC,CAAC;AAjBW,QAAA,kBAAkB,sBAiB7B;AAEK,MAAM,mBAAmB,GAAG,CACjC,CAAe,EACf,IAAY,EACZ,IAAsB,EACL,EAAE,CACnB,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AALpE,QAAA,mBAAmB,uBAKiD;AAEjF,wCAAwC;AACjC,MAAM,WAAW,GAAG,CACzB,CAAe,EACf,EAAU,EACV,MAA2B,EACA,EAAE,CAC7B,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE;IAC3B,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;CAC5F,CAAC,CAAC;AAPQ,QAAA,WAAW,eAOnB;AAEL,6BAA6B;AACtB,MAAM,qBAAqB,GAAG,CACnC,CAAe,EACf,MAA2B,EACV,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAHpF,QAAA,qBAAqB,yBAG+D;AAEjG,uCAAuC;AAChC,MAAM,uBAAuB,GAAG,CACrC,CAAe,EACf,EAAU,EACV,EAAU,EACV,GAAW,EACM,EAAE,CACnB,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE;IAC3B,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAChG,CAAC,CAAC;AARQ,QAAA,uBAAuB,2BAQ/B;AAEL,oDAAoD;AAC7C,MAAM,sBAAsB,GAAG,CAAC,EAAE,SAAS,EAAmB,EAAmB,EAAE;IACxF,OAAO,SAAS,CAAC;;GAEhB,CAAC,EAAE,CAAC;AACP,CAAC,CAAC;AAJW,QAAA,sBAAsB,0BAIjC;AAEK,MAAM,mBAAmB,GAAG,CACjC,CAAe,EACf,IAAsB,EACS,EAAE,CACjC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAJlE,QAAA,mBAAmB,uBAI+C;AAY/E,SAAS,YAAY,CACnB,WAAqC,EACrC,GAA4C;IAE5C,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;QAC/B,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACtD,CAAC;IACD,MAAM,IAAI,GAAG,WAAoB,CAAC;IAClC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}