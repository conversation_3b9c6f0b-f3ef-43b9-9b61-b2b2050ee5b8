{"version": 3, "file": "reconcileTransformSerializerPlugin.js", "sourceRoot": "", "sources": ["../../src/serializer/reconcileTransformSerializerPlugin.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA,4CAkFC;AAMD,oCAGC;AAGD,gFA8LC;AAtUD,iEAAwC;AAOxC,yGAAiF;AACjF,sGAAsF;AACtF,qFAAkF;AAElF,mEAA8D;AAC9D,iFAA6E;AAC7E,oDAA4B;AAC5B,0DAA6B;AAE7B,yCAA0D;AAC1D,+CAA4D;AAC5D,iGAMkD;AAClD,iEAA4E;AAC5E,uFAIoD;AAMpD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAuB,CAAC;AAEzE,MAAM,wBAAwB,GAAG,KAAK,CAAC;AAEvC,iFAAiF;AACjF,iEAAiE;AACjE,SAAgB,gBAAgB,CAC9B,YAAmC,EACnC,WAAmC;IAEnC,iFAAiF;IACjF,iEAAiE;IACjE,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAsB,CAAC;IAEvD,MAAM,cAAc,GAAG,CACrB,GAGE,EACF,EAAE;QACF,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAE/C,8DAA8D;QAC9D,EAAE;QACF,qHAAqH;QACrH,gGAAgG;QAChG,EAAE;QACF,mGAAmG;QACnG,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,oHAAoH;QACpH,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,IAAA,8BAAO,EACxB,IAAA,0CAAmB,EAAC;gBAClB,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS;gBAC7B,WAAW,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW;gBAClC,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,aAAa;aACtC,CAAC,CACH,CAAC;YAEF,IAAI,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;gBAChC,OAAO,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,uEAAuE;QACvE,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,KAAK,CACH,uCAAuC,EACvC,mBAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAC7C,mBAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CACtD,CAAC;QAEF,MAAM,IAAI,KAAK,CACb,cAAc,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,gFAAgF,KAAK,CAAC,IAAI,CAC/H,WAAW,CAAC,OAAO,EAAE,CACtB;aACE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;aAChD,IAAI,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;IACJ,CAAC,CAAC;IAEF,mGAAmG;IACnG,YAAY,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;QAC3B,MAAM,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;QAErC,sFAAsF;QACtF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE;gBACjC,uFAAuF;gBACvF,IAAI,EAAE,GAAG;aACV,CAAC,CAAC;QACL,CAAC;QAED,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE;YACjC,GAAG,QAAQ;YACX,uFAAuF;YACvF,IAAI,EAAE,GAAG;SACV,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,SAAS,iBAAiB,CAAC,KAAoB;IAC7C,OAAO,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AACzC,CAAC;AAED,SAAgB,YAAY,CAAC,KAAoB,EAAE,IAAY;IAC7D,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,sBAAsB;QAAE,OAAO,KAAK,CAAC;IACjE,OAAO,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC;AAChF,CAAC;AAED,yLAAyL;AAClL,KAAK,UAAU,kCAAkC,CACtD,UAAkB,EAClB,UAA0C,EAC1C,KAAoB,EACpB,OAA0B;IAE1B,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9B,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,oFAAoF;IACpF,+GAA+G;IAC/G,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;QAChD,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,IAAA,yBAAc,EAAC,MAAM,CAAC,EAAE,CAAC;gBAC3B,sCAAsC;gBACtC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;oBACjB,EAAE;oBACF,MAAM,yBAAyB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAEhD,KAAK,UAAU,yBAAyB,CACtC,KAA0B,EAC1B,UAAwB;QAExB,IACE,UAAU,CAAC,IAAI,KAAK,WAAW;YAC/B,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC5B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,EACnC,CAAC;YACD,KAAK,CAAC,6CAA6C,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;YAClE,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,2FAA2F;QAC3F,0DAA0D;QAC1D,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;QAE5C,IAAA,gBAAM,EAAC,SAAS,EAAE,yEAAyE,CAAC,CAAC;QAE7F,IAAI,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC;QAC9B,IAAA,gBAAM,EAAC,GAAG,EAAE,0BAA0B,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;QACrD,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC;QAE3B,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,SAAS,CAAC;QAE/C,MAAM,oBAAoB,GAAG,GAAG,EAAE,CAChC,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;aAC7B,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE;YACd,MAAM,OAAO,GAAG,IAAA,2CAAoB,EAAC,GAAG,CAAC;gBACvC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;gBAC1C,CAAC,CAAC,SAAS,CAAC;YACd,OAAO,OAAO,IAAI,IAAA,yCAA2B,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5E,CAAC,CAAC;aACD,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEjC,MAAM,IAAI,GAAG,IAAA,2CAAkB,EAAC,GAAG,EAAE;YACnC,gBAAgB,EAAE,IAAI;YACtB,QAAQ,EAAE,KAAK,CAAC,IAAI;YACpB,SAAS;YACT,aAAa;YACb,OAAO,EAAE;gBACP,oCAAoC;gBACpC,GAAG,KAAK,CAAC,gBAAgB;gBAEzB,yBAAyB,EAAE,IAAI;gBAE/B,cAAc,EAAE,SAAS,CAAC,cAAc;gBACxC,uCAAuC;gBACvC,kBAAkB,EAAE,SAAS,CAAC,cAAc;oBAC1C,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,kBAAkB;wBACzC,CAAC,CAAC,oBAAoB,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;wBAC1E,CAAC,CAAC,oBAAoB,EAAE;oBAC1B,CAAC,CAAC,EAAE;aACP;SACF,CAAC,CAAC;QAEH,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAEf,IAAI,iBAAiB,GAAG,EAAE,CAAC;QAC3B,IAAI,YAAmC,CAAC;QAExC,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,8BAA8B,IAAI,IAAI,CAAC;QAC3F,oEAAoE;QACpE,IAAI,CAAC;YACH,wEAAwE;YACxE,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,iBAAiB,EAAE,GAAG,IAAA,8BAAmB,EAAC,GAAG,EAAE;gBACnE,GAAG,SAAS,CAAC,0BAA0B;gBACvC,4BAA4B,EAC1B,qBAAqB,IAAI,IAAI;oBAC3B,CAAC,CAAC,CAAC,GAAqB,EAAE,EAAE;wBACxB,OAAO,qBAAqB,CAAC,GAAG,CAAC,IAAA,gCAAQ,EAAC,GAAG,CAAC,CAAC,CAAC;oBAClD,CAAC;oBACH,CAAC,CAAC,IAAI;gBACV,WAAW,EAAE,KAAK;gBAClB,uCAAuC;gBACvC,gBAAgB,EAAE,wBAAwB;gBAC1C,gFAAgF;gBAChF,qBAAqB,EAAE,SAAS;aACjC,CAAC,CAAC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,8CAA+B,EAAE,CAAC;gBACrD,MAAM,IAAI,gDAAuB,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;QAED,iDAAiD;QACjD,KAAK,CAAC,YAAY;YAChB,EAAE;YACF,gBAAgB,CAAC,YAAY,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;QAErD,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,wBAAc,CAAC,UAAU,CACnD,GAAG,EACH,SAAS,CAAC,aAAa,EACvB,SAAS,CAAC,SAAS,EACnB,iBAAiB,EACjB,SAAS,CAAC,YAAY,EACtB,SAAS,CAAC,sBAAsB,KAAK,KAAK,CAC3C,CAAC;QAEF,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,IAAI,SAAS,CAAC,kCAAkC,IAAI,IAAI,EAAE,CAAC;YACzD,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,SAAS,CAAC,sBAAsB,EAAE,CAAC;YACrC,sEAAsE;YACtE,QAAQ,CAAC,IAAI,CACX,GAAG,IAAA,gDAAsB,EAAC,UAAU,EAAE;gBACpC,aAAa,EAAE,QAAQ;aACxB,CAAC,CACH,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,IAAA,mBAAQ,EACrB,UAAU,EACV;YACE,kBAAkB;YAClB,mIAAmI;YACnI,OAAO,EAAE,SAAS,CAAC,sBAAsB;YACzC,QAAQ,EAAE,KAAK,CAAC,IAAI;YACpB,WAAW,EAAE,KAAK;YAClB,cAAc,EAAE,KAAK,CAAC,IAAI;YAC1B,UAAU,EAAE,IAAI;SACjB,EACD,UAAU,CAAC,IAAI,CAAC,IAAI,CACrB,CAAC;QAEF,+CAA+C;QAC/C,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,iCAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3E,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QAEvB,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YACrB,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAEnD,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,MAAM,IAAA,mCAAU,EAC/B,SAAS,CAAC,MAAM,EAChB,KAAK,CAAC,IAAI,EACV,MAAM,CAAC,IAAI,EACX,MAAM,EACN,GAAG,EACH,QAAQ,CACT,CAAC,CAAC;QACL,CAAC;QAED,IAAI,SAAS,CAAC;QACd,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,IAAA,uCAAyB,EAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;QAE5D,OAAO;YACL,GAAG,UAAU;YACb,IAAI,EAAE;gBACJ,GAAG,UAAU,CAAC,IAAI;gBAClB,IAAI;gBACJ,GAAG;gBACH,SAAS;gBACT,WAAW;gBACT,2JAA2J;gBAC3J,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW;oBAChC,8EAA8E;oBAC9E,GAAG,CAAC,WAAW;oBACf,UAAU,CAAC,IAAI,CAAC,WAAW;oBAC3B,IAAI;aACP;SACF,CAAC;IACJ,CAAC;AACH,CAAC"}