{"version": 3, "file": "withExpoSerializers.js", "sourceRoot": "", "sources": ["../../src/serializer/withExpoSerializers.ts"], "names": [], "mappings": ";;;;;AAsDA,kDAiBC;AAID,sDAqBC;AAED,kFAkJC;AAsGD,oFAoBC;AAtWD,iHAA4F;AAE5F,0FAAkE;AAElE,+CAAyD;AAEzD,uCAAyC;AACzC,+FAG+C;AAC/C,sDAA0E;AAC1E,6FAA0F;AAC1F,uDAA+E;AAE/E,2EAAkE;AAClE,gCAA6B;AAyB7B,MAAM,eAAe,GACnB,OAAO,yBAAkB,KAAK,UAAU;IACtC,CAAC,CAAC,yBAAkB,CAAC,eAAe;IACpC,CAAC,CAAC,yBAAkB,CAAC;AAEzB,SAAgB,mBAAmB,CACjC,MAAoB,EACpB,UAAmC,EAAE;IAErC,MAAM,UAAU,GAAuB,EAAE,CAAC;IAC1C,UAAU,CAAC,IAAI,CAAC,mEAA6B,CAAC,CAAC;IAC/C,IAAI,CAAC,SAAG,CAAC,uBAAuB,EAAE,CAAC;QACjC,UAAU,CAAC,IAAI,CAAC,yEAAmC,CAAC,CAAC;IACvD,CAAC;IAED,+BAA+B;IAC/B,UAAU,CAAC,IAAI,CAAC,+CAAmB,CAAC,CAAC;IAErC,uDAAuD;IACvD,UAAU,CAAC,IAAI,CAAC,uEAAkC,CAAC,CAAC;IAEpD,OAAO,qBAAqB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAC5D,CAAC;AAED,iFAAiF;AACjF,qBAAqB;AACrB,SAAgB,qBAAqB,CACnC,MAAoB,EACpB,UAA8B,EAC9B,UAAmC,EAAE;IAErC,MAAM,cAAc,GAAG,oCAAoC,CACzD,MAAM,EACN,UAAU,EACV,MAAM,CAAC,UAAU,EAAE,gBAAgB,IAAI,IAAI,EAC3C,OAAO,CACR,CAAC;IAEF,mFAAmF;IACnF,qFAAqF;IAErF,yFAAyF;IACzF,MAAM,CAAC,UAAU,KAAK,EAAE,CAAC;IACzB,yFAAyF;IACzF,MAAM,CAAC,UAAU,CAAC,gBAAgB,GAAG,cAAc,CAAC;IAEpD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAgB,mCAAmC,CACjD,MAA4B,EAC5B,gBAAyC,EAAE;IAE3C,OAAO,KAAK,EACV,UAAkB,EAClB,UAA0C,EAC1C,KAAiC,EACjC,YAA4C,EACK,EAAE;QACnD,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC;QACjD,mGAAmG;QACnG,MAAM,aAAa,GAAG,YAAY,CAAC,eAAe,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC;QAE9E,MAAM,OAAO,GAAG;YACd,QAAQ,EAAE,KAAK,CAAC,gBAAgB,EAAE,QAAQ;YAC1C,WAAW,EAAE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAE,WAAW,IAAI,QAAQ;SACrF,CAAC;QAEF,MAAM,OAAO,GAAmC;YAC9C,GAAG,YAAY;YACf,cAAc,EAAE,CAAC,QAAQ,EAAE,GAAG,KAAK,EAAE,EAAE;gBACrC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACrB,OAAO,YAAY,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,KAAK,CAAC,CAAC;gBACzD,CAAC;gBAED,OAAO,YAAY,CAAC,cAAc,CAChC,QAAQ;gBACR,gGAAgG;gBAChG,OAAO,CACR,CAAC;YACJ,CAAC;SACF,CAAC;QAEF,IAAI,OAA2B,CAAC;QAChC,MAAM,WAAW,GAAG,GAAG,EAAE;YACvB,IAAI,CAAC,aAAa,IAAI,OAAO,EAAE,CAAC;gBAC9B,OAAO,OAAO,CAAC;YACjB,CAAC;YAED,8BAA8B;YAC9B,MAAM,MAAM,GAAG,IAAA,2BAAY,EAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE;gBACzD,GAAG,OAAO;gBACV,OAAO,EAAE,SAAS;aACnB,CAAC,CAAC;YACH,MAAM,UAAU,GAAG,IAAA,wBAAc,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC;YAC/C,OAAO,GAAG,IAAA,sBAAY,EAAC,UAAU,CAAC,CAAC;YACnC,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC;QAEF,IAAI,kBAAkB,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;QAEzC,IAAI,UAAU,GAAkB,IAAI,CAAC;QACrC,IAAI,SAAS,GAAkB,IAAI,CAAC;QAEpC,+DAA+D;QAC/D,yGAAyG;QACzG,MAAM,wBAAwB,GAAG,wBAAwB,CAAC,MAAM,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;QAC/F,IAAI,wBAAwB,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,wBAAwB,CAAC,UAAU,EAAE,kBAAkB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAC9F,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC/B,UAAU,GAAG,MAAM,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACN,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;gBACzB,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC;YACzB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,OAAO,GAAG,WAAW,EAAE,CAAC;YAC9B,IAAI,aAAa,CAAC,wCAAwC,EAAE,CAAC;gBAC3D,KAAK,MAAM,MAAM,IAAI,aAAa,CAAC,wCAAwC,EAAE,CAAC;oBAC5E,kBAAkB,GAAG,MAAM,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,GAAG,kBAAkB,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;gBACvF,CAAC;YACH,CAAC;YACD,UAAU,GAAG,IAAA,wBAAc,EACzB,IAAA,2BAAY,EAAC,UAAU,EAAE,kBAAkB,EAAE,KAAK,EAAE;gBAClD,GAAG,OAAO;gBACV,OAAO;aACR,CAAC,CACH,CAAC,IAAI,CAAC;QACT,CAAC;QAED,MAAM,cAAc,GAAG,GAAG,EAAE;YAC1B,SAAS,KAAK,eAAe,CAC3B,CAAC,GAAG,kBAAkB,EAAE,GAAG,IAAA,kCAAgB,EAAC,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,EACvF;gBACE,8BAA8B;gBAC9B,aAAa,EAAE,KAAK;gBACpB,2DAA2D;gBAC3D,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;gBAChD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;aACrD,CACF,CAAC;YAEF,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACpC,MAAM,GAAG,GAAG,IAAA,2BAAY,EAAC,OAAO,CAAC,SAAS,CAAC;gBACzC,CAAC,CAAC,IAAA,0BAAW,EAAC,OAAO,CAAC,SAAS,CAAC;gBAChC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;YACtB,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;YAC/C,2CAA2C;YAC3C,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACrC,OAAO;oBACL,IAAI,EAAE,UAAU;oBAChB,GAAG,EAAE,cAAc,EAAE;iBACtB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;gBACtB,OAAO,UAAU,CAAC;YACpB,CAAC;YACD,OAAO;gBACL,IAAI,EAAE,UAAU;gBAChB,GAAG,EAAE,SAAS;aACf,CAAC;QACJ,CAAC;QAED,cAAc;QAEd,SAAS,KAAK,cAAc,EAAE,CAAC;QAE/B,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,0BAA0B,GAAG,CAAC,SAAiB,EAAE,EAAE;gBACvD,qHAAqH;gBACrH,8DAA8D;gBAC9D,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBAC9C,eAAe,CAAC,OAAO,GAAG,WAAW,EAAE,CAAC;gBACxC,4CAA4C;gBAC5C,sCAAsC;gBACtC,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACzC,CAAC,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,UAAU;gBAChB,GAAG,EAAE,0BAA0B,CAAC,SAAS,CAAC;aAC3C,CAAC;QACJ,CAAC;QAED,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,GAAG,EAAE,SAAS;SACf,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAC3B,MAAmB,EACnB,kBAAsC,EACtC,gBAAyC,EAAE;IAE3C,MAAM,iBAAiB,GACrB,kBAAkB,IAAI,mCAAmC,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAEnF,MAAM,cAAc,GAAG,KAAK,EAC1B,UAAkB,EAClB,UAA0C,EAC1C,KAAiC,EACjC,YAAmC,EACc,EAAE;QACnD,MAAM,OAAO,GAAG;YACd,QAAQ,EAAE,KAAK,CAAC,gBAAgB,EAAE,QAAQ;YAC1C,WAAW,EAAE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAE,WAAW,IAAI,QAAQ;SACrF,CAAC;QAEF,MAAM,OAAO,GAA0B;YACrC,GAAG,YAAY;YACf,cAAc,EAAE,CAAC,QAAQ,EAAE,GAAG,KAAK,EAAE,EAAE;gBACrC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACrB,OAAO,YAAY,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,KAAK,CAAC,CAAC;gBACzD,CAAC;gBACD,OAAO,YAAY,CAAC,cAAc,CAChC,QAAQ;gBACR,gGAAgG;gBAChG,OAAO,CACR,CAAC;YACJ,CAAC;SACF,CAAC;QAEF,MAAM,uBAAuB,GAAG,YAAY,CAAC,iBAAiB,CAAC;QAE/D,uEAAuE;QACvE,gHAAgH;QAChH,MAAM,uBAAuB,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM,CAAC;QAElE,MAAM,iBAAiB,GAAG,CAAC,GAAG,EAAE;YAC9B,IAAI,uBAAuB,EAAE,CAAC;gBAC5B,OAAO;oBACL,UAAU,EAAE,uBAAuB,CAAC,MAAM;oBAC1C,WAAW,EAAE,uBAAuB,CAAC,WAAW;oBAChD,WAAW,EAAE,uBAAuB,CAAC,WAAW;oBAChD,iBAAiB,EAAE,uBAAuB,CAAC,iBAAiB;iBAC7D,CAAC;YACJ,CAAC;YACD,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACtB,MAAM,SAAS,GAAG,IAAA,2BAAY,EAAC,OAAO,CAAC,SAAS,CAAC;oBAC/C,CAAC,CAAC,IAAA,0BAAW,EAAC,OAAO,CAAC,SAAS,CAAC;oBAChC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;gBAEtB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;gBAEnD,OAAO;oBACL,UAAU,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,mBAAmB,CAAC;oBACrD,WAAW,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,wBAAwB,CAAC,KAAK,MAAM;oBACtE,WAAW,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,wBAAwB,CAAC,KAAK,MAAM;oBACtE,iBAAiB,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,MAAM;iBACrE,CAAC;YACJ,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,iBAAiB,EAAE,UAAU,KAAK,QAAQ,EAAE,CAAC;YAC/C,OAAO,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACnE,CAAC;QAED,yDAAyD;QACzD,OAAO,CAAC,iBAAiB,GAAG;YAC1B,GAAG,OAAO,CAAC,iBAAiB;YAC5B,GAAG,iBAAiB;SACrB,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,0CAAwB,EAC3C,MAAM,EACN;YACE,iBAAiB,EAAE,CAAC,CAAC,iBAAiB,CAAC,iBAAiB;YACxD,WAAW,EAAE,CAAC,CAAC,iBAAiB,CAAC,WAAW;YAC5C,GAAG,aAAa;SACjB,EACD,UAAU,EACV,UAAU,EACV,KAAK,EAEL,OAAO,CACR,CAAC;QAEF,IAAI,uBAAuB,EAAE,CAAC;YAC5B,qFAAqF;YACrF,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC,CAAC;IAEF,OAAO,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC;AACnE,CAAC;AAED,SAAgB,oCAAoC,CAClD,MAAmB,EACnB,UAA4C,EAC5C,kBAAqC,EACrC,UAAmC,EAAE;IAErC,MAAM,eAAe,GAAG,oBAAoB,CAAC,MAAM,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;IAElF,OAAO,0BAA0B,CAC/B,kBAAkB,EAClB,KAAK,EAAE,GAAG,KAA2B,EAA0B,EAAE;QAC/D,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,IAAI,SAAS,EAAE,CAAC;gBACd,KAAK,GAAG,MAAM,SAAS,CAAC,GAAG,KAAK,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC,GAAG,KAAK,CAAC,CAAC;IACnC,CAAC,CACF,CAAC;AACJ,CAAC;AAED,SAAS,0BAA0B,CAAC,QAA2B,EAAE,IAAgB;IAC/E,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,oBAAoB,EAAE,QAAQ,EAAE,CAAC,CAAC;AACjE,CAAC;AAED,SAAS,wBAAwB,CAAC,UAA8B;IAC9D,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,sBAAsB,IAAI,UAAU,CAAC;QAAE,OAAO,IAAI,CAAC;IACxE,OAAO,UAAU,CAAC,oBAAyC,CAAC;AAC9D,CAAC"}