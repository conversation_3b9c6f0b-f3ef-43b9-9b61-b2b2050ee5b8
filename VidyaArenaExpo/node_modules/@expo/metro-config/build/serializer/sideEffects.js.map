{"version": 3, "file": "sideEffects.js", "sourceRoot": "", "sources": ["../../src/serializer/sideEffects.ts"], "names": [], "mappings": ";;;;;AA2BA,kEAsCC;AAmCD,4DA2BC;AAmCD,0CAEC;AAxJD,qFAAkF;AAClF,4CAAoB;AACpB,yCAAsC;AACtC,gDAAwB;AAExB,mEAAgE;AAEhE,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAuB,CAAC;AAQ1E,SAAgB,2BAA2B,CACzC,OAA0B,EAC1B,KAAoB,EACpB,KAAqB,EACrB,cAAwB,CAAC,KAAK,CAAC,IAAI,CAAC,EACpC,UAAuB,IAAI,GAAG,EAAE;IAEhC,MAAM,0BAA0B,GAAG,oBAAoB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACxE,IAAI,0BAA0B,EAAE,CAAC;QAC/B,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAC7B,CAAC;IACD,kEAAkE;IAClE,KAAK,MAAM,YAAY,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;QACvD,IAAI,CAAC,IAAA,2CAAoB,EAAC,YAAY,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC;YAClF,SAAS;QACX,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QACvC,MAAM,GAAG,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,CAAE,CAAC;QAC/D,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,SAAS;QACX,CAAC;QAED,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,2BAA2B,CACxD,OAAO,EACP,KAAK,EACL,GAAG,EACH,CAAC,GAAG,WAAW,EAAE,YAAY,CAAC,YAAY,CAAC,EAC3C,OAAO,CACR,CAAC;QAEF,IAAI,aAAa,EAAE,CAAC;YAClB,2CAA2C;YAC3C,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;YAEzB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IACD,OAAO,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC;AAC1C,CAAC;AAED,MAAM,YAAY,GAAG,IAAI,GAAG,EAAe,CAAC;AAE5C,MAAM,qBAAqB,GAAG,CAC5B,OAEC,EACD,GAAW,EAC8B,EAAE;IAC3C,IAAI,WAAgB,CAAC;IACrB,IAAI,eAAe,GAAkB,IAAI,CAAC;IAC1C,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,UAAU,EAAE,CAAC;QACvD,CAAC,WAAW,EAAE,eAAe,CAAC,GAAG,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;IACrE,CAAC;SAAM,CAAC;QACN,MAAM,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,eAAe,GAAG,IAAA,6CAAqB,EAAC,GAAG,CAAC,CAAC;QAC7C,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAE,CAAC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,iCAAiC;IACjC,MAAM,OAAO,GAAG,cAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAC9C,MAAM,YAAY,GAAG,wBAAwB,CAAC,OAAO,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;IACrF,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;IACpC,OAAO,YAAY,CAAC;AACtB,CAAC,CAAC;AAEF,SAAgB,wBAAwB,CACtC,OAAe,EACf,WAAiD,EACjD,kBAA0B,EAAE;IAE5B,OAAO,CAAC,EAAU,EAAE,EAAE;QACpB,+EAA+E;QAC/E,IAAI,WAAW,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,OAAO,WAAW,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACjD,OAAO,WAAW,CAAC,WAAW,CAAC;QACjC,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE,CAAC;YAClD,MAAM,YAAY,GAAG,cAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAChD,OAAO,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,UAAe,EAAE,EAAE;gBACtD,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;oBACnC,OAAO,IAAA,qBAAS,EAAC,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE;wBAC9D,SAAS,EAAE,IAAI;qBAChB,CAAC,CAAC;gBACL,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,4CAA4C,EAAE,eAAe,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC;QAC9F,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,OAA0B,EAAE,KAAqB;IAC7E,IAAI,KAAK,EAAE,WAAW,KAAK,SAAS,EAAE,CAAC;QACrC,OAAO,KAAK,CAAC,WAAW,CAAC;IAC3B,CAAC;IACD,MAAM,YAAY,GAAG,gCAAgC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACtE,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC;IACjC,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,gCAAgC,CACvC,OAA0B,EAC1B,KAAqB;IAErB,IAAI,KAAK,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;QACpC,OAAO,KAAK,CAAC,WAAW,CAAC;IAC3B,CAAC;IACD,2CAA2C;IAC3C,IAAI,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;QAChC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW,CAAC,EAAE,CAAC;QAC/D,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAEhE,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,eAAe,CAAC,IAAY;IAC1C,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC/B,CAAC"}