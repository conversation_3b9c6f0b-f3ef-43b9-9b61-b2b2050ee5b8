{"version": 3, "file": "hmrJSBundle.js", "sourceRoot": "", "sources": ["../../../src/serializer/fork/hmrJSBundle.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;;;AAIH,iFAA4E;AAC5E,gEAAsC;AACtC,0DAA6B;AAE7B,wDAA2B;AAE3B,6BAA8C;AAU9C,SAAS,eAAe,CACtB,aAAoC,EACpC,KAAyB,EACzB,OAAgB;IAEhB,MAAM,OAAO,GAAgB,EAAE,CAAC;IAEhC,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;QACnC,IAAI,IAAA,eAAU,EAAC,MAAM,CAAC,EAAE,CAAC;YACvB,uDAAuD;YACvD,MAAM,MAAM,GAAG,CAAC,SAA2B,EAAE,EAAE;gBAC7C,MAAM,SAAS,GAAG,kBAAG,CAAC,KAAK,CAAC,kBAAG,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC;gBACjE,yEAAyE;gBACzE,gHAAgH;gBAChH,oHAAoH;gBACpH,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC;gBACtB,SAAS,CAAC,QAAQ,GAAG,mBAAI,CAAC,QAAQ,CAChC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,WAAW,EACzC,mBAAI,CAAC,IAAI,CACP,mBAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EACzB,mBAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,mBAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,SAAS,CACxE,CACF,CAAC;gBAEF,OAAO,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC;gBACrC,OAAO,kBAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC,CAAC;YAEF,MAAM,gBAAgB,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,SAAS,GAAG,sBAAU,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,MAAM,IAAI,GACR,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;gBACrC,0BAA0B,gBAAgB,IAAI;gBAC9C,iBAAiB,SAAS,IAAI,CAAC;YAEjC,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;gBACnD,gBAAgB;gBAChB,SAAS;aACV,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,aAAa,CAAC,MAAmB,EAAE,KAAyB,EAAE,OAAgB;IACrF,MAAM,IAAI,GAAG,IAAA,eAAU,EAAC,MAAM,EAAE;QAC9B,GAAG,OAAO;QACV,SAAS,EAAE,kBAAG,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;QACxC,GAAG,EAAE,IAAI;QACT,YAAY,EAAE,KAAK;QACnB,wBAAwB,EAAE,IAAI;QAC9B,WAAW,EAAE,KAAK;KACnB,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,sBAAsB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACvE,iDAAiD;IACjD,MAAM,uBAAuB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACpD,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,CAAC,IAAY,EAAE,EAAE;QACxD,uBAAuB,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC,GAAG,CACnF,OAAO,CAAC,cAAc,CACvB,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,OAAO,IAAA,+CAAqB,EAAC,IAAI,CAAC,GAAG,EAAE,uBAAuB,CAAC,CAAC;AAClE,CAAC;AAED;;;;;GAKG;AACH,SAAS,sBAAsB,CAC7B,IAAY,EACZ,KAAyB,EACzB,sBAAmD,EAAE;IAErD,gCAAgC;IAChC,IAAI,IAAI,IAAI,mBAAmB,EAAE,CAAC;QAChC,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;IAC/B,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,mBAAmB,EAAE,CAAC;QACjD,mBAAmB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,sBAAsB,CAAC,OAAO,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAC;IAC9D,CAAC;IAED,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAED,SAAS,WAAW,CAClB,KAAuB,EACvB,KAAyB,EACzB,OAAgB;IAMhB,OAAO;QACL,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC;QAC5D,QAAQ,EAAE,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC;QAClE,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;KAChF,CAAC;AACJ,CAAC;AAED,kBAAe,WAAW,CAAC"}