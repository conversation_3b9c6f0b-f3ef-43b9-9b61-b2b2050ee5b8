{"version": 3, "file": "css-modules.js", "sourceRoot": "", "sources": ["../../src/transform-worker/css-modules.ts"], "names": [], "mappings": ";;;;;AAcA,sDAuEC;AAED,sGA4BC;AAED,wCAEC;AAED,4CAQC;AAMD,8CAwEC;AA/MD,mEAA0C;AAG1C,iDAAwD;AAExD,+BAA2C;AAO3C,MAAM,gBAAgB,GAAG,GAAG,CAAC;AAEtB,KAAK,UAAU,qBAAqB,CAAC,KAU3C;IACC,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,cAAc,CAAkC,CAAC;IAE/E,wCAAwC;IACxC,wDAAwD;IAExD,MAAM,UAAU,GAAG,SAAS,CAAC;QAC3B,QAAQ,EAAE,KAAK,CAAC,QAAQ;QACxB,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC5B,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS;QAClC,UAAU,EAAE;YACV,2CAA2C;YAC3C,mDAAmD;YACnD,YAAY,EAAE,KAAK;SACpB;QACD,aAAa,EAAE,IAAI;QACnB,mBAAmB,EAAE,IAAI;QACzB,oBAAoB;QACpB,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,WAAW;QACtC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM;QAC5B,mEAAmE;QACnE,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,gBAAgB;QAExC,OAAO,EAAE,MAAM,IAAA,qCAAsB,EAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;QAEhE,OAAO,EAAE,CAAC,EAAE,UAAU;KACvB,CAAC,CAAC;IAEH,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;IAEjE,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,6CAA6C,CACzF,UAAU,CAAC,OAAQ,CACpB,CAAC;IAEF,IAAI,YAAY,GAAG,gCAAgC,IAAI,CAAC,SAAS,CAC/D,MAAM,CACP,qBAAqB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;IAEvF,MAAM,UAAU,GAAG,iBAAiB,CAClC,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,GAAG,EACT,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,EAC1B,UAAU,CACX,CAAC;IAEF,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QACtB,MAAM,UAAU,GAAG,IAAA,wBAAkB,EAAC;YACpC,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,WAAW;YACtC,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,GAAG,EAAE,UAAU,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,YAAY,IAAI,IAAI,GAAG,UAAU,CAAC;IACpC,CAAC;IAED,OAAO;QACL,MAAM,EAAE,YAAY;QACpB,GAAG,EAAE,UAAU,CAAC,IAAI;QACpB,GAAG,EAAE,UAAU,CAAC,GAAG;QACnB,GAAG,UAAU;KACd,CAAC;AACJ,CAAC;AAED,SAAgB,6CAA6C,CAC3D,KAA8C;IAE9C,MAAM,MAAM,GAA2B,EAAE,CAAC;IAC1C,MAAM,cAAc,GAAwB,EAAE,CAAC;IAC/C,MAAM,SAAS,GAA2B,EAAE,CAAC;IAC7C,uFAAuF;IACvF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QACzC,qBAAqB;QACrB,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;QAE3B,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC1B,SAAS,IAAI,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzE,CAAC;QAED,2CAA2C;QAC3C,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;QAC7B,CAAC;QAED,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;QACxB,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,CAAC;QACrE,OAAO;YACL,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE;SACtD,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,EAAE,CAAC;AAC/C,CAAC;AAED,SAAgB,cAAc,CAAC,QAAgB;IAC7C,OAAO,CAAC,CAAC,uDAAuD,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAClF,CAAC;AAED,SAAgB,gBAAgB,CAAC,QAAgB,EAAE,IAAY,EAAE,QAAoB;IACnF,IAAI,QAAQ,EAAE,CAAC;QACb,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CACV,YAAY,OAAO,CAAC,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO,IAAA,oBAAS,EAAC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CACjJ,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAS,aAAa,CAAC,GAAW;IAChC,OAAO,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC;AAED,SAAgB,iBAAiB,CAC/B,QAAgB,EAChB,YAAoB,EACpB,IAAY,EACZ,UAA6D;IAE7D,MAAM,eAAe,GAAmC,EAAE,CAAC;IAE3D,MAAM,aAAa,GAAuD,EAAE,CAAC;IAC7E,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;QAC5B,KAAK,MAAM,GAAG,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;YAC1C,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC1B,+FAA+F;gBAC/F,IAAI,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC3B,eAAe,CAAC,IAAI,CAAC;wBACnB,GAAG,EAAE,GAAG,CAAC,GAAG;wBACZ,QAAQ,EAAE,GAAG,CAAC,QAAQ;wBACtB,KAAK,EAAE,GAAG,CAAC,KAAK;qBACjB,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,+FAA+F;oBAC/F,aAAa,CAAC,IAAI,CAAC;wBACjB,IAAI,EAAE,GAAG,CAAC,GAAG;wBACb,IAAI,EAAE;4BACJ,SAAS,EAAE,IAAI;4BACf,WAAW,EAAE,KAAK;4BAClB,UAAU,EAAE,KAAK;4BACjB,IAAI,EAAE;gCACJ;oCACE,KAAK,EAAE;wCACL,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;wCACxB,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM;wCAC5B,KAAK,EAAE,CAAC,CAAC;qCACV;oCACD,GAAG,EAAE;wCACH,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;wCACtB,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM;wCAC1B,KAAK,EAAE,CAAC,CAAC;qCACV;oCACD,QAAQ;oCACR,cAAc,EAAE,SAAS;iCAC1B;6BACF;4BACD,GAAG,EAAE;gCACH,GAAG,EAAE,GAAG,CAAC,GAAG;gCACZ,KAAK,EAAE,GAAG,CAAC,KAAK;gCAChB,QAAQ,EAAE,GAAG,CAAC,QAAQ;6BACvB;4BACD,WAAW,EAAE,EAAE;4BACf,GAAG,EAAE,GAAG,CAAC,GAAG;yBACb;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;gBAC9B,kCAAkC;gBAClC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;gBAEjD,MAAM,WAAW,GAAG,eAAe;iBACjC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC;oBACtB,oCAAoC;oBACpC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;gBACjC,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,gFAAgF;oBAChF,OAAO,CAAC,IAAI,CACV,2DAA2D,QAAQ,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,OAAO,IAAA,oBAAS,EAAC,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAC5L,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,EAAE,CAAC;AAChE,CAAC"}