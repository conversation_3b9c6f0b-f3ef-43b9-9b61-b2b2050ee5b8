{"version": 3, "file": "moduleMapper.js", "sourceRoot": "", "sources": ["../../../src/transform-worker/utils/moduleMapper.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAAmC;AACnC,gDAAwB;AAgBxB,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAC5B,8DAA8D,CACzC,CAAC;AAExB,MAAM,sBAAsB,GAAG,CAAC,OAAe,EAAE,MAA6B,EAAE,EAAE,CAChF,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;AACnE,MAAM,iBAAiB,GAAG,sBAAsB,CAAC,aAAa,CAAC,CAAC;AAEhE;;;;;;;;;;;;GAYG;AACH,MAAM,kBAAkB,GAA2B;IACjD,KAAK,EAAE,iBAAiB;IACxB,yBAAyB,EAAE,iBAAiB;IAC5C,aAAa,EAAE,iBAAiB;IAChC,iBAAiB,EAAE,iBAAiB;IACpC,cAAc,EAAE,iBAAiB;IACjC,YAAY,EAAE,iBAAiB;IAC/B,gBAAgB,EAAE,iBAAiB;IACnC,gBAAgB,EAAE,iBAAiB;IACnC,eAAe,EAAE,iBAAiB;IAClC,kBAAkB,EAAE,iBAAiB;IACrC,yBAAyB,EAAE,iBAAiB;IAC5C,wBAAwB,EAAE,iBAAiB;IAC3C,oBAAoB,EAAE,sBAAsB,CAAC,MAAM,CAAC;CACrD,CAAC;AAEF,MAAM,oBAAoB,GAAG,CAAC,UAAkB,EAAE,EAAE,CAClD,UAAU,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACrD,MAAM,mBAAmB,GAAG,CAAC,YAAsB,EAAE,EAAE,CACrD,IAAI,MAAM,CAAC,KAAK,YAAY,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AAE9E,wGAAwG;AACjG,MAAM,kBAAkB,GAAG,GAAG,EAAE;IACrC,yDAAyD;IACzD,MAAM,YAAY,GAAG,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;IAC1E,OAAO,CAAC,OAAe,EAAiB,EAAE;QACxC,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,WAAW,EAAE,CAAC;YAChB,kEAAkE;YAClE,sDAAsD;YACtD,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAW,CAAC,CAAC;YACtE,IAAI,gBAAgB,EAAE,CAAC;gBACrB,oEAAoE;gBACpE,6BAA6B;gBAC7B,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC,CAAC;AAjBW,QAAA,kBAAkB,sBAiB7B;AAEF,mEAAmE;AACnE,MAAM,iBAAiB,GAAG,GAAG,EAAE,CAC7B,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,YAAY,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,CAAC;AAEhF,IAAI,4BAA4B,GAAG,KAAK,CAAC;AAEzC;;;;;;;;;;;;;;GAcG;AACI,MAAM,uBAAuB,GAAG,GAAG,EAAE;IAC1C,IAAI,4BAA4B,EAAE,CAAC;QACjC,OAAO;IACT,CAAC;SAAM,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;QAChC,oEAAoE;QACpE,oDAAoD;QACpD,gEAAgE;QAChE,gEAAgE;QAChE,4DAA4D;QAC5D,4BAA4B;QAC5B,KAAK,CAAC,uDAAuD,CAAC,CAAC;IACjE,CAAC;IACD,4BAA4B,GAAG,IAAI,CAAC;IACpC,MAAM,YAAY,GAAG,IAAA,0BAAkB,GAAE,CAAC;IAE1C,oGAAoG;IACpG,MAAM,MAAM,GACV,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAE,MAAM,CAAC,WAAmB,CAAC,CAAC,CAAC,gBAAa,CAAC;IAE9E,MAAM,uBAAuB,GAAG,MAAM,CAAC,gBAAgB,CAAC;IACxD,IAAI,kBAAkB,GAAG,KAAK,CAAC;IAC/B,MAAM,CAAC,gBAAgB,GAAG,UAAU,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO;QAClE,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxB,IAAI,CAAC;gBACH,kBAAkB,GAAG,IAAI,CAAC;gBAC1B,MAAM,QAAQ,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC;gBAClE,IAAI,QAAQ,EAAE,CAAC;oBACb,uEAAuE;oBACvE,yDAAyD;oBACzD,oEAAoE;oBACpE,eAAe;oBACf,kEAAkE;oBAClE,kEAAkE;oBAClE,uCAAuC;oBACvC,gHAAgH;oBAChH,MAAM,iBAAiB,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;oBAChD,IAAI,iBAAiB,EAAE,CAAC;wBACtB,KAAK,CAAC,uBAAuB,OAAO,SAAS,iBAAiB,GAAG,CAAC,CAAC;wBACnE,OAAO,iBAAiB,CAAC;oBAC3B,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,KAAK,CAAC,+BAA+B,OAAO,MAAM,KAAK,EAAE,CAAC,CAAC;YAC7D,CAAC;oBAAS,CAAC;gBACT,yCAAyC;gBACzC,kBAAkB,GAAG,KAAK,CAAC;YAC7B,CAAC;QACH,CAAC;QACD,OAAO,uBAAuB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAC9E,CAAC,CAAC;AACJ,CAAC,CAAC;AAlDW,QAAA,uBAAuB,2BAkDlC"}