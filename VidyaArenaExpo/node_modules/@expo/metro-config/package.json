{"name": "@expo/metro-config", "version": "54.0.3", "description": "A Metro config for running React Native projects with the Metro bundler", "main": "build/ExpoMetroConfig.js", "types": "build/ExpoMetroConfig.d.ts", "scripts": {"build": "expo-module tsc", "clean": "expo-module clean", "lint": "expo-module lint", "prepare": "expo-module clean && expo-module tsc", "prepublishOnly": "expo-module prepublishOnly", "test": "expo-module test", "typecheck": "expo-module typecheck", "watch": "expo-module tsc --watch --preserveWatchOutput"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/metro-config"}, "keywords": ["expo", "metro"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/metro-config#readme", "files": ["build", "file-store", "babel-transformer"], "dependencies": {"@babel/code-frame": "^7.20.0", "@babel/core": "^7.20.0", "@babel/generator": "^7.20.5", "@expo/config": "~12.0.9", "@expo/metro": "~0.1.1", "@expo/env": "~2.0.7", "@expo/json-file": "~10.0.7", "@expo/spawn-async": "^1.7.2", "browserslist": "^4.25.0", "chalk": "^4.1.0", "debug": "^4.3.2", "dotenv": "~16.4.5", "dotenv-expand": "~11.0.6", "getenv": "^2.0.0", "glob": "^10.4.2", "hermes-parser": "^0.29.1", "jsc-safe-url": "^0.2.4", "lightningcss": "^1.30.1", "minimatch": "^9.0.0", "postcss": "~8.4.32", "resolve-from": "^5.0.0"}, "peerDependencies": {"expo": "*"}, "peerDependenciesMeta": {"expo": {"optional": true}}, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.20", "@types/babel__core": "^7.20.5", "dedent": "^1.5.3", "expo-module-scripts": "^5.0.7", "sass": "^1.60.0"}, "publishConfig": {"access": "public"}, "gitHead": "33f49d0f802b101fd61ac0eedcb1ce35b0adde51"}