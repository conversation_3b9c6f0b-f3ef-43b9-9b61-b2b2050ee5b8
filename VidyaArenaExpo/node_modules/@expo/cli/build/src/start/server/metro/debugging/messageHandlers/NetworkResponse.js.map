{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/messageHandlers/NetworkResponse.ts"], "sourcesContent": ["import type { Protocol } from 'devtools-protocol';\n\nimport { MessageHandler } from '../MessageHandler';\nimport type {\n  CdpMessage,\n  DeviceRequest,\n  DebuggerRequest,\n  DebuggerR<PERSON>ponse,\n  DeviceResponse,\n} from '../types';\n\n/**\n * The global network response storage, as a workaround for the network inspector.\n * @see createDebugMiddleware#createNetworkWebsocket\n */\nexport const NETWORK_RESPONSE_STORAGE = new Map<\n  string,\n  DebuggerResponse<NetworkGetResponseBody>['result']\n>();\n\nexport class NetworkResponseHandler extends MessageHandler {\n  /** All known responses, mapped by request id */\n  storage = NETWORK_RESPONSE_STORAGE;\n\n  handleDeviceMessage(message: DeviceRequest<NetworkReceivedResponseBody>) {\n    if (message.method === 'Expo(Network.receivedResponseBody)') {\n      const { requestId, ...requestInfo } = message.params;\n      this.storage.set(requestId, requestInfo);\n      return true;\n    }\n\n    return false;\n  }\n\n  handleDebuggerMessage(message: DebuggerRequest<NetworkGetResponseBody>) {\n    if (\n      message.method === 'Network.getResponseBody' &&\n      this.storage.has(message.params.requestId)\n    ) {\n      return this.sendToDebugger<DeviceResponse<NetworkGetResponseBody>>({\n        id: message.id,\n        result: this.storage.get(message.params.requestId)!,\n      });\n    }\n\n    return false;\n  }\n}\n\n/** Custom message to transfer the response body data to the proxy */\nexport type NetworkReceivedResponseBody = CdpMessage<\n  'Expo(Network.receivedResponseBody)',\n  Protocol.Network.GetResponseBodyRequest & Protocol.Network.GetResponseBodyResponse,\n  never\n>;\n\n/** @see https://chromedevtools.github.io/devtools-protocol/1-2/Network/#method-getResponseBody */\nexport type NetworkGetResponseBody = CdpMessage<\n  'Network.getResponseBody',\n  Protocol.Network.GetResponseBodyRequest,\n  Protocol.Network.GetResponseBodyResponse\n>;\n"], "names": ["NETWORK_RESPONSE_STORAGE", "NetworkResponseHandler", "Map", "MessageHandler", "handleDeviceMessage", "message", "method", "requestId", "requestInfo", "params", "storage", "set", "handleDebuggerMessage", "has", "sendToDebugger", "id", "result", "get"], "mappings": ";;;;;;;;;;;IAeaA,wBAAwB;eAAxBA;;IAKAC,sBAAsB;eAAtBA;;;gCAlBkB;AAaxB,MAAMD,2BAA2B,IAAIE;AAKrC,MAAMD,+BAA+BE,8BAAc;IAIxDC,oBAAoBC,OAAmD,EAAE;QACvE,IAAIA,QAAQC,MAAM,KAAK,sCAAsC;YAC3D,MAAM,EAAEC,SAAS,EAAE,GAAGC,aAAa,GAAGH,QAAQI,MAAM;YACpD,IAAI,CAACC,OAAO,CAACC,GAAG,CAACJ,WAAWC;YAC5B,OAAO;QACT;QAEA,OAAO;IACT;IAEAI,sBAAsBP,OAAgD,EAAE;QACtE,IACEA,QAAQC,MAAM,KAAK,6BACnB,IAAI,CAACI,OAAO,CAACG,GAAG,CAACR,QAAQI,MAAM,CAACF,SAAS,GACzC;YACA,OAAO,IAAI,CAACO,cAAc,CAAyC;gBACjEC,IAAIV,QAAQU,EAAE;gBACdC,QAAQ,IAAI,CAACN,OAAO,CAACO,GAAG,CAACZ,QAAQI,MAAM,CAACF,SAAS;YACnD;QACF;QAEA,OAAO;IACT;;QA1BK,gBACL,8CAA8C,QAC9CG,UAAUV;;AAyBZ"}