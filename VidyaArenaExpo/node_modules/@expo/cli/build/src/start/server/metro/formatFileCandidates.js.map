{"version": 3, "sources": ["../../../../../src/start/server/metro/formatFileCandidates.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * A fork of my proposed Metro change https://github.com/facebook/metro/pull/1036/files\n */\n\nimport type { FileCandidates } from '@expo/metro/metro-resolver';\nimport path from 'path';\n\nfunction groupExtensions(exts: readonly string[]): string[][] {\n  // Reverse the extensions and split into parts\n  const extParts = exts.map((ext) => ext.split('.').reverse());\n\n  // Find the maximum depth of extension parts\n  const maxDepth = Math.max(...extParts.map((parts) => parts.length));\n\n  // Initialize groups based on the max depth\n  const groups = Array.from({ length: maxDepth }, () => new Set<string>());\n\n  extParts.forEach((parts) => {\n    parts.forEach((part, i) => {\n      // Add parts to the corresponding group based on their depth\n      groups[i].add(part);\n    });\n  });\n\n  // Cycle groups and remove duplicates that appear forwards\n  groups.forEach((group, index) => {\n    // Remove duplicates that appear forwards\n    // NOTE: This doesn't support extensions like `.native.native.js`\n    groups.forEach((otherGroup, otherIndex) => {\n      if (index < otherIndex) {\n        otherGroup.forEach((part) => group.delete(part));\n      }\n    });\n  });\n\n  // Convert sets back to arrays and reverse groups to correct order\n  return groups.map((group) => Array.from(group)).reverse();\n}\n\nfunction createMatcherPatternForExtensions(exts: readonly string[]): string {\n  let formatted = '';\n\n  if (exts.length) {\n    // Apply grouping function\n    const groups = groupExtensions(exts);\n\n    formatted += groups\n      .map((group, index) => {\n        return index < groups.length - 1\n          ? `(${group.map((ext) => `.${ext}`).join('|')})?`\n          : `.(${group.join('|')})`;\n      })\n      .join('');\n  }\n\n  return formatted;\n}\n\nexport function formatFileCandidates(\n  candidates: FileCandidates,\n  allowIndex: boolean = false\n): string {\n  if (candidates.type === 'asset') {\n    return candidates.name;\n  }\n\n  let formatted = candidates.filePathPrefix;\n\n  if (allowIndex) {\n    formatted += `(${path.sep}index)?`;\n  }\n\n  const extensions = candidates.candidateExts\n    // Drop additional dots, the first character if it is a dot, and remove empty strings.\n    .map((ext) => ext.replace(/\\.+/g, '.').replace(/^\\./g, ''))\n    .filter(Boolean);\n\n  formatted += createMatcherPatternForExtensions(extensions);\n\n  return formatted;\n}\n"], "names": ["formatFileCandidates", "groupExtensions", "exts", "extParts", "map", "ext", "split", "reverse", "max<PERSON><PERSON><PERSON>", "Math", "max", "parts", "length", "groups", "Array", "from", "Set", "for<PERSON>ach", "part", "i", "add", "group", "index", "otherGroup", "otherIndex", "delete", "createMatcherPatternForExtensions", "formatted", "join", "candidates", "allowIndex", "type", "name", "filePathPrefix", "path", "sep", "extensions", "candidate<PERSON><PERSON><PERSON>", "replace", "filter", "Boolean"], "mappings": "AAAA;;;;;;;;CAQC;;;;+BAwDeA;;;eAAAA;;;;gEArDC;;;;;;;;;;;AAEjB,SAASC,gBAAgBC,IAAuB;IAC9C,8CAA8C;IAC9C,MAAMC,WAAWD,KAAKE,GAAG,CAAC,CAACC,MAAQA,IAAIC,KAAK,CAAC,KAAKC,OAAO;IAEzD,4CAA4C;IAC5C,MAAMC,WAAWC,KAAKC,GAAG,IAAIP,SAASC,GAAG,CAAC,CAACO,QAAUA,MAAMC,MAAM;IAEjE,2CAA2C;IAC3C,MAAMC,SAASC,MAAMC,IAAI,CAAC;QAAEH,QAAQJ;IAAS,GAAG,IAAM,IAAIQ;IAE1Db,SAASc,OAAO,CAAC,CAACN;QAChBA,MAAMM,OAAO,CAAC,CAACC,MAAMC;YACnB,4DAA4D;YAC5DN,MAAM,CAACM,EAAE,CAACC,GAAG,CAACF;QAChB;IACF;IAEA,0DAA0D;IAC1DL,OAAOI,OAAO,CAAC,CAACI,OAAOC;QACrB,yCAAyC;QACzC,iEAAiE;QACjET,OAAOI,OAAO,CAAC,CAACM,YAAYC;YAC1B,IAAIF,QAAQE,YAAY;gBACtBD,WAAWN,OAAO,CAAC,CAACC,OAASG,MAAMI,MAAM,CAACP;YAC5C;QACF;IACF;IAEA,kEAAkE;IAClE,OAAOL,OAAOT,GAAG,CAAC,CAACiB,QAAUP,MAAMC,IAAI,CAACM,QAAQd,OAAO;AACzD;AAEA,SAASmB,kCAAkCxB,IAAuB;IAChE,IAAIyB,YAAY;IAEhB,IAAIzB,KAAKU,MAAM,EAAE;QACf,0BAA0B;QAC1B,MAAMC,SAASZ,gBAAgBC;QAE/ByB,aAAad,OACVT,GAAG,CAAC,CAACiB,OAAOC;YACX,OAAOA,QAAQT,OAAOD,MAAM,GAAG,IAC3B,CAAC,CAAC,EAAES,MAAMjB,GAAG,CAAC,CAACC,MAAQ,CAAC,CAAC,EAAEA,KAAK,EAAEuB,IAAI,CAAC,KAAK,EAAE,CAAC,GAC/C,CAAC,EAAE,EAAEP,MAAMO,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,GACCA,IAAI,CAAC;IACV;IAEA,OAAOD;AACT;AAEO,SAAS3B,qBACd6B,UAA0B,EAC1BC,aAAsB,KAAK;IAE3B,IAAID,WAAWE,IAAI,KAAK,SAAS;QAC/B,OAAOF,WAAWG,IAAI;IACxB;IAEA,IAAIL,YAAYE,WAAWI,cAAc;IAEzC,IAAIH,YAAY;QACdH,aAAa,CAAC,CAAC,EAAEO,eAAI,CAACC,GAAG,CAAC,OAAO,CAAC;IACpC;IAEA,MAAMC,aAAaP,WAAWQ,aAAa,AACzC,sFAAsF;KACrFjC,GAAG,CAAC,CAACC,MAAQA,IAAIiC,OAAO,CAAC,QAAQ,KAAKA,OAAO,CAAC,QAAQ,KACtDC,MAAM,CAACC;IAEVb,aAAaD,kCAAkCU;IAE/C,OAAOT;AACT"}