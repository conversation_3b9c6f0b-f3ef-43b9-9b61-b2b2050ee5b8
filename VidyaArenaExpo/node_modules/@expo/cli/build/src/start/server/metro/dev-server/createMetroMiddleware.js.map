{"version": 3, "sources": ["../../../../../../src/start/server/metro/dev-server/createMetroMiddleware.ts"], "sourcesContent": ["import type { MetroConfig } from '@expo/metro/metro';\nimport connect from 'connect';\n\nimport { createEventsSocket } from './createEventSocket';\nimport { createMessagesSocket } from './createMessageSocket';\nimport { Log } from '../../../../log';\nimport { openInEditorAsync } from '../../../../utils/editor';\n\nconst compression = require('compression');\n\nexport function createMetroMiddleware(metroConfig: Pick<MetroConfig, 'projectRoot'>) {\n  const messages = createMessagesSocket({ logger: Log });\n  const events = createEventsSocket(messages);\n\n  const middleware = connect()\n    .use(noCacheMiddleware)\n    .use(compression())\n    // Support opening stack frames from clients directly in the editor\n    .use('/open-stack-frame', rawBodyMiddleware)\n    .use('/open-stack-frame', metroOpenStackFrameMiddleware)\n    // Support the symbolication endpoint of Metro\n    // See: https://github.com/facebook/metro/blob/a792d85ffde3c21c3fbf64ac9404ab0afe5ff957/packages/metro/src/Server.js#L1266\n    .use('/symbolicate', rawBodyMiddleware)\n    // Support status check to detect if the packager needs to be started from the native side\n    .use('/status', createMetroStatusMiddleware(metroConfig));\n\n  return {\n    middleware,\n    messagesSocket: messages,\n    eventsSocket: events,\n    websocketEndpoints: {\n      [messages.endpoint]: messages.server,\n      [events.endpoint]: events.server,\n    },\n  };\n}\n\nconst noCacheMiddleware: connect.NextHandleFunction = (req, res, next) => {\n  res.setHeader('Surrogate-Control', 'no-store');\n  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');\n  res.setHeader('Pragma', 'no-cache');\n  res.setHeader('Expires', '0');\n  next();\n};\n\nconst rawBodyMiddleware: connect.NextHandleFunction = (req, _res, next) => {\n  const reqWithBody = req as typeof req & { rawBody: string };\n  reqWithBody.setEncoding('utf8');\n  reqWithBody.rawBody = '';\n  reqWithBody.on('data', (chunk) => (reqWithBody.rawBody += chunk));\n  reqWithBody.on('end', next);\n};\n\nconst metroOpenStackFrameMiddleware: connect.NextHandleFunction = (req, res, next) => {\n  // Only accept POST requests\n  if (req.method !== 'POST') return next();\n  // Only handle requests with a raw body\n  if (!('rawBody' in req) || !req.rawBody) {\n    res.statusCode = 406;\n    return res.end('Open stack frame requires the JSON stack frame as request body');\n  }\n\n  const frame = JSON.parse(req.rawBody as string);\n  openInEditorAsync(frame.file, frame.lineNumber).finally(() => res.end('OK'));\n};\n\nfunction createMetroStatusMiddleware(\n  metroConfig: Pick<MetroConfig, 'projectRoot'>\n): connect.NextHandleFunction {\n  return (_req, res) => {\n    res.setHeader('X-React-Native-Project-Root', encodeURI(metroConfig.projectRoot!));\n    res.end('packager-status:running');\n  };\n}\n"], "names": ["createMetroMiddleware", "compression", "require", "metroConfig", "messages", "createMessagesSocket", "logger", "Log", "events", "createEventsSocket", "middleware", "connect", "use", "noCacheMiddleware", "rawBodyMiddleware", "metroOpenStackFrameMiddleware", "createMetroStatusMiddleware", "messagesSocket", "eventsSocket", "websocketEndpoints", "endpoint", "server", "req", "res", "next", "<PERSON><PERSON><PERSON><PERSON>", "_res", "reqWithBody", "setEncoding", "rawBody", "on", "chunk", "method", "statusCode", "end", "frame", "JSON", "parse", "openInEditorAsync", "file", "lineNumber", "finally", "_req", "encodeURI", "projectRoot"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;;gEATI;;;;;;mCAEe;qCACE;qBACjB;wBACc;;;;;;AAElC,MAAMC,cAAcC,QAAQ;AAErB,SAASF,sBAAsBG,WAA6C;IACjF,MAAMC,WAAWC,IAAAA,yCAAoB,EAAC;QAAEC,QAAQC,QAAG;IAAC;IACpD,MAAMC,SAASC,IAAAA,qCAAkB,EAACL;IAElC,MAAMM,aAAaC,IAAAA,kBAAO,IACvBC,GAAG,CAACC,mBACJD,GAAG,CAACX,cACL,mEAAmE;KAClEW,GAAG,CAAC,qBAAqBE,mBACzBF,GAAG,CAAC,qBAAqBG,8BAC1B,8CAA8C;IAC9C,0HAA0H;KACzHH,GAAG,CAAC,gBAAgBE,kBACrB,0FAA0F;KACzFF,GAAG,CAAC,WAAWI,4BAA4Bb;IAE9C,OAAO;QACLO;QACAO,gBAAgBb;QAChBc,cAAcV;QACdW,oBAAoB;YAClB,CAACf,SAASgB,QAAQ,CAAC,EAAEhB,SAASiB,MAAM;YACpC,CAACb,OAAOY,QAAQ,CAAC,EAAEZ,OAAOa,MAAM;QAClC;IACF;AACF;AAEA,MAAMR,oBAAgD,CAACS,KAAKC,KAAKC;IAC/DD,IAAIE,SAAS,CAAC,qBAAqB;IACnCF,IAAIE,SAAS,CAAC,iBAAiB;IAC/BF,IAAIE,SAAS,CAAC,UAAU;IACxBF,IAAIE,SAAS,CAAC,WAAW;IACzBD;AACF;AAEA,MAAMV,oBAAgD,CAACQ,KAAKI,MAAMF;IAChE,MAAMG,cAAcL;IACpBK,YAAYC,WAAW,CAAC;IACxBD,YAAYE,OAAO,GAAG;IACtBF,YAAYG,EAAE,CAAC,QAAQ,CAACC,QAAWJ,YAAYE,OAAO,IAAIE;IAC1DJ,YAAYG,EAAE,CAAC,OAAON;AACxB;AAEA,MAAMT,gCAA4D,CAACO,KAAKC,KAAKC;IAC3E,4BAA4B;IAC5B,IAAIF,IAAIU,MAAM,KAAK,QAAQ,OAAOR;IAClC,uCAAuC;IACvC,IAAI,CAAE,CAAA,aAAaF,GAAE,KAAM,CAACA,IAAIO,OAAO,EAAE;QACvCN,IAAIU,UAAU,GAAG;QACjB,OAAOV,IAAIW,GAAG,CAAC;IACjB;IAEA,MAAMC,QAAQC,KAAKC,KAAK,CAACf,IAAIO,OAAO;IACpCS,IAAAA,yBAAiB,EAACH,MAAMI,IAAI,EAAEJ,MAAMK,UAAU,EAAEC,OAAO,CAAC,IAAMlB,IAAIW,GAAG,CAAC;AACxE;AAEA,SAASlB,4BACPb,WAA6C;IAE7C,OAAO,CAACuC,MAAMnB;QACZA,IAAIE,SAAS,CAAC,+BAA+BkB,UAAUxC,YAAYyC,WAAW;QAC9ErB,IAAIW,GAAG,CAAC;IACV;AACF"}