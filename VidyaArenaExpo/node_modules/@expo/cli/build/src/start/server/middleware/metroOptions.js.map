{"version": 3, "sources": ["../../../../../src/start/server/middleware/metroOptions.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport type { BundleOptions as MetroBundleOptions } from '@expo/metro/metro/shared/types.flow';\n\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { getRouterDirectoryModuleIdWithManifest } from '../metro/router';\n\nconst debug = require('debug')('expo:metro:options') as typeof console.log;\n\nexport type MetroEnvironment = 'node' | 'react-server' | 'client';\n\nexport type ExpoMetroOptions = {\n  platform: string;\n  mainModuleName: string;\n  mode: string;\n  minify?: boolean;\n  environment?: MetroEnvironment;\n  serializerOutput?: 'static';\n  serializerIncludeMaps?: boolean;\n  lazy?: boolean;\n  engine?: 'hermes';\n  preserveEnvVars?: boolean;\n  bytecode: boolean;\n  /** Enable async routes (route-based bundle splitting) in Expo Router. */\n  asyncRoutes?: boolean;\n  /** Module ID relative to the projectRoot for the Expo Router app directory. */\n  routerRoot: string;\n  /** Enable React compiler support in Babel. */\n  reactCompiler: boolean;\n  baseUrl?: string;\n  isExporting: boolean;\n  /** Is bundling a DOM Component (\"use dom\"). Requires the entry dom component file path. */\n  domRoot?: string;\n  /** Exporting MD5 filename based on file contents, for EAS Update.  */\n  useMd5Filename?: boolean;\n  inlineSourceMap?: boolean;\n  clientBoundaries?: string[];\n  splitChunks?: boolean;\n  usedExports?: boolean;\n  /** Enable optimized bundling (required for tree shaking). */\n  optimize?: boolean;\n\n  modulesOnly?: boolean;\n  runModule?: boolean;\n\n  /** Should assets be exported for hosting. Always true on web. Always false for embedded builds. Optional for native exports. */\n  hosted?: boolean;\n  /** Disable live bindings (enabled by default, required for circular deps) in experimental import export support. */\n  liveBindings?: boolean;\n};\n\n// See: @expo/metro-config/src/serializer/fork/baseJSBundle.ts `ExpoSerializerOptions`\nexport type SerializerOptions = {\n  includeSourceMaps?: boolean;\n  output?: 'static';\n  splitChunks?: boolean;\n  usedExports?: boolean;\n  exporting?: boolean;\n};\n\nexport type ExpoMetroBundleOptions = MetroBundleOptions & {\n  serializerOptions?: SerializerOptions;\n};\n\nexport function isServerEnvironment(environment?: any): boolean {\n  return environment === 'node' || environment === 'react-server';\n}\n\nfunction withDefaults({\n  mode = 'development',\n  minify = mode === 'production',\n  preserveEnvVars = mode !== 'development' && env.EXPO_NO_CLIENT_ENV_VARS,\n  lazy,\n  environment,\n  ...props\n}: ExpoMetroOptions): ExpoMetroOptions {\n  if (props.bytecode) {\n    if (props.platform === 'web') {\n      throw new CommandError('Cannot use bytecode with the web platform');\n    }\n    if (props.engine !== 'hermes') {\n      throw new CommandError('Bytecode is only supported with the Hermes engine');\n    }\n  }\n\n  const optimize =\n    props.optimize ??\n    (environment !== 'node' && mode === 'production' && env.EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH);\n\n  return {\n    mode,\n    minify,\n    preserveEnvVars,\n    optimize,\n    usedExports: optimize && env.EXPO_UNSTABLE_TREE_SHAKING,\n    lazy: !props.isExporting && lazy,\n    environment: environment === 'client' ? undefined : environment,\n    liveBindings: env.EXPO_UNSTABLE_LIVE_BINDINGS,\n    ...props,\n  };\n}\n\nexport function getBaseUrlFromExpoConfig(exp: ExpoConfig) {\n  return exp.experiments?.baseUrl?.trim().replace(/\\/+$/, '') ?? '';\n}\n\nexport function getAsyncRoutesFromExpoConfig(exp: ExpoConfig, mode: string, platform: string) {\n  let asyncRoutesSetting;\n\n  if (exp.extra?.router?.asyncRoutes) {\n    const asyncRoutes = exp.extra?.router?.asyncRoutes;\n    if (['boolean', 'string'].includes(typeof asyncRoutes)) {\n      asyncRoutesSetting = asyncRoutes;\n    } else if (typeof asyncRoutes === 'object') {\n      asyncRoutesSetting = asyncRoutes[platform] ?? asyncRoutes.default;\n    }\n  }\n\n  return [mode, true].includes(asyncRoutesSetting);\n}\n\nexport function getMetroDirectBundleOptionsForExpoConfig(\n  projectRoot: string,\n  exp: ExpoConfig,\n  options: Omit<ExpoMetroOptions, 'baseUrl' | 'reactCompiler' | 'routerRoot' | 'asyncRoutes'>\n): Partial<ExpoMetroBundleOptions> {\n  return getMetroDirectBundleOptions({\n    ...options,\n    reactCompiler: !!exp.experiments?.reactCompiler,\n    baseUrl: getBaseUrlFromExpoConfig(exp),\n    routerRoot: getRouterDirectoryModuleIdWithManifest(projectRoot, exp),\n    asyncRoutes: getAsyncRoutesFromExpoConfig(exp, options.mode, options.platform),\n  });\n}\n\nexport function getMetroDirectBundleOptions(\n  options: ExpoMetroOptions\n): Partial<ExpoMetroBundleOptions> {\n  const {\n    mainModuleName,\n    platform,\n    mode,\n    minify,\n    environment,\n    serializerOutput,\n    serializerIncludeMaps,\n    bytecode,\n    lazy,\n    engine,\n    preserveEnvVars,\n    asyncRoutes,\n    baseUrl,\n    routerRoot,\n    isExporting,\n    inlineSourceMap,\n    splitChunks,\n    usedExports,\n    reactCompiler,\n    optimize,\n    domRoot,\n    clientBoundaries,\n    runModule,\n    modulesOnly,\n    useMd5Filename,\n    hosted,\n    liveBindings,\n  } = withDefaults(options);\n\n  const dev = mode !== 'production';\n  const isHermes = engine === 'hermes';\n\n  if (isExporting) {\n    debug('Disabling lazy bundling for export build');\n    options.lazy = false;\n  }\n\n  let fakeSourceUrl: string | undefined;\n  let fakeSourceMapUrl: string | undefined;\n\n  // TODO: Upstream support to Metro for passing custom serializer options.\n  if (serializerIncludeMaps != null || serializerOutput != null) {\n    fakeSourceUrl = new URL(\n      createBundleUrlPath(options).replace(/^\\//, ''),\n      'http://localhost:8081'\n    ).toString();\n    if (serializerIncludeMaps) {\n      fakeSourceMapUrl = fakeSourceUrl.replace('.bundle?', '.map?');\n    }\n  }\n\n  const customTransformOptions: ExpoMetroBundleOptions['customTransformOptions'] = {\n    __proto__: null,\n    optimize: optimize || undefined,\n    engine,\n    clientBoundaries,\n    preserveEnvVars: preserveEnvVars || undefined,\n    // Use string to match the query param behavior.\n    asyncRoutes: asyncRoutes ? String(asyncRoutes) : undefined,\n    environment,\n    baseUrl: baseUrl || undefined,\n    routerRoot,\n    bytecode: bytecode ? '1' : undefined,\n    reactCompiler: reactCompiler ? String(reactCompiler) : undefined,\n    dom: domRoot,\n    hosted: hosted ? '1' : undefined,\n    useMd5Filename: useMd5Filename || undefined,\n    liveBindings: !liveBindings ? String(liveBindings) : undefined,\n  };\n\n  // Iterate and delete undefined values\n  for (const key in customTransformOptions) {\n    if (customTransformOptions[key] === undefined) {\n      delete customTransformOptions[key];\n    }\n  }\n\n  const bundleOptions: Partial<ExpoMetroBundleOptions> = {\n    platform,\n    entryFile: mainModuleName,\n    dev,\n    minify: minify ?? !dev,\n    inlineSourceMap: inlineSourceMap ?? false,\n    lazy: (!isExporting && lazy) || undefined,\n    unstable_transformProfile: isHermes ? 'hermes-stable' : 'default',\n    customTransformOptions,\n    runModule,\n    modulesOnly,\n    customResolverOptions: {\n      __proto__: null,\n      environment,\n      exporting: isExporting || undefined,\n    },\n    sourceMapUrl: fakeSourceMapUrl,\n    sourceUrl: fakeSourceUrl,\n    serializerOptions: {\n      splitChunks,\n      usedExports: usedExports || undefined,\n      output: serializerOutput,\n      includeSourceMaps: serializerIncludeMaps,\n      exporting: isExporting || undefined,\n    },\n  };\n\n  return bundleOptions;\n}\n\nexport function createBundleUrlPathFromExpoConfig(\n  projectRoot: string,\n  exp: ExpoConfig,\n  options: Omit<ExpoMetroOptions, 'reactCompiler' | 'baseUrl' | 'routerRoot'>\n): string {\n  return createBundleUrlPath({\n    ...options,\n    reactCompiler: !!exp.experiments?.reactCompiler,\n    baseUrl: getBaseUrlFromExpoConfig(exp),\n    routerRoot: getRouterDirectoryModuleIdWithManifest(projectRoot, exp),\n  });\n}\n\nexport function createBundleUrlPath(options: ExpoMetroOptions): string {\n  const queryParams = createBundleUrlSearchParams(options);\n  return `/${encodeURI(options.mainModuleName.replace(/^\\/+/, ''))}.bundle?${queryParams.toString()}`;\n}\n\n/**\n * Create a bundle URL, containing all required query parameters, using a valid \"os path\".\n * On POSIX systems, this would look something like `/Users/<USER>/project/file.js?dev=false&..`.\n * On UNIX systems, this would look something like `C:\\Users\\<USER>\\project\\file.js?dev=false&..`.\n * This path can safely be used with `path.*` modifiers and resolved.\n */\nexport function createBundleOsPath(options: ExpoMetroOptions): string {\n  const queryParams = createBundleUrlSearchParams(options);\n  const mainModuleName = toPosixPath(options.mainModuleName);\n  return `${mainModuleName}.bundle?${queryParams.toString()}`;\n}\n\nexport function createBundleUrlSearchParams(options: ExpoMetroOptions): URLSearchParams {\n  const {\n    platform,\n    mode,\n    minify,\n    environment,\n    serializerOutput,\n    serializerIncludeMaps,\n    lazy,\n    bytecode,\n    engine,\n    preserveEnvVars,\n    asyncRoutes,\n    baseUrl,\n    routerRoot,\n    reactCompiler,\n    inlineSourceMap,\n    isExporting,\n    clientBoundaries,\n    splitChunks,\n    usedExports,\n    optimize,\n    domRoot,\n    modulesOnly,\n    runModule,\n    hosted,\n    liveBindings,\n  } = withDefaults(options);\n\n  const dev = String(mode !== 'production');\n  const queryParams = new URLSearchParams({\n    platform: encodeURIComponent(platform),\n    dev,\n    // TODO: Is this still needed?\n    hot: String(false),\n  });\n\n  // Lazy bundling must be disabled for bundle splitting to work.\n  if (!isExporting && lazy) {\n    queryParams.append('lazy', String(lazy));\n  }\n\n  if (inlineSourceMap) {\n    queryParams.append('inlineSourceMap', String(inlineSourceMap));\n  }\n\n  if (minify) {\n    queryParams.append('minify', String(minify));\n  }\n\n  // We split bytecode from the engine since you could technically use Hermes without bytecode.\n  // Hermes indicates the type of language features you want to transform out of the JS, whereas bytecode\n  // indicates whether you want to use the Hermes bytecode format.\n  if (engine) {\n    queryParams.append('transform.engine', engine);\n  }\n  if (bytecode) {\n    queryParams.append('transform.bytecode', '1');\n  }\n  if (asyncRoutes) {\n    queryParams.append('transform.asyncRoutes', String(asyncRoutes));\n  }\n  if (preserveEnvVars) {\n    queryParams.append('transform.preserveEnvVars', String(preserveEnvVars));\n  }\n  if (baseUrl) {\n    queryParams.append('transform.baseUrl', baseUrl);\n  }\n  if (clientBoundaries?.length) {\n    queryParams.append('transform.clientBoundaries', JSON.stringify(clientBoundaries));\n  }\n  if (routerRoot != null) {\n    queryParams.append('transform.routerRoot', routerRoot);\n  }\n  if (reactCompiler) {\n    queryParams.append('transform.reactCompiler', String(reactCompiler));\n  }\n  if (domRoot) {\n    queryParams.append('transform.dom', domRoot);\n  }\n  if (hosted) {\n    queryParams.append('transform.hosted', '1');\n  }\n\n  if (environment) {\n    queryParams.append('resolver.environment', environment);\n    queryParams.append('transform.environment', environment);\n  }\n\n  if (isExporting) {\n    queryParams.append('resolver.exporting', String(isExporting));\n  }\n\n  if (splitChunks) {\n    queryParams.append('serializer.splitChunks', String(splitChunks));\n  }\n  if (usedExports) {\n    queryParams.append('serializer.usedExports', String(usedExports));\n  }\n  if (optimize) {\n    queryParams.append('transform.optimize', String(optimize));\n  }\n  if (serializerOutput) {\n    queryParams.append('serializer.output', serializerOutput);\n  }\n  if (serializerIncludeMaps) {\n    queryParams.append('serializer.map', String(serializerIncludeMaps));\n  }\n  if (engine === 'hermes') {\n    queryParams.append('unstable_transformProfile', 'hermes-stable');\n  }\n\n  if (modulesOnly != null) {\n    queryParams.set('modulesOnly', String(modulesOnly));\n  }\n  if (runModule != null) {\n    queryParams.set('runModule', String(runModule));\n  }\n\n  if (liveBindings === false) {\n    queryParams.append('transform.liveBindings', String(false));\n  }\n\n  return queryParams;\n}\n\n/**\n * Convert all path separators to `/`, including on Windows.\n * Metro asumes that all module specifiers are posix paths.\n * References to directories can still be Windows-style paths in Metro.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Modules#importing_features_into_your_script\n * @see https://github.com/facebook/metro/pull/1286\n */\nexport function convertPathToModuleSpecifier(pathLike: string) {\n  return toPosixPath(pathLike);\n}\n\nexport function getMetroOptionsFromUrl(urlFragment: string) {\n  const url = new URL(urlFragment, 'http://localhost:0');\n  const getStringParam = (key: string) => {\n    const param = url.searchParams.get(key);\n    if (Array.isArray(param)) {\n      throw new Error(`Expected single value for ${key}`);\n    }\n    return param;\n  };\n\n  let pathname = url.pathname;\n  if (pathname.endsWith('.bundle')) {\n    pathname = pathname.slice(0, -'.bundle'.length);\n  }\n\n  const options: ExpoMetroOptions = {\n    mode: isTruthy(getStringParam('dev') ?? 'true') ? 'development' : 'production',\n    minify: isTruthy(getStringParam('minify') ?? 'false'),\n    lazy: isTruthy(getStringParam('lazy') ?? 'false'),\n    routerRoot: getStringParam('transform.routerRoot') ?? 'app',\n    hosted: isTruthy(getStringParam('transform.hosted')),\n    isExporting: isTruthy(getStringParam('resolver.exporting') ?? 'false'),\n    environment: assertEnvironment(getStringParam('transform.environment') ?? 'node'),\n    platform: url.searchParams.get('platform') ?? 'web',\n    bytecode: isTruthy(getStringParam('transform.bytecode') ?? 'false'),\n    mainModuleName: convertPathToModuleSpecifier(pathname),\n    reactCompiler: isTruthy(getStringParam('transform.reactCompiler') ?? 'false'),\n    asyncRoutes: isTruthy(getStringParam('transform.asyncRoutes') ?? 'false'),\n    baseUrl: getStringParam('transform.baseUrl') ?? undefined,\n    // clientBoundaries: JSON.parse(getStringParam('transform.clientBoundaries') ?? '[]'),\n    engine: assertEngine(getStringParam('transform.engine')),\n    runModule: isTruthy(getStringParam('runModule') ?? 'true'),\n    modulesOnly: isTruthy(getStringParam('modulesOnly') ?? 'false'),\n    liveBindings: isTruthy(getStringParam('transform.liveBindings') ?? 'true'),\n  };\n\n  return options;\n}\n\nfunction isTruthy(value: string | null): boolean {\n  return value === 'true' || value === '1';\n}\n\nfunction assertEnvironment(environment: string | undefined): MetroEnvironment | undefined {\n  if (!environment) {\n    return undefined;\n  }\n  if (!['node', 'react-server', 'client'].includes(environment)) {\n    throw new Error(`Expected transform.environment to be one of: node, react-server, client`);\n  }\n  return environment as MetroEnvironment;\n}\nfunction assertEngine(engine: string | undefined | null): 'hermes' | undefined {\n  if (!engine) {\n    return undefined;\n  }\n  if (!['hermes'].includes(engine)) {\n    throw new Error(`Expected transform.engine to be one of: hermes`);\n  }\n  return engine as 'hermes';\n}\n"], "names": ["convertPathToModuleSpecifier", "createBundleOsPath", "createBundleUrlPath", "createBundleUrlPathFromExpoConfig", "createBundleUrlSearchParams", "getAsyncRoutesFromExpoConfig", "getBaseUrlFromExpoConfig", "getMetroDirectBundleOptions", "getMetroDirectBundleOptionsForExpoConfig", "getMetroOptionsFromUrl", "isServerEnvironment", "debug", "require", "environment", "with<PERSON><PERSON><PERSON><PERSON>", "mode", "minify", "preserveEnvVars", "env", "EXPO_NO_CLIENT_ENV_VARS", "lazy", "props", "bytecode", "platform", "CommandError", "engine", "optimize", "EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH", "usedExports", "EXPO_UNSTABLE_TREE_SHAKING", "isExporting", "undefined", "liveBindings", "EXPO_UNSTABLE_LIVE_BINDINGS", "exp", "experiments", "baseUrl", "trim", "replace", "asyncRoutesSetting", "extra", "router", "asyncRoutes", "includes", "default", "projectRoot", "options", "reactCompiler", "routerRoot", "getRouterDirectoryModuleIdWithManifest", "mainModuleName", "serializerOutput", "serializerIncludeMaps", "inlineSourceMap", "splitChunks", "domRoot", "clientBoundaries", "runModule", "modulesOnly", "useMd5Filename", "hosted", "dev", "isHermes", "fakeSourceUrl", "fakeSourceMapUrl", "URL", "toString", "customTransformOptions", "__proto__", "String", "dom", "key", "bundleOptions", "entryFile", "unstable_transformProfile", "customResolverOptions", "exporting", "sourceMapUrl", "sourceUrl", "serializerOptions", "output", "includeSourceMaps", "queryParams", "encodeURI", "toPosixPath", "URLSearchParams", "encodeURIComponent", "hot", "append", "length", "JSON", "stringify", "set", "pathLike", "urlFragment", "url", "getStringParam", "param", "searchParams", "get", "Array", "isArray", "Error", "pathname", "endsWith", "slice", "<PERSON><PERSON><PERSON><PERSON>", "assertEnvironment", "assertEngine", "value"], "mappings": ";;;;;;;;;;;IA2ZgBA,4BAA4B;eAA5BA;;IA5IAC,kBAAkB;eAAlBA;;IAXAC,mBAAmB;eAAnBA;;IAbAC,iCAAiC;eAAjCA;;IA8BAC,2BAA2B;eAA3BA;;IA1KAC,4BAA4B;eAA5BA;;IAJAC,wBAAwB;eAAxBA;;IAiCAC,2BAA2B;eAA3BA;;IAdAC,wCAAwC;eAAxCA;;IAqSAC,sBAAsB;eAAtBA;;IA9VAC,mBAAmB;eAAnBA;;;qBA9DI;wBACS;0BACD;wBAC2B;AAEvD,MAAMC,QAAQC,QAAQ,SAAS;AAyDxB,SAASF,oBAAoBG,WAAiB;IACnD,OAAOA,gBAAgB,UAAUA,gBAAgB;AACnD;AAEA,SAASC,aAAa,EACpBC,OAAO,aAAa,EACpBC,SAASD,SAAS,YAAY,EAC9BE,kBAAkBF,SAAS,iBAAiBG,QAAG,CAACC,uBAAuB,EACvEC,IAAI,EACJP,WAAW,EACX,GAAGQ,OACc;IACjB,IAAIA,MAAMC,QAAQ,EAAE;QAClB,IAAID,MAAME,QAAQ,KAAK,OAAO;YAC5B,MAAM,IAAIC,oBAAY,CAAC;QACzB;QACA,IAAIH,MAAMI,MAAM,KAAK,UAAU;YAC7B,MAAM,IAAID,oBAAY,CAAC;QACzB;IACF;IAEA,MAAME,WACJL,MAAMK,QAAQ,IACbb,CAAAA,gBAAgB,UAAUE,SAAS,gBAAgBG,QAAG,CAACS,kCAAkC,AAAD;IAE3F,OAAO;QACLZ;QACAC;QACAC;QACAS;QACAE,aAAaF,YAAYR,QAAG,CAACW,0BAA0B;QACvDT,MAAM,CAACC,MAAMS,WAAW,IAAIV;QAC5BP,aAAaA,gBAAgB,WAAWkB,YAAYlB;QACpDmB,cAAcd,QAAG,CAACe,2BAA2B;QAC7C,GAAGZ,KAAK;IACV;AACF;AAEO,SAASf,yBAAyB4B,GAAe;QAC/CA,0BAAAA;IAAP,OAAOA,EAAAA,mBAAAA,IAAIC,WAAW,sBAAfD,2BAAAA,iBAAiBE,OAAO,qBAAxBF,yBAA0BG,IAAI,GAAGC,OAAO,CAAC,QAAQ,QAAO;AACjE;AAEO,SAASjC,6BAA6B6B,GAAe,EAAEnB,IAAY,EAAEQ,QAAgB;QAGtFW,mBAAAA;IAFJ,IAAIK;IAEJ,KAAIL,aAAAA,IAAIM,KAAK,sBAATN,oBAAAA,WAAWO,MAAM,qBAAjBP,kBAAmBQ,WAAW,EAAE;YACdR,oBAAAA;QAApB,MAAMQ,eAAcR,cAAAA,IAAIM,KAAK,sBAATN,qBAAAA,YAAWO,MAAM,qBAAjBP,mBAAmBQ,WAAW;QAClD,IAAI;YAAC;YAAW;SAAS,CAACC,QAAQ,CAAC,OAAOD,cAAc;YACtDH,qBAAqBG;QACvB,OAAO,IAAI,OAAOA,gBAAgB,UAAU;YAC1CH,qBAAqBG,WAAW,CAACnB,SAAS,IAAImB,YAAYE,OAAO;QACnE;IACF;IAEA,OAAO;QAAC7B;QAAM;KAAK,CAAC4B,QAAQ,CAACJ;AAC/B;AAEO,SAAS/B,yCACdqC,WAAmB,EACnBX,GAAe,EACfY,OAA2F;QAIxEZ;IAFnB,OAAO3B,4BAA4B;QACjC,GAAGuC,OAAO;QACVC,eAAe,CAAC,GAACb,mBAAAA,IAAIC,WAAW,qBAAfD,iBAAiBa,aAAa;QAC/CX,SAAS9B,yBAAyB4B;QAClCc,YAAYC,IAAAA,8CAAsC,EAACJ,aAAaX;QAChEQ,aAAarC,6BAA6B6B,KAAKY,QAAQ/B,IAAI,EAAE+B,QAAQvB,QAAQ;IAC/E;AACF;AAEO,SAAShB,4BACduC,OAAyB;IAEzB,MAAM,EACJI,cAAc,EACd3B,QAAQ,EACRR,IAAI,EACJC,MAAM,EACNH,WAAW,EACXsC,gBAAgB,EAChBC,qBAAqB,EACrB9B,QAAQ,EACRF,IAAI,EACJK,MAAM,EACNR,eAAe,EACfyB,WAAW,EACXN,OAAO,EACPY,UAAU,EACVlB,WAAW,EACXuB,eAAe,EACfC,WAAW,EACX1B,WAAW,EACXmB,aAAa,EACbrB,QAAQ,EACR6B,OAAO,EACPC,gBAAgB,EAChBC,SAAS,EACTC,WAAW,EACXC,cAAc,EACdC,MAAM,EACN5B,YAAY,EACb,GAAGlB,aAAagC;IAEjB,MAAMe,MAAM9C,SAAS;IACrB,MAAM+C,WAAWrC,WAAW;IAE5B,IAAIK,aAAa;QACfnB,MAAM;QACNmC,QAAQ1B,IAAI,GAAG;IACjB;IAEA,IAAI2C;IACJ,IAAIC;IAEJ,yEAAyE;IACzE,IAAIZ,yBAAyB,QAAQD,oBAAoB,MAAM;QAC7DY,gBAAgB,IAAIE,IAClB/D,oBAAoB4C,SAASR,OAAO,CAAC,OAAO,KAC5C,yBACA4B,QAAQ;QACV,IAAId,uBAAuB;YACzBY,mBAAmBD,cAAczB,OAAO,CAAC,YAAY;QACvD;IACF;IAEA,MAAM6B,yBAA2E;QAC/EC,WAAW;QACX1C,UAAUA,YAAYK;QACtBN;QACA+B;QACAvC,iBAAiBA,mBAAmBc;QACpC,gDAAgD;QAChDW,aAAaA,cAAc2B,OAAO3B,eAAeX;QACjDlB;QACAuB,SAASA,WAAWL;QACpBiB;QACA1B,UAAUA,WAAW,MAAMS;QAC3BgB,eAAeA,gBAAgBsB,OAAOtB,iBAAiBhB;QACvDuC,KAAKf;QACLK,QAAQA,SAAS,MAAM7B;QACvB4B,gBAAgBA,kBAAkB5B;QAClCC,cAAc,CAACA,eAAeqC,OAAOrC,gBAAgBD;IACvD;IAEA,sCAAsC;IACtC,IAAK,MAAMwC,OAAOJ,uBAAwB;QACxC,IAAIA,sBAAsB,CAACI,IAAI,KAAKxC,WAAW;YAC7C,OAAOoC,sBAAsB,CAACI,IAAI;QACpC;IACF;IAEA,MAAMC,gBAAiD;QACrDjD;QACAkD,WAAWvB;QACXW;QACA7C,QAAQA,UAAU,CAAC6C;QACnBR,iBAAiBA,mBAAmB;QACpCjC,MAAM,AAAC,CAACU,eAAeV,QAASW;QAChC2C,2BAA2BZ,WAAW,kBAAkB;QACxDK;QACAV;QACAC;QACAiB,uBAAuB;YACrBP,WAAW;YACXvD;YACA+D,WAAW9C,eAAeC;QAC5B;QACA8C,cAAcb;QACdc,WAAWf;QACXgB,mBAAmB;YACjBzB;YACA1B,aAAaA,eAAeG;YAC5BiD,QAAQ7B;YACR8B,mBAAmB7B;YACnBwB,WAAW9C,eAAeC;QAC5B;IACF;IAEA,OAAOyC;AACT;AAEO,SAASrE,kCACd0C,WAAmB,EACnBX,GAAe,EACfY,OAA2E;QAIxDZ;IAFnB,OAAOhC,oBAAoB;QACzB,GAAG4C,OAAO;QACVC,eAAe,CAAC,GAACb,mBAAAA,IAAIC,WAAW,qBAAfD,iBAAiBa,aAAa;QAC/CX,SAAS9B,yBAAyB4B;QAClCc,YAAYC,IAAAA,8CAAsC,EAACJ,aAAaX;IAClE;AACF;AAEO,SAAShC,oBAAoB4C,OAAyB;IAC3D,MAAMoC,cAAc9E,4BAA4B0C;IAChD,OAAO,CAAC,CAAC,EAAEqC,UAAUrC,QAAQI,cAAc,CAACZ,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE4C,YAAYhB,QAAQ,IAAI;AACrG;AAQO,SAASjE,mBAAmB6C,OAAyB;IAC1D,MAAMoC,cAAc9E,4BAA4B0C;IAChD,MAAMI,iBAAiBkC,IAAAA,qBAAW,EAACtC,QAAQI,cAAc;IACzD,OAAO,GAAGA,eAAe,QAAQ,EAAEgC,YAAYhB,QAAQ,IAAI;AAC7D;AAEO,SAAS9D,4BAA4B0C,OAAyB;IACnE,MAAM,EACJvB,QAAQ,EACRR,IAAI,EACJC,MAAM,EACNH,WAAW,EACXsC,gBAAgB,EAChBC,qBAAqB,EACrBhC,IAAI,EACJE,QAAQ,EACRG,MAAM,EACNR,eAAe,EACfyB,WAAW,EACXN,OAAO,EACPY,UAAU,EACVD,aAAa,EACbM,eAAe,EACfvB,WAAW,EACX0B,gBAAgB,EAChBF,WAAW,EACX1B,WAAW,EACXF,QAAQ,EACR6B,OAAO,EACPG,WAAW,EACXD,SAAS,EACTG,MAAM,EACN5B,YAAY,EACb,GAAGlB,aAAagC;IAEjB,MAAMe,MAAMQ,OAAOtD,SAAS;IAC5B,MAAMmE,cAAc,IAAIG,gBAAgB;QACtC9D,UAAU+D,mBAAmB/D;QAC7BsC;QACA,8BAA8B;QAC9B0B,KAAKlB,OAAO;IACd;IAEA,+DAA+D;IAC/D,IAAI,CAACvC,eAAeV,MAAM;QACxB8D,YAAYM,MAAM,CAAC,QAAQnB,OAAOjD;IACpC;IAEA,IAAIiC,iBAAiB;QACnB6B,YAAYM,MAAM,CAAC,mBAAmBnB,OAAOhB;IAC/C;IAEA,IAAIrC,QAAQ;QACVkE,YAAYM,MAAM,CAAC,UAAUnB,OAAOrD;IACtC;IAEA,6FAA6F;IAC7F,uGAAuG;IACvG,gEAAgE;IAChE,IAAIS,QAAQ;QACVyD,YAAYM,MAAM,CAAC,oBAAoB/D;IACzC;IACA,IAAIH,UAAU;QACZ4D,YAAYM,MAAM,CAAC,sBAAsB;IAC3C;IACA,IAAI9C,aAAa;QACfwC,YAAYM,MAAM,CAAC,yBAAyBnB,OAAO3B;IACrD;IACA,IAAIzB,iBAAiB;QACnBiE,YAAYM,MAAM,CAAC,6BAA6BnB,OAAOpD;IACzD;IACA,IAAImB,SAAS;QACX8C,YAAYM,MAAM,CAAC,qBAAqBpD;IAC1C;IACA,IAAIoB,oCAAAA,iBAAkBiC,MAAM,EAAE;QAC5BP,YAAYM,MAAM,CAAC,8BAA8BE,KAAKC,SAAS,CAACnC;IAClE;IACA,IAAIR,cAAc,MAAM;QACtBkC,YAAYM,MAAM,CAAC,wBAAwBxC;IAC7C;IACA,IAAID,eAAe;QACjBmC,YAAYM,MAAM,CAAC,2BAA2BnB,OAAOtB;IACvD;IACA,IAAIQ,SAAS;QACX2B,YAAYM,MAAM,CAAC,iBAAiBjC;IACtC;IACA,IAAIK,QAAQ;QACVsB,YAAYM,MAAM,CAAC,oBAAoB;IACzC;IAEA,IAAI3E,aAAa;QACfqE,YAAYM,MAAM,CAAC,wBAAwB3E;QAC3CqE,YAAYM,MAAM,CAAC,yBAAyB3E;IAC9C;IAEA,IAAIiB,aAAa;QACfoD,YAAYM,MAAM,CAAC,sBAAsBnB,OAAOvC;IAClD;IAEA,IAAIwB,aAAa;QACf4B,YAAYM,MAAM,CAAC,0BAA0BnB,OAAOf;IACtD;IACA,IAAI1B,aAAa;QACfsD,YAAYM,MAAM,CAAC,0BAA0BnB,OAAOzC;IACtD;IACA,IAAIF,UAAU;QACZwD,YAAYM,MAAM,CAAC,sBAAsBnB,OAAO3C;IAClD;IACA,IAAIyB,kBAAkB;QACpB+B,YAAYM,MAAM,CAAC,qBAAqBrC;IAC1C;IACA,IAAIC,uBAAuB;QACzB8B,YAAYM,MAAM,CAAC,kBAAkBnB,OAAOjB;IAC9C;IACA,IAAI3B,WAAW,UAAU;QACvByD,YAAYM,MAAM,CAAC,6BAA6B;IAClD;IAEA,IAAI9B,eAAe,MAAM;QACvBwB,YAAYU,GAAG,CAAC,eAAevB,OAAOX;IACxC;IACA,IAAID,aAAa,MAAM;QACrByB,YAAYU,GAAG,CAAC,aAAavB,OAAOZ;IACtC;IAEA,IAAIzB,iBAAiB,OAAO;QAC1BkD,YAAYM,MAAM,CAAC,0BAA0BnB,OAAO;IACtD;IAEA,OAAOa;AACT;AAUO,SAASlF,6BAA6B6F,QAAgB;IAC3D,OAAOT,IAAAA,qBAAW,EAACS;AACrB;AAEO,SAASpF,uBAAuBqF,WAAmB;IACxD,MAAMC,MAAM,IAAI9B,IAAI6B,aAAa;IACjC,MAAME,iBAAiB,CAACzB;QACtB,MAAM0B,QAAQF,IAAIG,YAAY,CAACC,GAAG,CAAC5B;QACnC,IAAI6B,MAAMC,OAAO,CAACJ,QAAQ;YACxB,MAAM,IAAIK,MAAM,CAAC,0BAA0B,EAAE/B,KAAK;QACpD;QACA,OAAO0B;IACT;IAEA,IAAIM,WAAWR,IAAIQ,QAAQ;IAC3B,IAAIA,SAASC,QAAQ,CAAC,YAAY;QAChCD,WAAWA,SAASE,KAAK,CAAC,GAAG,CAAC,UAAUhB,MAAM;IAChD;IAEA,MAAM3C,UAA4B;QAChC/B,MAAM2F,SAASV,eAAe,UAAU,UAAU,gBAAgB;QAClEhF,QAAQ0F,SAASV,eAAe,aAAa;QAC7C5E,MAAMsF,SAASV,eAAe,WAAW;QACzChD,YAAYgD,eAAe,2BAA2B;QACtDpC,QAAQ8C,SAASV,eAAe;QAChClE,aAAa4E,SAASV,eAAe,yBAAyB;QAC9DnF,aAAa8F,kBAAkBX,eAAe,4BAA4B;QAC1EzE,UAAUwE,IAAIG,YAAY,CAACC,GAAG,CAAC,eAAe;QAC9C7E,UAAUoF,SAASV,eAAe,yBAAyB;QAC3D9C,gBAAgBlD,6BAA6BuG;QAC7CxD,eAAe2D,SAASV,eAAe,8BAA8B;QACrEtD,aAAagE,SAASV,eAAe,4BAA4B;QACjE5D,SAAS4D,eAAe,wBAAwBjE;QAChD,sFAAsF;QACtFN,QAAQmF,aAAaZ,eAAe;QACpCvC,WAAWiD,SAASV,eAAe,gBAAgB;QACnDtC,aAAagD,SAASV,eAAe,kBAAkB;QACvDhE,cAAc0E,SAASV,eAAe,6BAA6B;IACrE;IAEA,OAAOlD;AACT;AAEA,SAAS4D,SAASG,KAAoB;IACpC,OAAOA,UAAU,UAAUA,UAAU;AACvC;AAEA,SAASF,kBAAkB9F,WAA+B;IACxD,IAAI,CAACA,aAAa;QAChB,OAAOkB;IACT;IACA,IAAI,CAAC;QAAC;QAAQ;QAAgB;KAAS,CAACY,QAAQ,CAAC9B,cAAc;QAC7D,MAAM,IAAIyF,MAAM,CAAC,uEAAuE,CAAC;IAC3F;IACA,OAAOzF;AACT;AACA,SAAS+F,aAAanF,MAAiC;IACrD,IAAI,CAACA,QAAQ;QACX,OAAOM;IACT;IACA,IAAI,CAAC;QAAC;KAAS,CAACY,QAAQ,CAAClB,SAAS;QAChC,MAAM,IAAI6E,MAAM,CAAC,8CAA8C,CAAC;IAClE;IACA,OAAO7E;AACT"}