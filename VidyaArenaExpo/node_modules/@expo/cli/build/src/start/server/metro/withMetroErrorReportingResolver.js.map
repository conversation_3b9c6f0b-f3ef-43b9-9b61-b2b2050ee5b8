{"version": 3, "sources": ["../../../../../src/start/server/metro/withMetroErrorReportingResolver.ts"], "sourcesContent": ["import type { ConfigT as MetroConfig } from '@expo/metro/metro-config';\nimport type { ResolutionContext } from '@expo/metro/metro-resolver';\nimport chalk from 'chalk';\nimport path from 'path';\nimport { stripVTControlCharacters } from 'util';\n\nimport type { ExpoCustomMetroResolver } from './withMetroResolvers';\nimport { isPathInside } from '../../../utils/dir';\nimport { env } from '../../../utils/env';\n\nconst debug = require('debug')('expo:metro:withMetroResolvers') as typeof console.log;\n\n// TODO: Do we need to expose this?\nconst STACK_DEPTH_LIMIT = 35;\nconst STACK_COUNT_LIMIT = 2_000;\n\nexport function withMetroErrorReportingResolver(config: MetroConfig): MetroConfig {\n  if (!env.EXPO_METRO_UNSTABLE_ERRORS) {\n    return config;\n  }\n\n  const originalResolveRequest = config.resolver?.resolveRequest;\n\n  const depGraph: DepGraph = new Map();\n\n  const mutateResolutionError = createMutateResolutionError(config, depGraph);\n\n  return {\n    ...config,\n    resolver: {\n      ...config.resolver,\n      resolveRequest(context, moduleName, platform) {\n        const storeResult = (res: NonNullable<ReturnType<ExpoCustomMetroResolver>>) => {\n          const inputPlatform = platform ?? 'null';\n\n          const key = optionsKeyForContext(context);\n          if (!depGraph.has(key)) depGraph.set(key, new Map());\n          const mapByTarget = depGraph.get(key);\n          if (!mapByTarget!.has(inputPlatform)) mapByTarget!.set(inputPlatform, new Map());\n          const mapByPlatform = mapByTarget!.get(inputPlatform);\n          if (!mapByPlatform!.has(context.originModulePath))\n            mapByPlatform!.set(context.originModulePath, new Set());\n          const setForModule = mapByPlatform!.get(context.originModulePath)!;\n\n          const qualifiedModuleName = res?.type === 'sourceFile' ? res.filePath : moduleName;\n          setForModule.add({ path: qualifiedModuleName, request: moduleName });\n        };\n\n        // If the user defined a resolver, run it first and depend on the documented\n        // chaining logic: https://facebook.github.io/metro/docs/resolution/#resolution-algorithm\n        //\n        // config.resolver.resolveRequest = (context, moduleName, platform) => {\n        //\n        //  // Do work...\n        //\n        //  return context.resolveRequest(context, moduleName, platform);\n        // };\n        try {\n          const firstResolver = originalResolveRequest ?? context.resolveRequest;\n          const res = firstResolver(context, moduleName, platform);\n          storeResult(res);\n          return res;\n        } catch (error: any) {\n          throw mutateResolutionError(error, context, moduleName, platform);\n        }\n      },\n    },\n  };\n}\n\nexport type DepGraph = Map<\n  // custom options\n  string,\n  Map<\n    // platform\n    string,\n    Map<\n      // origin module name\n      string,\n      Set<{\n        // required module name\n        path: string;\n        // This isn't entirely accurate since a module can be imported multiple times in a file,\n        // and use different names. But it's good enough for now.\n        request: string;\n      }>\n    >\n  >\n>;\n\nfunction optionsKeyForContext(context: ResolutionContext) {\n  const canonicalize: typeof import('@expo/metro/metro-core/canonicalize').default = require('@expo/metro/metro-core/canonicalize');\n  // Compound key for the resolver cache\n  return JSON.stringify(context.customResolverOptions ?? {}, canonicalize) ?? '';\n}\n\nexport const createMutateResolutionError =\n  (\n    config: MetroConfig,\n    depGraph: DepGraph,\n    stackDepthLimit = STACK_DEPTH_LIMIT,\n    stackCountLimit = STACK_COUNT_LIMIT\n  ) =>\n  (error: Error, context: ResolutionContext, moduleName: string, platform: string | null) => {\n    const inputPlatform = platform ?? 'null';\n\n    const mapByOrigin = depGraph.get(optionsKeyForContext(context));\n    const mapByPlatform = mapByOrigin?.get(inputPlatform);\n\n    if (!mapByPlatform) {\n      return error;\n    }\n\n    // collect all references inversely using some expensive lookup\n\n    const getReferences = (origin: string) => {\n      const inverseOrigin: { origin: string; previous: string; request: string }[] = [];\n\n      if (!mapByPlatform) {\n        return inverseOrigin;\n      }\n\n      for (const [originKey, mapByTarget] of mapByPlatform) {\n        // search comparing origin to path\n\n        const found = [...mapByTarget.values()].find((resolution) => resolution.path === origin);\n        if (found) {\n          inverseOrigin.push({\n            origin,\n            previous: originKey,\n            request: found.request,\n          });\n        }\n      }\n\n      return inverseOrigin;\n    };\n\n    const root = config.server?.unstable_serverRoot ?? config.projectRoot;\n    const projectRoot = config.projectRoot;\n\n    type Frame = {\n      origin: string;\n      request: string;\n    };\n    type Stack = {\n      circular?: boolean;\n      limited?: boolean;\n      serverRoot?: boolean;\n      projectRoot?: boolean;\n      frames: Frame[];\n    };\n\n    let stackCounter = 0;\n    let inverseStack: Stack | undefined;\n    /** @returns boolean - done */\n    const saveStack = (stack: Stack): boolean => {\n      stackCounter++;\n\n      if (!inverseStack) {\n        // First stack, save it\n        inverseStack = stack;\n        return false;\n      }\n\n      if (stackCounter >= stackCountLimit) {\n        // Too many stacks explored, stop searching\n        return true;\n      }\n\n      if (stack.circular || stack.limited) {\n        // Not better than the current one, skip\n        return false;\n      }\n\n      if (inverseStack.circular || inverseStack.limited) {\n        // Current one is better than the previous one, save it\n        inverseStack = stack;\n        // No return as we want to continue validation the new stack\n      }\n\n      if (inverseStack.projectRoot) {\n        // The best possible stack already acquired, skip\n        return true;\n      }\n\n      const stackOrigin = stack.frames[stack.frames.length - 1].origin;\n\n      if (\n        stackOrigin &&\n        isPathInside(stackOrigin, projectRoot) &&\n        !stackOrigin.includes('node_modules')\n      ) {\n        // The best stack to show to users is the one leading from the project code.\n        stack.serverRoot = true;\n        inverseStack = stack;\n        return true;\n      }\n\n      if (\n        // Has to be after the project root check\n        stackOrigin &&\n        isPathInside(stackOrigin, root) &&\n        !stackOrigin.includes('node_modules')\n      ) {\n        // The best stack to show to users is the one leading from the monorepo code.\n        stack.serverRoot = true;\n        inverseStack = stack;\n        return false;\n      }\n\n      // If new stack is not better do nothing\n      return false;\n    };\n\n    /** @returns boolean - done */\n    const recurseBackWithLimit = (\n      frame: { origin: string; request: string },\n      limit: number,\n      stack: Stack = { frames: [] },\n      visited: Set<string> = new Set()\n    ): boolean => {\n      stack.frames.push(frame);\n\n      if (visited.has(frame.origin)) {\n        stack.circular = true;\n        return saveStack(stack);\n      }\n\n      if (stack.frames.length >= limit) {\n        stack.limited = true;\n        return saveStack(stack);\n      }\n\n      visited.add(frame.origin);\n\n      const inverse = getReferences(frame.origin);\n      if (inverse.length === 0) {\n        // No more references, push the stack and return\n        return saveStack(stack);\n      }\n\n      for (const match of inverse) {\n        // Use more qualified name if possible\n        // results.origin = match.origin;\n        // Found entry point\n        if (frame.origin === match.previous) {\n          continue;\n        }\n\n        const isDone = recurseBackWithLimit(\n          { origin: match.previous, request: match.request },\n          limit,\n          {\n            frames: [...stack.frames],\n          },\n          new Set(visited)\n        );\n\n        if (isDone) {\n          return true; // Stop search\n        }\n      }\n\n      return false; // Continue search\n    };\n\n    recurseBackWithLimit(\n      { origin: context.originModulePath, request: moduleName },\n      stackDepthLimit\n    );\n\n    debug('Number of explored stacks:', stackCounter);\n\n    if (inverseStack && inverseStack.frames.length > 0) {\n      const formattedImport = chalk`{gray  |} {cyan import} `;\n      const importMessagePadding = ' '.repeat(stripVTControlCharacters(formattedImport).length + 1);\n\n      debug('Found inverse graph:', JSON.stringify(inverseStack, null, 2));\n\n      let extraMessage = chalk.bold(\n        `Import stack${stackCounter >= stackCountLimit ? ` (${stackCounter})` : ''}:`\n      );\n\n      for (const frame of inverseStack.frames) {\n        let currentMessage = '';\n        let filename = path.relative(root, frame.origin);\n\n        if (filename.match(/\\?ctx=[\\w\\d]+$/)) {\n          filename = filename.replace(/\\?ctx=[\\w\\d]+$/, chalk.dim(' (require.context)'));\n        } else {\n          let formattedRequest = chalk.green(`\"${frame.request}\"`);\n\n          if (\n            // If bundling for web and the import is pulling internals from outside of react-native\n            // then mark it as an invalid import.\n            inputPlatform === 'web' &&\n            !/^(node_modules\\/)?react-native\\//.test(filename) &&\n            frame.request.match(/^react-native\\/.*/)\n          ) {\n            formattedRequest =\n              formattedRequest +\n              chalk`\\n${importMessagePadding}{yellow ^ Importing react-native internals is not supported on web.}`;\n          }\n\n          filename = filename + chalk`\\n${formattedImport}${formattedRequest}`;\n        }\n\n        let line = '\\n' + chalk.gray(' ') + filename;\n        if (filename.match(/node_modules/)) {\n          line = chalk.gray(\n            // Bold the node module name\n            line.replace(/node_modules\\/([^/]+)/, (_match, p1) => {\n              return 'node_modules/' + chalk.bold(p1);\n            })\n          );\n        }\n        currentMessage += `\\n${line}`;\n        extraMessage += currentMessage;\n      }\n\n      if (inverseStack.circular) {\n        extraMessage += chalk`\\n${importMessagePadding}{yellow ^ The import above creates circular dependency.}`;\n      }\n\n      if (inverseStack.limited) {\n        extraMessage += chalk`\\n\\n {bold {yellow Depth limit reached. The actual stack is longer than what you can see above.}}`;\n      }\n\n      extraMessage += '\\n';\n\n      // @ts-expect-error\n      error._expoImportStack = extraMessage;\n    } else {\n      debug('Found no inverse tree for:', context.originModulePath);\n    }\n\n    return error;\n  };\n"], "names": ["createMutateResolutionError", "withMetroErrorReportingResolver", "debug", "require", "STACK_DEPTH_LIMIT", "STACK_COUNT_LIMIT", "config", "env", "EXPO_METRO_UNSTABLE_ERRORS", "originalResolveRequest", "resolver", "resolveRequest", "depGraph", "Map", "mutateResolutionError", "context", "moduleName", "platform", "storeResult", "res", "inputPlatform", "key", "optionsKeyForContext", "has", "set", "mapByTarget", "get", "mapByPlatform", "originModulePath", "Set", "setForModule", "qualifiedModuleName", "type", "filePath", "add", "path", "request", "firstResolver", "error", "canonicalize", "JSON", "stringify", "customResolverOptions", "stackDepthLimit", "stackCountLimit", "mapByOrigin", "getReferences", "origin", "inverse<PERSON><PERSON>in", "<PERSON><PERSON><PERSON>", "found", "values", "find", "resolution", "push", "previous", "root", "server", "unstable_serverRoot", "projectRoot", "stackCounter", "inverseStack", "saveStack", "stack", "circular", "limited", "stackOrigin", "frames", "length", "isPathInside", "includes", "serverRoot", "recurseBackWithLimit", "frame", "limit", "visited", "inverse", "match", "isDone", "formattedImport", "chalk", "importMessagePadding", "repeat", "stripVTControlCharacters", "extraMessage", "bold", "currentMessage", "filename", "relative", "replace", "dim", "formattedRequest", "green", "test", "line", "gray", "_match", "p1", "_expoImportStack"], "mappings": ";;;;;;;;;;;IAgGaA,2BAA2B;eAA3BA;;IAhFGC,+BAA+B;eAA/BA;;;;gEAdE;;;;;;;gEACD;;;;;;;yBACwB;;;;;;qBAGZ;qBACT;;;;;;AAEpB,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,mCAAmC;AACnC,MAAMC,oBAAoB;AAC1B,MAAMC,oBAAoB;AAEnB,SAASJ,gCAAgCK,MAAmB;QAKlCA;IAJ/B,IAAI,CAACC,QAAG,CAACC,0BAA0B,EAAE;QACnC,OAAOF;IACT;IAEA,MAAMG,0BAAyBH,mBAAAA,OAAOI,QAAQ,qBAAfJ,iBAAiBK,cAAc;IAE9D,MAAMC,WAAqB,IAAIC;IAE/B,MAAMC,wBAAwBd,4BAA4BM,QAAQM;IAElE,OAAO;QACL,GAAGN,MAAM;QACTI,UAAU;YACR,GAAGJ,OAAOI,QAAQ;YAClBC,gBAAeI,OAAO,EAAEC,UAAU,EAAEC,QAAQ;gBAC1C,MAAMC,cAAc,CAACC;oBACnB,MAAMC,gBAAgBH,YAAY;oBAElC,MAAMI,MAAMC,qBAAqBP;oBACjC,IAAI,CAACH,SAASW,GAAG,CAACF,MAAMT,SAASY,GAAG,CAACH,KAAK,IAAIR;oBAC9C,MAAMY,cAAcb,SAASc,GAAG,CAACL;oBACjC,IAAI,CAACI,YAAaF,GAAG,CAACH,gBAAgBK,YAAaD,GAAG,CAACJ,eAAe,IAAIP;oBAC1E,MAAMc,gBAAgBF,YAAaC,GAAG,CAACN;oBACvC,IAAI,CAACO,cAAeJ,GAAG,CAACR,QAAQa,gBAAgB,GAC9CD,cAAeH,GAAG,CAACT,QAAQa,gBAAgB,EAAE,IAAIC;oBACnD,MAAMC,eAAeH,cAAeD,GAAG,CAACX,QAAQa,gBAAgB;oBAEhE,MAAMG,sBAAsBZ,CAAAA,uBAAAA,IAAKa,IAAI,MAAK,eAAeb,IAAIc,QAAQ,GAAGjB;oBACxEc,aAAaI,GAAG,CAAC;wBAAEC,MAAMJ;wBAAqBK,SAASpB;oBAAW;gBACpE;gBAEA,4EAA4E;gBAC5E,yFAAyF;gBACzF,EAAE;gBACF,wEAAwE;gBACxE,EAAE;gBACF,iBAAiB;gBACjB,EAAE;gBACF,iEAAiE;gBACjE,KAAK;gBACL,IAAI;oBACF,MAAMqB,gBAAgB5B,0BAA0BM,QAAQJ,cAAc;oBACtE,MAAMQ,MAAMkB,cAActB,SAASC,YAAYC;oBAC/CC,YAAYC;oBACZ,OAAOA;gBACT,EAAE,OAAOmB,OAAY;oBACnB,MAAMxB,sBAAsBwB,OAAOvB,SAASC,YAAYC;gBAC1D;YACF;QACF;IACF;AACF;AAsBA,SAASK,qBAAqBP,OAA0B;IACtD,MAAMwB,eAA6EpC,QAAQ;IAC3F,sCAAsC;IACtC,OAAOqC,KAAKC,SAAS,CAAC1B,QAAQ2B,qBAAqB,IAAI,CAAC,GAAGH,iBAAiB;AAC9E;AAEO,MAAMvC,8BACX,CACEM,QACAM,UACA+B,kBAAkBvC,iBAAiB,EACnCwC,kBAAkBvC,iBAAiB,GAErC,CAACiC,OAAcvB,SAA4BC,YAAoBC;YAmChDX;QAlCb,MAAMc,gBAAgBH,YAAY;QAElC,MAAM4B,cAAcjC,SAASc,GAAG,CAACJ,qBAAqBP;QACtD,MAAMY,gBAAgBkB,+BAAAA,YAAanB,GAAG,CAACN;QAEvC,IAAI,CAACO,eAAe;YAClB,OAAOW;QACT;QAEA,+DAA+D;QAE/D,MAAMQ,gBAAgB,CAACC;YACrB,MAAMC,gBAAyE,EAAE;YAEjF,IAAI,CAACrB,eAAe;gBAClB,OAAOqB;YACT;YAEA,KAAK,MAAM,CAACC,WAAWxB,YAAY,IAAIE,cAAe;gBACpD,kCAAkC;gBAElC,MAAMuB,QAAQ;uBAAIzB,YAAY0B,MAAM;iBAAG,CAACC,IAAI,CAAC,CAACC,aAAeA,WAAWlB,IAAI,KAAKY;gBACjF,IAAIG,OAAO;oBACTF,cAAcM,IAAI,CAAC;wBACjBP;wBACAQ,UAAUN;wBACVb,SAASc,MAAMd,OAAO;oBACxB;gBACF;YACF;YAEA,OAAOY;QACT;QAEA,MAAMQ,OAAOlD,EAAAA,iBAAAA,OAAOmD,MAAM,qBAAbnD,eAAeoD,mBAAmB,KAAIpD,OAAOqD,WAAW;QACrE,MAAMA,cAAcrD,OAAOqD,WAAW;QActC,IAAIC,eAAe;QACnB,IAAIC;QACJ,4BAA4B,GAC5B,MAAMC,YAAY,CAACC;YACjBH;YAEA,IAAI,CAACC,cAAc;gBACjB,uBAAuB;gBACvBA,eAAeE;gBACf,OAAO;YACT;YAEA,IAAIH,gBAAgBhB,iBAAiB;gBACnC,2CAA2C;gBAC3C,OAAO;YACT;YAEA,IAAImB,MAAMC,QAAQ,IAAID,MAAME,OAAO,EAAE;gBACnC,wCAAwC;gBACxC,OAAO;YACT;YAEA,IAAIJ,aAAaG,QAAQ,IAAIH,aAAaI,OAAO,EAAE;gBACjD,uDAAuD;gBACvDJ,eAAeE;YACf,4DAA4D;YAC9D;YAEA,IAAIF,aAAaF,WAAW,EAAE;gBAC5B,iDAAiD;gBACjD,OAAO;YACT;YAEA,MAAMO,cAAcH,MAAMI,MAAM,CAACJ,MAAMI,MAAM,CAACC,MAAM,GAAG,EAAE,CAACrB,MAAM;YAEhE,IACEmB,eACAG,IAAAA,iBAAY,EAACH,aAAaP,gBAC1B,CAACO,YAAYI,QAAQ,CAAC,iBACtB;gBACA,4EAA4E;gBAC5EP,MAAMQ,UAAU,GAAG;gBACnBV,eAAeE;gBACf,OAAO;YACT;YAEA,IACE,yCAAyC;YACzCG,eACAG,IAAAA,iBAAY,EAACH,aAAaV,SAC1B,CAACU,YAAYI,QAAQ,CAAC,iBACtB;gBACA,6EAA6E;gBAC7EP,MAAMQ,UAAU,GAAG;gBACnBV,eAAeE;gBACf,OAAO;YACT;YAEA,wCAAwC;YACxC,OAAO;QACT;QAEA,4BAA4B,GAC5B,MAAMS,uBAAuB,CAC3BC,OACAC,OACAX,QAAe;YAAEI,QAAQ,EAAE;QAAC,CAAC,EAC7BQ,UAAuB,IAAI9C,KAAK;YAEhCkC,MAAMI,MAAM,CAACb,IAAI,CAACmB;YAElB,IAAIE,QAAQpD,GAAG,CAACkD,MAAM1B,MAAM,GAAG;gBAC7BgB,MAAMC,QAAQ,GAAG;gBACjB,OAAOF,UAAUC;YACnB;YAEA,IAAIA,MAAMI,MAAM,CAACC,MAAM,IAAIM,OAAO;gBAChCX,MAAME,OAAO,GAAG;gBAChB,OAAOH,UAAUC;YACnB;YAEAY,QAAQzC,GAAG,CAACuC,MAAM1B,MAAM;YAExB,MAAM6B,UAAU9B,cAAc2B,MAAM1B,MAAM;YAC1C,IAAI6B,QAAQR,MAAM,KAAK,GAAG;gBACxB,gDAAgD;gBAChD,OAAON,UAAUC;YACnB;YAEA,KAAK,MAAMc,SAASD,QAAS;gBAC3B,sCAAsC;gBACtC,iCAAiC;gBACjC,oBAAoB;gBACpB,IAAIH,MAAM1B,MAAM,KAAK8B,MAAMtB,QAAQ,EAAE;oBACnC;gBACF;gBAEA,MAAMuB,SAASN,qBACb;oBAAEzB,QAAQ8B,MAAMtB,QAAQ;oBAAEnB,SAASyC,MAAMzC,OAAO;gBAAC,GACjDsC,OACA;oBACEP,QAAQ;2BAAIJ,MAAMI,MAAM;qBAAC;gBAC3B,GACA,IAAItC,IAAI8C;gBAGV,IAAIG,QAAQ;oBACV,OAAO,MAAM,cAAc;gBAC7B;YACF;YAEA,OAAO,OAAO,kBAAkB;QAClC;QAEAN,qBACE;YAAEzB,QAAQhC,QAAQa,gBAAgB;YAAEQ,SAASpB;QAAW,GACxD2B;QAGFzC,MAAM,8BAA8B0D;QAEpC,IAAIC,gBAAgBA,aAAaM,MAAM,CAACC,MAAM,GAAG,GAAG;YAClD,MAAMW,kBAAkBC,IAAAA,gBAAK,CAAA,CAAC,wBAAwB,CAAC;YACvD,MAAMC,uBAAuB,IAAIC,MAAM,CAACC,IAAAA,gCAAwB,EAACJ,iBAAiBX,MAAM,GAAG;YAE3FlE,MAAM,wBAAwBsC,KAAKC,SAAS,CAACoB,cAAc,MAAM;YAEjE,IAAIuB,eAAeJ,gBAAK,CAACK,IAAI,CAC3B,CAAC,YAAY,EAAEzB,gBAAgBhB,kBAAkB,CAAC,EAAE,EAAEgB,aAAa,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YAG/E,KAAK,MAAMa,SAASZ,aAAaM,MAAM,CAAE;gBACvC,IAAImB,iBAAiB;gBACrB,IAAIC,WAAWpD,eAAI,CAACqD,QAAQ,CAAChC,MAAMiB,MAAM1B,MAAM;gBAE/C,IAAIwC,SAASV,KAAK,CAAC,mBAAmB;oBACpCU,WAAWA,SAASE,OAAO,CAAC,kBAAkBT,gBAAK,CAACU,GAAG,CAAC;gBAC1D,OAAO;oBACL,IAAIC,mBAAmBX,gBAAK,CAACY,KAAK,CAAC,CAAC,CAAC,EAAEnB,MAAMrC,OAAO,CAAC,CAAC,CAAC;oBAEvD,IACE,uFAAuF;oBACvF,qCAAqC;oBACrChB,kBAAkB,SAClB,CAAC,mCAAmCyE,IAAI,CAACN,aACzCd,MAAMrC,OAAO,CAACyC,KAAK,CAAC,sBACpB;wBACAc,mBACEA,mBACAX,IAAAA,gBAAK,CAAA,CAAC,EAAE,EAAEC,qBAAqB,oEAAoE,CAAC;oBACxG;oBAEAM,WAAWA,WAAWP,IAAAA,gBAAK,CAAA,CAAC,EAAE,EAAED,gBAAgB,EAAEY,iBAAiB,CAAC;gBACtE;gBAEA,IAAIG,OAAO,OAAOd,gBAAK,CAACe,IAAI,CAAC,OAAOR;gBACpC,IAAIA,SAASV,KAAK,CAAC,iBAAiB;oBAClCiB,OAAOd,gBAAK,CAACe,IAAI,CACf,4BAA4B;oBAC5BD,KAAKL,OAAO,CAAC,yBAAyB,CAACO,QAAQC;wBAC7C,OAAO,kBAAkBjB,gBAAK,CAACK,IAAI,CAACY;oBACtC;gBAEJ;gBACAX,kBAAkB,CAAC,EAAE,EAAEQ,MAAM;gBAC7BV,gBAAgBE;YAClB;YAEA,IAAIzB,aAAaG,QAAQ,EAAE;gBACzBoB,gBAAgBJ,IAAAA,gBAAK,CAAA,CAAC,EAAE,EAAEC,qBAAqB,wDAAwD,CAAC;YAC1G;YAEA,IAAIpB,aAAaI,OAAO,EAAE;gBACxBmB,gBAAgBJ,IAAAA,gBAAK,CAAA,CAAC,iGAAiG,CAAC;YAC1H;YAEAI,gBAAgB;YAEhB,mBAAmB;YACnB9C,MAAM4D,gBAAgB,GAAGd;QAC3B,OAAO;YACLlF,MAAM,8BAA8Ba,QAAQa,gBAAgB;QAC9D;QAEA,OAAOU;IACT"}