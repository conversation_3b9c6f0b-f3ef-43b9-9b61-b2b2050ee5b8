{"version": 3, "sources": ["../../../../../src/start/server/metro/MetroBundlerDevServer.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { ExpoConfig, getConfig } from '@expo/config';\nimport { getMetroServerRoot } from '@expo/config/paths';\nimport * as runtimeEnv from '@expo/env';\nimport baseJSBundle from '@expo/metro/metro/DeltaBundler/Serializers/baseJSBundle';\nimport {\n  sourceMapGeneratorNonBlocking,\n  type SourceMapGeneratorOptions,\n} from '@expo/metro/metro/DeltaBundler/Serializers/sourceMapGenerator';\nimport type {\n  Module,\n  DeltaResult,\n  TransformInputOptions,\n} from '@expo/metro/metro/DeltaBundler/types.flow';\nimport type {\n  default as MetroHmrServer,\n  Client as MetroHmrClient,\n} from '@expo/metro/metro/HmrServer';\nimport type { GraphRevision } from '@expo/metro/metro/IncrementalBundler';\nimport type MetroServer from '@expo/metro/metro/Server';\nimport bundleToString from '@expo/metro/metro/lib/bundleToString';\nimport getGraphId from '@expo/metro/metro/lib/getGraphId';\nimport type { TransformProfile } from '@expo/metro/metro-babel-transformer';\nimport type { CustomResolverOptions } from '@expo/metro/metro-resolver';\nimport { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport {\n  createServerComponentsMiddleware,\n  fileURLToFilePath,\n} from './createServerComponentsMiddleware';\nimport { createRouteHandlerMiddleware } from './createServerRouteMiddleware';\nimport { ExpoRouterServerManifestV1, fetchManifest } from './fetchRouterManifest';\nimport { instantiateMetroAsync } from './instantiateMetro';\nimport {\n  attachImportStackToRootMessage,\n  dropStackIfContainsCodeFrame,\n  getErrorOverlayHtmlAsync,\n  IS_METRO_BUNDLE_ERROR_SYMBOL,\n} from './metroErrorInterface';\nimport { metroWatchTypeScriptFiles } from './metroWatchTypeScriptFiles';\nimport {\n  getRouterDirectoryModuleIdWithManifest,\n  hasWarnedAboutApiRoutes,\n  isApiRouteConvention,\n  warnInvalidWebOutput,\n} from './router';\nimport { serializeHtmlWithAssets } from './serializeHtml';\nimport { observeAnyFileChanges, observeFileChanges } from './waitForMetroToObserveTypeScriptFile';\nimport {\n  BundleAssetWithFileHashes,\n  ExportAssetDescriptor,\n  ExportAssetMap,\n} from '../../../export/saveAssets';\nimport { Log } from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { getFreePortAsync } from '../../../utils/port';\nimport { BundlerDevServer, BundlerStartOptions, DevServerInstance } from '../BundlerDevServer';\nimport {\n  cachedSourceMaps,\n  evalMetroAndWrapFunctions,\n  evalMetroNoHandling,\n} from '../getStaticRenderFunctions';\nimport { ContextModuleSourceMapsMiddleware } from '../middleware/ContextModuleSourceMapsMiddleware';\nimport { CreateFileMiddleware } from '../middleware/CreateFileMiddleware';\nimport { DevToolsPluginMiddleware } from '../middleware/DevToolsPluginMiddleware';\nimport { createDomComponentsMiddleware } from '../middleware/DomComponentsMiddleware';\nimport { FaviconMiddleware } from '../middleware/FaviconMiddleware';\nimport { HistoryFallbackMiddleware } from '../middleware/HistoryFallbackMiddleware';\nimport { InterstitialPageMiddleware } from '../middleware/InterstitialPageMiddleware';\nimport { resolveMainModuleName } from '../middleware/ManifestMiddleware';\nimport { RuntimeRedirectMiddleware } from '../middleware/RuntimeRedirectMiddleware';\nimport { ServeStaticMiddleware } from '../middleware/ServeStaticMiddleware';\nimport {\n  convertPathToModuleSpecifier,\n  createBundleUrlPath,\n  ExpoMetroOptions,\n  createBundleOsPath,\n  getAsyncRoutesFromExpoConfig,\n  getBaseUrlFromExpoConfig,\n  getMetroDirectBundleOptions,\n} from '../middleware/metroOptions';\nimport { prependMiddleware } from '../middleware/mutations';\nimport { startTypescriptTypeGenerationAsync } from '../type-generation/startTypescriptTypeGeneration';\n\nexport type ExpoRouterRuntimeManifest = Awaited<\n  ReturnType<typeof import('expo-router/build/static/renderStaticContent').getManifest>\n>;\n\ntype SSRLoadModuleFunc = <T extends Record<string, any>>(\n  filePath: string,\n  specificOptions?: Partial<ExpoMetroOptions>,\n  extras?: { hot?: boolean }\n) => Promise<T>;\n\ninterface BundleDirectResult {\n  numModifiedFiles: number;\n  lastModifiedDate: Date;\n  nextRevId: string;\n  bundle: string;\n  map: string;\n  /** Defined if the output is multi-bundle. */\n  artifacts?: SerialAsset[];\n  assets?: readonly BundleAssetWithFileHashes[];\n}\n\ninterface MetroModuleContentsResult extends BundleDirectResult {\n  filename: string;\n}\n\ninterface SSRModuleContentsResult extends Omit<BundleDirectResult, 'bundle'> {\n  filename: string;\n  src: string;\n  map: string;\n}\n\nconst debug = require('debug')('expo:start:server:metro') as typeof console.log;\n\n/** Default port to use for apps running in Expo Go. */\nconst EXPO_GO_METRO_PORT = 8081;\n\n/** Default port to use for apps that run in standard React Native projects or Expo Dev Clients. */\nconst DEV_CLIENT_METRO_PORT = 8081;\n\nexport class MetroBundlerDevServer extends BundlerDevServer {\n  private metro: MetroServer | null = null;\n  private hmrServer: MetroHmrServer<MetroHmrClient> | null = null;\n  private ssrHmrClients: Map<string, MetroHmrClient> = new Map();\n  isReactServerComponentsEnabled?: boolean;\n  isReactServerRoutesEnabled?: boolean;\n\n  get name(): string {\n    return 'metro';\n  }\n\n  async resolvePortAsync(options: Partial<BundlerStartOptions> = {}): Promise<number> {\n    const port =\n      // If the manually defined port is busy then an error should be thrown...\n      options.port ??\n      // Otherwise use the default port based on the runtime target.\n      (options.devClient\n        ? // Don't check if the port is busy if we're using the dev client since most clients are hardcoded to 8081.\n          Number(process.env.RCT_METRO_PORT) || DEV_CLIENT_METRO_PORT\n        : // Otherwise (running in Expo Go) use a free port that falls back on the classic 8081 port.\n          await getFreePortAsync(EXPO_GO_METRO_PORT));\n\n    return port;\n  }\n\n  private async exportServerRoute({\n    contents,\n    artifactFilename,\n    files,\n    includeSourceMaps,\n    descriptor,\n  }: {\n    contents: { src: string; map?: any } | null | undefined;\n    artifactFilename: string;\n    files: ExportAssetMap;\n    includeSourceMaps?: boolean;\n    routeId?: string;\n    descriptor: Partial<ExportAssetDescriptor>;\n  }) {\n    if (!contents) return;\n\n    let src = contents.src;\n    if (includeSourceMaps && contents.map) {\n      // TODO(kitten): Merge the source map transformer in the future\n      // https://github.com/expo/expo/blob/0dffdb15/packages/%40expo/metro-config/src/serializer/serializeChunks.ts#L422-L439\n      // Alternatively, check whether `sourcesRoot` helps here\n      const artifactBasename = encodeURIComponent(path.basename(artifactFilename) + '.map');\n      src = src.replace(/\\/\\/# sourceMappingURL=.*/g, `//# sourceMappingURL=${artifactBasename}`);\n      const parsedMap = typeof contents.map === 'string' ? JSON.parse(contents.map) : contents.map;\n      const mapData: any = {\n        ...descriptor,\n        contents: JSON.stringify({\n          version: parsedMap.version,\n          sources: parsedMap.sources.map((source: string) => {\n            source =\n              typeof source === 'string' && source.startsWith(this.projectRoot)\n                ? path.relative(this.projectRoot, source)\n                : source;\n            return convertPathToModuleSpecifier(source);\n          }),\n          sourcesContent: new Array(parsedMap.sources.length).fill(null),\n          names: parsedMap.names,\n          mappings: parsedMap.mappings,\n        }),\n        targetDomain: 'server',\n      };\n      files.set(artifactFilename + '.map', mapData);\n    }\n    const fileData: ExportAssetDescriptor = {\n      ...descriptor,\n      contents: src,\n      targetDomain: 'server',\n    };\n    files.set(artifactFilename, fileData);\n  }\n\n  private async exportMiddleware({\n    manifest,\n    appDir,\n    outputDir,\n    files,\n    platform,\n    includeSourceMaps,\n  }: {\n    manifest: ExpoRouterServerManifestV1;\n    appDir: string;\n    outputDir: string;\n    files: ExportAssetMap;\n    platform: string;\n    includeSourceMaps?: boolean;\n  }) {\n    if (!manifest.middleware) return;\n\n    const middlewareFilePath = path.isAbsolute(manifest.middleware.file)\n      ? manifest.middleware.file\n      : path.join(appDir, manifest.middleware.file);\n    const contents = await this.bundleApiRoute(middlewareFilePath, { platform });\n    const artifactFilename = convertPathToModuleSpecifier(\n      path.join(outputDir, path.relative(appDir, middlewareFilePath.replace(/\\.[tj]sx?$/, '.js')))\n    );\n\n    await this.exportServerRoute({\n      contents,\n      artifactFilename,\n      files,\n      includeSourceMaps,\n      descriptor: {\n        middlewareId: '/middleware',\n      },\n    });\n\n    // Remap the middleware file to represent the output file.\n    manifest.middleware.file = artifactFilename;\n  }\n\n  async exportExpoRouterApiRoutesAsync({\n    includeSourceMaps,\n    outputDir,\n    prerenderManifest,\n    platform,\n  }: {\n    includeSourceMaps?: boolean;\n    outputDir: string;\n    // This does not contain the API routes info.\n    prerenderManifest: ExpoRouterServerManifestV1;\n    platform: string;\n  }): Promise<{ files: ExportAssetMap; manifest: ExpoRouterServerManifestV1<string> }> {\n    const { routerRoot } = this.instanceMetroOptions;\n    assert(\n      routerRoot != null,\n      'The server must be started before calling exportExpoRouterApiRoutesAsync.'\n    );\n\n    const appDir = path.join(this.projectRoot, routerRoot);\n    const manifest = await this.getExpoRouterRoutesManifestAsync({ appDir });\n\n    const files: ExportAssetMap = new Map();\n\n    // Inject RSC middleware.\n    const rscPath = '/_flight/[...rsc]';\n\n    if (\n      this.isReactServerComponentsEnabled &&\n      // If the RSC route is not already in the manifest, add it.\n      !manifest.apiRoutes.find((route) => route.page.startsWith('/_flight/'))\n    ) {\n      debug('Adding RSC route to the manifest:', rscPath);\n      // NOTE: This might need to be sorted to the correct spot in the future.\n      manifest.apiRoutes.push({\n        file: resolveFrom(this.projectRoot, '@expo/cli/static/template/[...rsc]+api.ts'),\n        page: rscPath,\n        namedRegex: '^/_flight(?:/(?<rsc>.+?))?(?:/)?$',\n        routeKeys: { rsc: 'rsc' },\n      });\n    }\n\n    await this.exportMiddleware({\n      manifest,\n      appDir,\n      outputDir,\n      files,\n      platform,\n      includeSourceMaps,\n    });\n\n    for (const route of manifest.apiRoutes) {\n      const filepath = path.isAbsolute(route.file) ? route.file : path.join(appDir, route.file);\n      const contents = await this.bundleApiRoute(filepath, { platform });\n\n      const artifactFilename =\n        route.page === rscPath\n          ? // HACK: Add RSC renderer to the output...\n            convertPathToModuleSpecifier(path.join(outputDir, '.' + rscPath + '.js'))\n          : convertPathToModuleSpecifier(\n              path.join(outputDir, path.relative(appDir, filepath.replace(/\\.[tj]sx?$/, '.js')))\n            );\n\n      await this.exportServerRoute({\n        contents,\n        artifactFilename,\n        files,\n        includeSourceMaps,\n        descriptor: {\n          apiRouteId: route.page,\n        },\n      });\n      // Remap the manifest files to represent the output files.\n      route.file = artifactFilename;\n    }\n\n    return {\n      manifest: {\n        ...manifest,\n        htmlRoutes: prerenderManifest.htmlRoutes,\n      },\n      files,\n    };\n  }\n\n  async getExpoRouterRoutesManifestAsync({ appDir }: { appDir: string }) {\n    // getBuiltTimeServerManifest\n    const { exp } = getConfig(this.projectRoot);\n    const manifest = await fetchManifest(this.projectRoot, {\n      ...exp.extra?.router,\n      preserveRedirectAndRewrites: true,\n      asJson: true,\n      appDir,\n    });\n\n    if (!manifest) {\n      throw new CommandError(\n        'EXPO_ROUTER_SERVER_MANIFEST',\n        'Unexpected error: server manifest could not be fetched.'\n      );\n    }\n\n    return manifest;\n  }\n\n  async getServerManifestAsync(): Promise<{\n    serverManifest: ExpoRouterServerManifestV1;\n    htmlManifest: ExpoRouterRuntimeManifest;\n  }> {\n    const { exp } = getConfig(this.projectRoot);\n    // NOTE: This could probably be folded back into `renderStaticContent` when expo-asset and font support RSC.\n    const { getBuildTimeServerManifestAsync, getManifest } = await this.ssrLoadModule<\n      typeof import('expo-router/build/static/getServerManifest')\n    >('expo-router/build/static/getServerManifest.js', {\n      // Only use react-server environment when the routes are using react-server rendering by default.\n      environment: this.isReactServerRoutesEnabled ? 'react-server' : 'node',\n    });\n\n    return {\n      serverManifest: await getBuildTimeServerManifestAsync({ ...exp.extra?.router }),\n      htmlManifest: await getManifest({ ...exp.extra?.router }),\n    };\n  }\n\n  async getStaticRenderFunctionAsync(): Promise<{\n    serverManifest: ExpoRouterServerManifestV1;\n    manifest: ExpoRouterRuntimeManifest;\n    renderAsync: (path: string) => Promise<string>;\n  }> {\n    const url = this.getDevServerUrlOrAssert();\n\n    const { getStaticContent, getManifest, getBuildTimeServerManifestAsync } =\n      await this.ssrLoadModule<typeof import('expo-router/build/static/renderStaticContent')>(\n        'expo-router/node/render.js',\n        {\n          // This must always use the legacy rendering resolution (no `react-server`) because it leverages\n          // the previous React SSG utilities which aren't available in React 19.\n          environment: 'node',\n        }\n      );\n\n    const { exp } = getConfig(this.projectRoot);\n\n    return {\n      serverManifest: await getBuildTimeServerManifestAsync({\n        ...exp.extra?.router,\n      }),\n      // Get routes from Expo Router.\n      manifest: await getManifest({ preserveApiRoutes: false, ...exp.extra?.router }),\n      // Get route generating function\n      async renderAsync(path: string) {\n        return await getStaticContent(new URL(path, url));\n      },\n    };\n  }\n\n  async getStaticResourcesAsync({\n    includeSourceMaps,\n    mainModuleName,\n    clientBoundaries = this.instanceMetroOptions.clientBoundaries ?? [],\n    platform = 'web',\n  }: {\n    includeSourceMaps?: boolean;\n    mainModuleName?: string;\n    clientBoundaries?: string[];\n    platform?: string;\n  } = {}) {\n    const { mode, minify, isExporting, baseUrl, reactCompiler, routerRoot, asyncRoutes } =\n      this.instanceMetroOptions;\n    assert(\n      mode != null &&\n        isExporting != null &&\n        baseUrl != null &&\n        routerRoot != null &&\n        reactCompiler != null &&\n        asyncRoutes != null,\n      'The server must be started before calling getStaticResourcesAsync.'\n    );\n\n    const resolvedMainModuleName =\n      mainModuleName ?? './' + resolveMainModuleName(this.projectRoot, { platform });\n    return await this.metroImportAsArtifactsAsync(resolvedMainModuleName, {\n      splitChunks: isExporting && !env.EXPO_NO_BUNDLE_SPLITTING,\n      platform,\n      mode,\n      minify,\n      environment: 'client',\n      serializerIncludeMaps: includeSourceMaps,\n      mainModuleName: resolvedMainModuleName,\n      lazy: !env.EXPO_NO_METRO_LAZY,\n      asyncRoutes,\n      baseUrl,\n      isExporting,\n      routerRoot,\n      clientBoundaries,\n      reactCompiler,\n      bytecode: false,\n    });\n  }\n\n  private async getStaticPageAsync(pathname: string) {\n    const { mode, isExporting, clientBoundaries, baseUrl, reactCompiler, routerRoot, asyncRoutes } =\n      this.instanceMetroOptions;\n    assert(\n      mode != null &&\n        isExporting != null &&\n        baseUrl != null &&\n        reactCompiler != null &&\n        routerRoot != null &&\n        asyncRoutes != null,\n      'The server must be started before calling getStaticPageAsync.'\n    );\n    const platform = 'web';\n\n    const devBundleUrlPathname = createBundleUrlPath({\n      splitChunks: isExporting && !env.EXPO_NO_BUNDLE_SPLITTING,\n      platform,\n      mode,\n      environment: 'client',\n      reactCompiler,\n      mainModuleName: resolveMainModuleName(this.projectRoot, { platform }),\n      lazy: !env.EXPO_NO_METRO_LAZY,\n      baseUrl,\n      isExporting,\n      asyncRoutes,\n      routerRoot,\n      clientBoundaries,\n      bytecode: false,\n    });\n\n    const bundleStaticHtml = async (): Promise<string> => {\n      const { getStaticContent } = await this.ssrLoadModule<\n        typeof import('expo-router/build/static/renderStaticContent')\n      >('expo-router/node/render.js', {\n        // This must always use the legacy rendering resolution (no `react-server`) because it leverages\n        // the previous React SSG utilities which aren't available in React 19.\n        environment: 'node',\n        minify: false,\n        isExporting,\n        platform,\n      });\n\n      const location = new URL(pathname, this.getDevServerUrlOrAssert());\n      return await getStaticContent(location);\n    };\n\n    const [{ artifacts: resources }, staticHtml] = await Promise.all([\n      this.getStaticResourcesAsync({\n        clientBoundaries: [],\n      }),\n      bundleStaticHtml(),\n    ]);\n    const content = serializeHtmlWithAssets({\n      isExporting,\n      resources,\n      template: staticHtml,\n      devBundleUrl: devBundleUrlPathname,\n      baseUrl,\n      hydrate: env.EXPO_WEB_DEV_HYDRATE,\n    });\n    return {\n      content,\n      resources,\n    };\n  }\n\n  // Set when the server is started.\n  private instanceMetroOptions: Partial<ExpoMetroOptions> = {};\n\n  private ssrLoadModule: SSRLoadModuleFunc = async (\n    filePath,\n    specificOptions = {},\n    extras = {}\n  ) => {\n    const res = await this.ssrLoadModuleContents(filePath, specificOptions);\n\n    if (\n      // TODO: hot should be a callback function for invalidating the related SSR module.\n      extras.hot &&\n      this.instanceMetroOptions.isExporting !== true\n    ) {\n      // Register SSR HMR\n      const serverRoot = getMetroServerRoot(this.projectRoot);\n      const relativePath = path.relative(serverRoot, res.filename);\n      const url = new URL(relativePath, this.getDevServerUrlOrAssert());\n      this.setupHmr(url);\n    }\n\n    return evalMetroAndWrapFunctions(\n      this.projectRoot,\n      res.src,\n      res.filename,\n      specificOptions.isExporting ?? this.instanceMetroOptions.isExporting!\n    );\n  };\n\n  private async metroImportAsArtifactsAsync(\n    filePath: string,\n    specificOptions: Partial<Omit<ExpoMetroOptions, 'serializerOutput'>> = {}\n  ) {\n    const results = await this.ssrLoadModuleContents(filePath, {\n      serializerOutput: 'static',\n      ...specificOptions,\n    });\n\n    // NOTE: This could potentially need more validation in the future.\n    if (results.artifacts && results.assets) {\n      return {\n        artifacts: results.artifacts,\n        assets: results.assets,\n        src: results.src,\n        filename: results.filename,\n        map: results.map,\n      };\n    }\n    throw new CommandError('Invalid bundler results: ' + results);\n  }\n\n  private async metroLoadModuleContents(\n    filePath: string,\n    specificOptions: ExpoMetroOptions,\n    extraOptions: {\n      sourceMapUrl?: string;\n      unstable_transformProfile?: TransformProfile;\n    } = {}\n  ): Promise<MetroModuleContentsResult> {\n    const { baseUrl } = this.instanceMetroOptions;\n    assert(baseUrl != null, 'The server must be started before calling metroLoadModuleContents.');\n\n    const opts: ExpoMetroOptions = {\n      // TODO: Possibly issues with using an absolute path here...\n      // mainModuleName: filePath,\n      lazy: false,\n      asyncRoutes: false,\n      inlineSourceMap: false,\n      engine: 'hermes',\n      minify: false,\n      // bytecode: false,\n      // Bundle in Node.js mode for SSR.\n      environment: 'node',\n      // platform: 'web',\n      // mode: 'development',\n      //\n      ...this.instanceMetroOptions,\n      baseUrl,\n      // routerRoot,\n      // isExporting,\n      ...specificOptions,\n    };\n\n    const expoBundleOptions = getMetroDirectBundleOptions(opts);\n\n    const resolverOptions = {\n      customResolverOptions: expoBundleOptions.customResolverOptions ?? {},\n      dev: expoBundleOptions.dev ?? true,\n    };\n\n    const transformOptions: TransformInputOptions = {\n      dev: expoBundleOptions.dev ?? true,\n      hot: true,\n      minify: expoBundleOptions.minify ?? false,\n      type: 'module',\n      unstable_transformProfile:\n        extraOptions.unstable_transformProfile ??\n        expoBundleOptions.unstable_transformProfile ??\n        'default',\n      customTransformOptions: expoBundleOptions.customTransformOptions ?? Object.create(null),\n      platform: expoBundleOptions.platform ?? 'web',\n      // @ts-expect-error: `runtimeBytecodeVersion` does not exist in `expoBundleOptions` or `TransformInputOptions`\n      runtimeBytecodeVersion: expoBundleOptions.runtimeBytecodeVersion,\n    };\n\n    const resolvedEntryFilePath = await this.resolveRelativePathAsync(filePath, {\n      resolverOptions,\n      transformOptions,\n    });\n\n    const filename = createBundleOsPath({\n      ...opts,\n      mainModuleName: resolvedEntryFilePath,\n    });\n\n    // https://github.com/facebook/metro/blob/2405f2f6c37a1b641cc379b9c733b1eff0c1c2a1/packages/metro/src/lib/parseOptionsFromUrl.js#L55-L87\n    const results = await this._bundleDirectAsync(resolvedEntryFilePath, {\n      graphOptions: {\n        lazy: expoBundleOptions.lazy ?? false,\n        shallow: expoBundleOptions.shallow ?? false,\n      },\n      resolverOptions,\n      serializerOptions: {\n        ...expoBundleOptions.serializerOptions,\n\n        inlineSourceMap: expoBundleOptions.inlineSourceMap ?? false,\n        modulesOnly: expoBundleOptions.modulesOnly ?? false,\n        runModule: expoBundleOptions.runModule ?? true,\n        // @ts-expect-error\n        sourceUrl: expoBundleOptions.sourceUrl,\n        // @ts-expect-error\n        sourceMapUrl: extraOptions.sourceMapUrl ?? expoBundleOptions.sourceMapUrl,\n      },\n      transformOptions,\n    });\n\n    return {\n      ...results,\n      filename,\n    };\n  }\n\n  private async ssrLoadModuleContents(\n    filePath: string,\n    specificOptions: Partial<ExpoMetroOptions> = {}\n  ): Promise<SSRModuleContentsResult> {\n    const { baseUrl, routerRoot, isExporting } = this.instanceMetroOptions;\n    assert(\n      baseUrl != null && routerRoot != null && isExporting != null,\n      'The server must be started before calling ssrLoadModuleContents.'\n    );\n\n    const opts: ExpoMetroOptions = {\n      // TODO: Possibly issues with using an absolute path here...\n      mainModuleName: convertPathToModuleSpecifier(filePath),\n      lazy: false,\n      asyncRoutes: false,\n      inlineSourceMap: false,\n      engine: 'hermes',\n      minify: false,\n      bytecode: false,\n      // Bundle in Node.js mode for SSR unless RSC is enabled.\n      environment: this.isReactServerComponentsEnabled ? 'react-server' : 'node',\n      platform: 'web',\n      mode: 'development',\n      //\n      ...this.instanceMetroOptions,\n\n      // Mostly disable compiler in SSR bundles.\n      reactCompiler: false,\n      baseUrl,\n      routerRoot,\n      isExporting,\n\n      ...specificOptions,\n    };\n\n    // https://github.com/facebook/metro/blob/2405f2f6c37a1b641cc379b9c733b1eff0c1c2a1/packages/metro/src/lib/parseOptionsFromUrl.js#L55-L87\n    const { filename, bundle, map, ...rest } = await this.metroLoadModuleContents(filePath, opts);\n    const scriptContents = wrapBundle(bundle);\n\n    if (map) {\n      debug('Registering SSR source map for:', filename);\n      cachedSourceMaps.set(filename, { url: this.projectRoot, map });\n    } else {\n      debug('No SSR source map found for:', filename);\n    }\n\n    return {\n      ...rest,\n      src: scriptContents,\n      filename,\n      map,\n    };\n  }\n\n  async nativeExportBundleAsync(\n    exp: ExpoConfig,\n    options: Omit<\n      ExpoMetroOptions,\n      'routerRoot' | 'asyncRoutes' | 'isExporting' | 'serializerOutput' | 'environment'\n    >,\n    files: ExportAssetMap,\n    extraOptions: {\n      sourceMapUrl?: string;\n      unstable_transformProfile?: TransformProfile;\n    } = {}\n  ): Promise<{\n    artifacts: SerialAsset[];\n    assets: readonly BundleAssetWithFileHashes[];\n    files?: ExportAssetMap;\n  }> {\n    if (this.isReactServerComponentsEnabled) {\n      return this.singlePageReactServerComponentExportAsync(exp, options, files, extraOptions);\n    }\n\n    return this.legacySinglePageExportBundleAsync(options, extraOptions);\n  }\n\n  private async singlePageReactServerComponentExportAsync(\n    exp: ExpoConfig,\n    options: Omit<\n      ExpoMetroOptions,\n      'baseUrl' | 'routerRoot' | 'asyncRoutes' | 'isExporting' | 'serializerOutput' | 'environment'\n    >,\n    files: ExportAssetMap,\n    extraOptions: {\n      sourceMapUrl?: string;\n      unstable_transformProfile?: TransformProfile;\n    } = {}\n  ): Promise<{\n    artifacts: SerialAsset[];\n    assets: readonly BundleAssetWithFileHashes[];\n    files: ExportAssetMap;\n  }> {\n    const getReactServerReferences = (artifacts: SerialAsset[]): string[] => {\n      // Get the React server action boundaries from the client bundle.\n      return unique(\n        artifacts\n          .filter((a) => a.type === 'js')\n          .map((artifact) =>\n            artifact.metadata.reactServerReferences?.map((ref) => fileURLToFilePath(ref))\n          )\n          // TODO: Segment by module for splitting.\n          .flat()\n          .filter(Boolean) as string[]\n      );\n    };\n\n    // NOTE(EvanBacon): This will not support any code elimination since it's a static pass.\n    let {\n      reactClientReferences: clientBoundaries,\n      reactServerReferences: serverActionReferencesInServer,\n      cssModules,\n    } = await this.rscRenderer!.getExpoRouterClientReferencesAsync(\n      {\n        platform: options.platform,\n        domRoot: options.domRoot,\n      },\n      files\n    );\n\n    // TODO: The output keys should be in production format or use a lookup manifest.\n\n    const processClientBoundaries = async (\n      reactServerReferences: string[]\n    ): Promise<{\n      artifacts: SerialAsset[];\n      assets: readonly BundleAssetWithFileHashes[];\n    }> => {\n      debug('Evaluated client boundaries:', clientBoundaries);\n\n      // Run metro bundler and create the JS bundles/source maps.\n      const bundle = await this.legacySinglePageExportBundleAsync(\n        {\n          ...options,\n          clientBoundaries,\n        },\n        extraOptions\n      );\n\n      // Get the React server action boundaries from the client bundle.\n      const newReactServerReferences = getReactServerReferences(bundle.artifacts);\n\n      if (!newReactServerReferences) {\n        // Possible issue with babel plugin / metro-config.\n        throw new Error(\n          'Static server action references were not returned from the Metro client bundle'\n        );\n      }\n      debug('React server action boundaries from client:', newReactServerReferences);\n\n      const allKnownReactServerReferences = unique([\n        ...reactServerReferences,\n        ...newReactServerReferences,\n      ]);\n\n      // When we export the server actions that were imported from the client, we may need to re-bundle the client with the new client boundaries.\n      const { clientBoundaries: nestedClientBoundaries } =\n        await this.rscRenderer!.exportServerActionsAsync(\n          {\n            platform: options.platform,\n            domRoot: options.domRoot,\n            entryPoints: allKnownReactServerReferences,\n          },\n          files\n        );\n\n      // TODO: Check against all modules in the initial client bundles.\n      const hasUniqueClientBoundaries = nestedClientBoundaries.some(\n        (boundary) => !clientBoundaries.includes(boundary)\n      );\n\n      if (!hasUniqueClientBoundaries) {\n        return bundle;\n      }\n\n      debug('Re-bundling client with nested client boundaries:', nestedClientBoundaries);\n\n      clientBoundaries = unique(clientBoundaries.concat(nestedClientBoundaries));\n\n      // Re-bundle the client with the new client boundaries that only exist in server actions that were imported from the client.\n      // Run metro bundler and create the JS bundles/source maps.\n      return processClientBoundaries(allKnownReactServerReferences);\n    };\n\n    const bundle = await processClientBoundaries(serverActionReferencesInServer);\n\n    // Inject the global CSS that was imported during the server render.\n    bundle.artifacts.push(...cssModules);\n\n    const serverRoot = getMetroServerRoot(this.projectRoot);\n\n    // HACK: Maybe this should be done in the serializer.\n    const clientBoundariesAsOpaqueIds = clientBoundaries.map((boundary) =>\n      // NOTE(cedric): relative module specifiers / IDs should always be POSIX formatted\n      toPosixPath(path.relative(serverRoot, boundary))\n    );\n    const moduleIdToSplitBundle = (\n      bundle.artifacts\n        .map((artifact) => artifact?.metadata?.paths && Object.values(artifact.metadata.paths))\n        .filter(Boolean)\n        .flat() as Record<string, string>[]\n    ).reduce((acc, paths) => ({ ...acc, ...paths }), {});\n\n    debug('SSR Manifest:', moduleIdToSplitBundle, clientBoundariesAsOpaqueIds);\n\n    const ssrManifest = new Map<string, string>();\n\n    if (Object.keys(moduleIdToSplitBundle).length) {\n      clientBoundariesAsOpaqueIds.forEach((boundary) => {\n        if (boundary in moduleIdToSplitBundle) {\n          ssrManifest.set(boundary, moduleIdToSplitBundle[boundary]);\n        } else {\n          throw new Error(\n            `Could not find boundary \"${boundary}\" in the SSR manifest. Available: ${Object.keys(moduleIdToSplitBundle).join(', ')}`\n          );\n        }\n      });\n    } else {\n      // Native apps with bundle splitting disabled.\n      debug('No split bundles');\n      clientBoundariesAsOpaqueIds.forEach((boundary) => {\n        // @ts-expect-error\n        ssrManifest.set(boundary, null);\n      });\n    }\n\n    const routerOptions = exp.extra?.router;\n\n    // Export the static RSC files\n    await this.rscRenderer!.exportRoutesAsync(\n      {\n        platform: options.platform,\n        ssrManifest,\n        routerOptions,\n      },\n      files\n    );\n\n    // Save the SSR manifest so we can perform more replacements in the server renderer and with server actions.\n    files.set(`_expo/rsc/${options.platform}/ssr-manifest.js`, {\n      targetDomain: 'server',\n      contents:\n        'module.exports = ' +\n        JSON.stringify(\n          // TODO: Add a less leaky version of this across the framework with just [key, value] (module ID, chunk).\n          Object.fromEntries(\n            Array.from(ssrManifest.entries()).map(([key, value]) => [\n              // Must match babel plugin.\n              './' + toPosixPath(path.relative(this.projectRoot, path.join(serverRoot, key))),\n              [key, value],\n            ])\n          )\n        ),\n    });\n\n    return { ...bundle, files };\n  }\n\n  async legacySinglePageExportBundleAsync(\n    options: Omit<\n      ExpoMetroOptions,\n      'routerRoot' | 'asyncRoutes' | 'isExporting' | 'serializerOutput' | 'environment' | 'hosted'\n    >,\n    extraOptions: {\n      sourceMapUrl?: string;\n      unstable_transformProfile?: TransformProfile;\n    } = {}\n  ): Promise<{ artifacts: SerialAsset[]; assets: readonly BundleAssetWithFileHashes[] }> {\n    const { baseUrl, routerRoot, isExporting } = this.instanceMetroOptions;\n    assert(options.mainModuleName != null, 'mainModuleName must be provided in options.');\n    assert(\n      baseUrl != null && routerRoot != null && isExporting != null,\n      'The server must be started before calling legacySinglePageExportBundleAsync.'\n    );\n\n    const opts: ExpoMetroOptions = {\n      ...this.instanceMetroOptions,\n      baseUrl,\n      routerRoot,\n      isExporting,\n      ...options,\n      environment: 'client',\n      serializerOutput: 'static',\n    };\n\n    // https://github.com/facebook/metro/blob/2405f2f6c37a1b641cc379b9c733b1eff0c1c2a1/packages/metro/src/lib/parseOptionsFromUrl.js#L55-L87\n    if (!opts.mainModuleName.startsWith('/') && !path.isAbsolute(opts.mainModuleName)) {\n      opts.mainModuleName = './' + opts.mainModuleName;\n    }\n\n    const output = await this.metroLoadModuleContents(opts.mainModuleName, opts, extraOptions);\n\n    return {\n      artifacts: output.artifacts!,\n      assets: output.assets!,\n    };\n  }\n\n  async watchEnvironmentVariables() {\n    if (!this.instance) {\n      throw new Error(\n        'Cannot observe environment variable changes without a running Metro instance.'\n      );\n    }\n    if (!this.metro) {\n      // This can happen when the run command is used and the server is already running in another\n      // process.\n      debug('Skipping Environment Variable observation because Metro is not running (headless).');\n      return;\n    }\n\n    const envFiles = runtimeEnv\n      .getFiles(process.env.NODE_ENV)\n      .map((fileName) => path.join(this.projectRoot, fileName));\n\n    observeFileChanges(\n      {\n        metro: this.metro,\n        server: this.instance.server,\n      },\n      envFiles,\n      () => {\n        debug('Reloading environment variables...');\n        // Force reload the environment variables.\n        runtimeEnv.load(this.projectRoot, { force: true });\n      }\n    );\n  }\n\n  rscRenderer: Awaited<ReturnType<typeof createServerComponentsMiddleware>> | null = null;\n\n  protected async startImplementationAsync(\n    options: BundlerStartOptions\n  ): Promise<DevServerInstance> {\n    options.port = await this.resolvePortAsync(options);\n    this.urlCreator = this.getUrlCreator(options);\n\n    const config = getConfig(this.projectRoot, { skipSDKVersionRequirement: true });\n    const { exp } = config;\n    // NOTE: This will change in the future when it's less experimental, we enable React 19, and turn on more RSC flags by default.\n    const isReactServerComponentsEnabled =\n      !!exp.experiments?.reactServerComponentRoutes || !!exp.experiments?.reactServerFunctions;\n    const isReactServerActionsOnlyEnabled =\n      !exp.experiments?.reactServerComponentRoutes && !!exp.experiments?.reactServerFunctions;\n    this.isReactServerComponentsEnabled = isReactServerComponentsEnabled;\n    this.isReactServerRoutesEnabled = !!exp.experiments?.reactServerComponentRoutes;\n\n    const useServerRendering = ['static', 'server'].includes(exp.web?.output ?? '');\n    const hasApiRoutes = isReactServerComponentsEnabled || exp.web?.output === 'server';\n    const baseUrl = getBaseUrlFromExpoConfig(exp);\n    const asyncRoutes = getAsyncRoutesFromExpoConfig(exp, options.mode ?? 'development', 'web');\n    const routerRoot = getRouterDirectoryModuleIdWithManifest(this.projectRoot, exp);\n    const reactCompiler = !!exp.experiments?.reactCompiler;\n    const appDir = path.join(this.projectRoot, routerRoot);\n    const mode = options.mode ?? 'development';\n\n    const routerOptions = exp.extra?.router;\n\n    if (isReactServerComponentsEnabled && exp.web?.output === 'static') {\n      throw new CommandError(\n        `Experimental server component support does not support 'web.output: ${exp.web!.output}' yet. Use 'web.output: \"server\"' during the experimental phase.`\n      );\n    }\n\n    // Error early about the window.location polyfill when React Server Components are enabled.\n    if (isReactServerComponentsEnabled && exp?.extra?.router?.origin === false) {\n      const configPath = config.dynamicConfigPath ?? config.staticConfigPath ?? '/app.json';\n      const configFileName = path.basename(configPath);\n      throw new CommandError(\n        `The Expo Router \"origin\" property in the Expo config (${configFileName}) cannot be \"false\" when React Server Components is enabled. Remove it from the ${configFileName} file and try again.`\n      );\n    }\n\n    const instanceMetroOptions = {\n      isExporting: !!options.isExporting,\n      baseUrl,\n      mode,\n      routerRoot,\n      reactCompiler,\n      minify: options.minify,\n      asyncRoutes,\n      // Options that are changing between platforms like engine, platform, and environment aren't set here.\n    };\n    this.instanceMetroOptions = instanceMetroOptions;\n\n    const parsedOptions = {\n      port: options.port,\n      maxWorkers: options.maxWorkers,\n      resetCache: options.resetDevServer,\n    };\n\n    // Required for symbolication:\n    process.env.EXPO_DEV_SERVER_ORIGIN = `http://localhost:${options.port}`;\n\n    const { metro, hmrServer, server, middleware, messageSocket } = await instantiateMetroAsync(\n      this,\n      parsedOptions,\n      {\n        isExporting: !!options.isExporting,\n        exp,\n      }\n    );\n\n    if (!options.isExporting) {\n      const manifestMiddleware = await this.getManifestMiddlewareAsync(options);\n\n      // Important that we noop source maps for context modules as soon as possible.\n      prependMiddleware(middleware, new ContextModuleSourceMapsMiddleware().getHandler());\n\n      // We need the manifest handler to be the first middleware to run so our\n      // routes take precedence over static files. For example, the manifest is\n      // served from '/' and if the user has an index.html file in their project\n      // then the manifest handler will never run, the static middleware will run\n      // and serve index.html instead of the manifest.\n      // https://github.com/expo/expo/issues/13114\n      prependMiddleware(middleware, manifestMiddleware.getHandler());\n\n      middleware.use(\n        new InterstitialPageMiddleware(this.projectRoot, {\n          // TODO: Prevent this from becoming stale.\n          scheme: options.location.scheme ?? null,\n        }).getHandler()\n      );\n      middleware.use(\n        new DevToolsPluginMiddleware(this.projectRoot, this.devToolsPluginManager).getHandler()\n      );\n\n      const deepLinkMiddleware = new RuntimeRedirectMiddleware(this.projectRoot, {\n        getLocation: ({ runtime }) => {\n          if (runtime === 'custom') {\n            return this.urlCreator?.constructDevClientUrl();\n          } else {\n            return this.urlCreator?.constructUrl({\n              scheme: 'exp',\n            });\n          }\n        },\n      });\n      middleware.use(deepLinkMiddleware.getHandler());\n\n      const serverRoot = getMetroServerRoot(this.projectRoot);\n\n      const domComponentRenderer = createDomComponentsMiddleware(\n        {\n          metroRoot: serverRoot,\n          projectRoot: this.projectRoot,\n        },\n        instanceMetroOptions\n      );\n      // Add support for DOM components.\n      // TODO: Maybe put behind a flag for now?\n      middleware.use(domComponentRenderer);\n\n      middleware.use(new CreateFileMiddleware(this.projectRoot).getHandler());\n\n      // Append support for redirecting unhandled requests to the index.html page on web.\n      if (this.isTargetingWeb()) {\n        // This MUST be after the manifest middleware so it doesn't have a chance to serve the template `public/index.html`.\n        middleware.use(new ServeStaticMiddleware(this.projectRoot).getHandler());\n\n        // This should come after the static middleware so it doesn't serve the favicon from `public/favicon.ico`.\n        middleware.use(new FaviconMiddleware(this.projectRoot).getHandler());\n      }\n\n      if (useServerRendering || isReactServerComponentsEnabled) {\n        observeAnyFileChanges(\n          {\n            metro,\n            server,\n          },\n          (events) => {\n            if (hasApiRoutes) {\n              // NOTE(EvanBacon): We aren't sure what files the API routes are using so we'll just invalidate\n              // aggressively to ensure we always have the latest. The only caching we really get here is for\n              // cases where the user is making subsequent requests to the same API route without changing anything.\n              // This is useful for testing but pretty suboptimal. Luckily our caching is pretty aggressive so it makes\n              // up for a lot of the overhead.\n              this.invalidateApiRouteCache();\n            } else if (!hasWarnedAboutApiRoutes()) {\n              for (const event of events) {\n                if (\n                  // If the user did not delete a file that matches the Expo Router API Route convention, then we should warn that\n                  // API Routes are not enabled in the project.\n                  event.metadata?.type !== 'd' &&\n                  // Ensure the file is in the project's routes directory to prevent false positives in monorepos.\n                  event.filePath.startsWith(appDir) &&\n                  isApiRouteConvention(event.filePath)\n                ) {\n                  warnInvalidWebOutput();\n                }\n              }\n            }\n          }\n        );\n      }\n\n      // If React 19 is enabled, then add RSC middleware to the dev server.\n      if (isReactServerComponentsEnabled) {\n        this.bindRSCDevModuleInjectionHandler();\n        const rscMiddleware = createServerComponentsMiddleware(this.projectRoot, {\n          instanceMetroOptions: this.instanceMetroOptions,\n          rscPath: '/_flight',\n          ssrLoadModule: this.ssrLoadModule.bind(this),\n          ssrLoadModuleArtifacts: this.metroImportAsArtifactsAsync.bind(this),\n          useClientRouter: isReactServerActionsOnlyEnabled,\n          createModuleId: metro._createModuleId.bind(metro),\n          routerOptions,\n        });\n        this.rscRenderer = rscMiddleware;\n        middleware.use(rscMiddleware.middleware);\n        this.onReloadRscEvent = rscMiddleware.onReloadRscEvent;\n      }\n\n      // Append support for redirecting unhandled requests to the index.html page on web.\n      if (this.isTargetingWeb()) {\n        if (!useServerRendering) {\n          // This MUST run last since it's the fallback.\n          middleware.use(\n            new HistoryFallbackMiddleware(manifestMiddleware.getHandler().internal).getHandler()\n          );\n        } else {\n          middleware.use(\n            createRouteHandlerMiddleware(this.projectRoot, {\n              appDir,\n              routerRoot,\n              config,\n              ...config.exp.extra?.router,\n              bundleApiRoute: (functionFilePath) =>\n                this.ssrImportApiRoute(functionFilePath, { platform: 'web' }),\n              getStaticPageAsync: async (pathname) => {\n                // TODO: Add server rendering when RSC is enabled.\n                if (isReactServerComponentsEnabled) {\n                  // NOTE: This is a temporary hack to return the SPA/template index.html in development when RSC is enabled.\n                  // While this technically works, it doesn't provide the correct experience of server rendering the React code to HTML first.\n                  const html = await manifestMiddleware.getSingleHtmlTemplateAsync();\n                  return { content: html };\n                }\n\n                // Non-RSC apps will bundle the static HTML for a given pathname and respond with it.\n                return this.getStaticPageAsync(pathname);\n              },\n            })\n          );\n        }\n      }\n    } else {\n      // If React 19 is enabled, then add RSC middleware to the dev server.\n      if (isReactServerComponentsEnabled) {\n        this.bindRSCDevModuleInjectionHandler();\n        const rscMiddleware = createServerComponentsMiddleware(this.projectRoot, {\n          instanceMetroOptions: this.instanceMetroOptions,\n          rscPath: '/_flight',\n          ssrLoadModule: this.ssrLoadModule.bind(this),\n          ssrLoadModuleArtifacts: this.metroImportAsArtifactsAsync.bind(this),\n          useClientRouter: isReactServerActionsOnlyEnabled,\n          createModuleId: metro._createModuleId.bind(metro),\n          routerOptions,\n        });\n        this.rscRenderer = rscMiddleware;\n      }\n    }\n    // Extend the close method to ensure that we clean up the local info.\n    const originalClose = server.close.bind(server);\n\n    server.close = (callback?: (err?: Error) => void) => {\n      return originalClose((err?: Error) => {\n        this.instance = null;\n        this.metro = null;\n        this.hmrServer = null;\n        this.ssrHmrClients = new Map();\n        callback?.(err);\n      });\n    };\n\n    this.metro = metro;\n    this.hmrServer = hmrServer;\n    return {\n      server,\n      location: {\n        // The port is the main thing we want to send back.\n        port: options.port,\n        // localhost isn't always correct.\n        host: 'localhost',\n        // http is the only supported protocol on native.\n        url: `http://localhost:${options.port}`,\n        protocol: 'http',\n      },\n      middleware,\n      messageSocket,\n    };\n  }\n\n  private onReloadRscEvent: ((platform: string) => void) | null = null;\n\n  private async registerSsrHmrAsync(url: string, onReload: (platform: string[]) => void) {\n    if (!this.hmrServer || this.ssrHmrClients.has(url)) {\n      return;\n    }\n\n    debug('[SSR] Register HMR:', url);\n\n    const sendFn = (message: string) => {\n      const data = JSON.parse(String(message)) as { type: string; body: any };\n\n      switch (data.type) {\n        case 'bundle-registered':\n        case 'update-done':\n        case 'update-start':\n          break;\n        case 'update':\n          {\n            const update = data.body;\n            const {\n              isInitialUpdate,\n              added,\n              modified,\n              deleted,\n            }: {\n              isInitialUpdate?: boolean;\n              added: {\n                module: [number | string, string];\n                sourceURL: string;\n                sourceMappingURL: string;\n              }[];\n              modified: {\n                module: [number | string, string];\n                sourceURL: string;\n                sourceMappingURL: string;\n              }[];\n              deleted: (number | string)[];\n            } = update;\n\n            const hasUpdate = added.length || modified.length || deleted.length;\n\n            // NOTE: We throw away the updates and instead simply send a trigger to the client to re-fetch the server route.\n            if (!isInitialUpdate && hasUpdate) {\n              // Clear all SSR modules before sending the reload event. This ensures that the next event will rebuild the in-memory state from scratch.\n              // @ts-expect-error\n              if (typeof globalThis.__c === 'function') globalThis.__c();\n\n              const allModuleIds = new Set(\n                [...added, ...modified].map((m) => m.module[0]).concat(deleted)\n              );\n\n              const platforms = unique(\n                Array.from(allModuleIds)\n                  .map((moduleId) => {\n                    if (typeof moduleId !== 'string') {\n                      return null;\n                    }\n                    // Extract platforms from the module IDs.\n                    return moduleId.match(/[?&]platform=([\\w]+)/)?.[1] ?? null;\n                  })\n                  .filter(Boolean)\n              ) as string[];\n\n              onReload(platforms);\n            }\n          }\n          break;\n        case 'error':\n          // GraphNotFound can mean that we have an issue in metroOptions where the URL doesn't match the object props.\n          Log.error('[SSR] HMR Error: ' + JSON.stringify(data, null, 2));\n\n          if (data.body?.type === 'GraphNotFoundError') {\n            Log.error(\n              'Available SSR HMR keys:',\n              // @ts-expect-error\n              (this.metro?._bundler._revisionsByGraphId as Map).keys()\n            );\n          }\n          break;\n        default:\n          debug('Unknown HMR message:', data);\n          break;\n      }\n    };\n\n    const client = await this.hmrServer!.onClientConnect(url, sendFn);\n    this.ssrHmrClients.set(url, client);\n    // Opt in...\n    client.optedIntoHMR = true;\n    await this.hmrServer!._registerEntryPoint(client, url, sendFn);\n  }\n\n  public async waitForTypeScriptAsync(): Promise<boolean> {\n    if (!this.instance) {\n      throw new Error('Cannot wait for TypeScript without a running server.');\n    }\n\n    return new Promise<boolean>((resolve) => {\n      if (!this.metro) {\n        // This can happen when the run command is used and the server is already running in another\n        // process. In this case we can't wait for the TypeScript check to complete because we don't\n        // have access to the Metro server.\n        debug('Skipping TypeScript check because Metro is not running (headless).');\n        return resolve(false);\n      }\n\n      const off = metroWatchTypeScriptFiles({\n        projectRoot: this.projectRoot,\n        server: this.instance!.server,\n        metro: this.metro,\n        tsconfig: true,\n        throttle: true,\n        eventTypes: ['change', 'add'],\n        callback: async () => {\n          // Run once, this prevents the TypeScript project prerequisite from running on every file change.\n          off();\n          const { TypeScriptProjectPrerequisite } = await import(\n            '../../doctor/typescript/TypeScriptProjectPrerequisite.js'\n          );\n\n          try {\n            const req = new TypeScriptProjectPrerequisite(this.projectRoot);\n            await req.bootstrapAsync();\n            resolve(true);\n          } catch (error: any) {\n            // Ensure the process doesn't fail if the TypeScript check fails.\n            // This could happen during the install.\n            Log.log();\n            Log.error(\n              chalk.red`Failed to automatically setup TypeScript for your project. Try restarting the dev server to fix.`\n            );\n            Log.exception(error);\n            resolve(false);\n          }\n        },\n      });\n    });\n  }\n\n  public async startTypeScriptServices() {\n    return startTypescriptTypeGenerationAsync({\n      server: this.instance?.server,\n      metro: this.metro,\n      projectRoot: this.projectRoot,\n    });\n  }\n\n  protected getConfigModuleIds(): string[] {\n    return ['./metro.config.js', './metro.config.json', './rn-cli.config.js'];\n  }\n\n  // API Routes\n\n  private pendingRouteOperations = new Map<string, Promise<SSRModuleContentsResult | null>>();\n\n  // Bundle the API Route with Metro and return the string contents to be evaluated in the server.\n  private async bundleApiRoute(\n    filePath: string,\n    { platform }: { platform: string }\n  ): Promise<SSRModuleContentsResult | null | undefined> {\n    if (this.pendingRouteOperations.has(filePath)) {\n      return this.pendingRouteOperations.get(filePath);\n    }\n    const bundleAsync = async (): Promise<SSRModuleContentsResult> => {\n      try {\n        debug('Bundle API route:', this.instanceMetroOptions.routerRoot, filePath);\n        return await this.ssrLoadModuleContents(filePath, {\n          isExporting: this.instanceMetroOptions.isExporting,\n          platform,\n        });\n      } catch (error: any) {\n        const appDir = this.instanceMetroOptions?.routerRoot\n          ? path.join(this.projectRoot, this.instanceMetroOptions!.routerRoot!)\n          : undefined;\n        const relativePath = appDir ? path.relative(appDir, filePath) : filePath;\n\n        // Expected errors: invalid syntax, missing resolutions.\n        // Wrap with command error for better error messages.\n        const err = new CommandError(\n          'API_ROUTE',\n          chalk`Failed to bundle API Route: {bold ${relativePath}}\\n\\n` + error.message\n        );\n\n        for (const key in error) {\n          // @ts-expect-error\n          err[key] = error[key];\n        }\n\n        throw err;\n      } finally {\n        // pendingRouteOperations.delete(filepath);\n      }\n    };\n    const route = bundleAsync();\n\n    this.pendingRouteOperations.set(filePath, route);\n    return route;\n  }\n\n  private async ssrImportApiRoute(\n    filePath: string,\n    { platform }: { platform: string }\n  ): Promise<null | Record<string, Function> | Response> {\n    // TODO: Cache the evaluated function.\n    try {\n      const apiRoute = await this.bundleApiRoute(filePath, { platform });\n\n      if (!apiRoute?.src) {\n        return null;\n      }\n      return evalMetroNoHandling(this.projectRoot, apiRoute.src, apiRoute.filename);\n    } catch (error) {\n      // Format any errors that were thrown in the global scope of the evaluation.\n      if (error instanceof Error) {\n        try {\n          const htmlServerError = await getErrorOverlayHtmlAsync({\n            error,\n            projectRoot: this.projectRoot,\n            routerRoot: this.instanceMetroOptions.routerRoot!,\n          });\n\n          return new Response(htmlServerError, {\n            status: 500,\n            headers: {\n              'Content-Type': 'text/html',\n            },\n          });\n        } catch (internalError) {\n          debug('Failed to generate Metro server error UI for API Route error:', internalError);\n          throw error;\n        }\n      } else {\n        throw error;\n      }\n    }\n  }\n\n  private invalidateApiRouteCache() {\n    this.pendingRouteOperations.clear();\n  }\n\n  // Ensure the global is available for SSR CSS modules to inject client updates.\n  private bindRSCDevModuleInjectionHandler() {\n    // Used by SSR CSS modules to broadcast client updates.\n    // @ts-expect-error\n    globalThis.__expo_rsc_inject_module = this.sendClientModule.bind(this);\n  }\n\n  // NOTE: This can only target a single platform at a time (web).\n  // used for sending RSC CSS to the root client in development.\n  private sendClientModule({ code, id }: { code: string; id: string }) {\n    this.broadcastMessage('sendDevCommand', {\n      name: 'module-import',\n      data: {\n        code,\n        id,\n      },\n    });\n  }\n\n  // Metro HMR\n\n  private setupHmr(url: URL) {\n    const onReload = (platforms: string[] = []) => {\n      // Send reload command to client from Fast Refresh code.\n\n      if (!platforms.length) {\n        // TODO: When is this called?\n        this.broadcastMessage('sendDevCommand', {\n          name: 'rsc-reload',\n        });\n      } else {\n        for (const platform of platforms) {\n          this.onReloadRscEvent?.(platform);\n          this.broadcastMessage('sendDevCommand', {\n            name: 'rsc-reload',\n            platform,\n          });\n        }\n      }\n    };\n\n    this.registerSsrHmrAsync(url.toString(), onReload);\n  }\n\n  // Direct Metro access\n\n  // Emulates the Metro dev server .bundle endpoint without having to go through a server.\n  private async _bundleDirectAsync(\n    resolvedEntryFilePath: string,\n    {\n      transformOptions,\n      resolverOptions,\n      graphOptions,\n      serializerOptions,\n    }: {\n      transformOptions: TransformInputOptions;\n      resolverOptions: {\n        customResolverOptions: CustomResolverOptions;\n        dev: boolean;\n      };\n      serializerOptions: {\n        modulesOnly: boolean;\n        runModule: boolean;\n        sourceMapUrl: string;\n        sourceUrl: string;\n        inlineSourceMap: boolean;\n        excludeSource: boolean;\n      };\n      graphOptions: {\n        shallow: boolean;\n        lazy: boolean;\n      };\n    }\n  ): Promise<BundleDirectResult> {\n    assert(this.metro, 'Metro server must be running to bundle directly.');\n    const config = this.metro._config;\n    const buildNumber = this.metro.getNewBuildNumber();\n    const bundlePerfLogger = config.unstable_perfLoggerFactory?.('BUNDLING_REQUEST', {\n      key: buildNumber,\n    });\n\n    const onProgress = (transformedFileCount: number, totalFileCount: number) => {\n      this.metro?._reporter?.update?.({\n        buildID: getBuildID(buildNumber),\n        type: 'bundle_transform_progressed',\n        transformedFileCount,\n        totalFileCount,\n      });\n    };\n\n    const revPromise = this.getMetroRevision(resolvedEntryFilePath, {\n      graphOptions,\n      transformOptions,\n      resolverOptions,\n    });\n\n    bundlePerfLogger?.point('resolvingAndTransformingDependencies_start');\n    bundlePerfLogger?.annotate({\n      bool: {\n        initial_build: revPromise == null,\n      },\n    });\n    this.metro?._reporter.update({\n      buildID: getBuildID(buildNumber),\n      bundleDetails: {\n        bundleType: transformOptions.type,\n        dev: transformOptions.dev,\n        entryFile: resolvedEntryFilePath,\n        minify: transformOptions.minify,\n        platform: transformOptions.platform,\n        customResolverOptions: resolverOptions.customResolverOptions,\n        customTransformOptions: transformOptions.customTransformOptions ?? {},\n      },\n      isPrefetch: false,\n      type: 'bundle_build_started',\n    });\n\n    try {\n      let delta: DeltaResult;\n      let revision: GraphRevision;\n\n      try {\n        // TODO: Some bug in Metro/RSC causes this to break when changing imports in server components.\n        // We should resolve the bug because it results in ~6x faster bundling to reuse the graph revision.\n        if (transformOptions.customTransformOptions?.environment === 'react-server') {\n          const props = await this.metro.getBundler().initializeGraph(\n            // NOTE: Using absolute path instead of relative input path is a breaking change.\n            // entryFile,\n            resolvedEntryFilePath,\n\n            transformOptions,\n            resolverOptions,\n            {\n              onProgress,\n              shallow: graphOptions.shallow,\n              lazy: graphOptions.lazy,\n            }\n          );\n          delta = props.delta;\n          revision = props.revision;\n        } else {\n          const props = await (revPromise != null\n            ? this.metro.getBundler().updateGraph(await revPromise, false)\n            : this.metro.getBundler().initializeGraph(\n                // NOTE: Using absolute path instead of relative input path is a breaking change.\n                // entryFile,\n                resolvedEntryFilePath,\n\n                transformOptions,\n                resolverOptions,\n                {\n                  onProgress,\n                  shallow: graphOptions.shallow,\n                  lazy: graphOptions.lazy,\n                }\n              ));\n          delta = props.delta;\n          revision = props.revision;\n        }\n      } catch (error) {\n        attachImportStackToRootMessage(error);\n        dropStackIfContainsCodeFrame(error);\n        throw error;\n      }\n\n      bundlePerfLogger?.annotate({\n        int: {\n          graph_node_count: revision.graph.dependencies.size,\n        },\n      });\n      bundlePerfLogger?.point('resolvingAndTransformingDependencies_end');\n      bundlePerfLogger?.point('serializingBundle_start');\n\n      const shouldAddToIgnoreList = this.metro._shouldAddModuleToIgnoreList.bind(this.metro);\n\n      const serializer = this.getMetroSerializer();\n\n      const bundle = await serializer(\n        // NOTE: Using absolute path instead of relative input path is a breaking change.\n        // entryFile,\n        resolvedEntryFilePath,\n\n        revision.prepend as any,\n        revision.graph as any,\n        {\n          asyncRequireModulePath: await this.metro._resolveRelativePath(\n            config.transformer.asyncRequireModulePath,\n            {\n              relativeTo: 'project',\n              resolverOptions,\n              transformOptions,\n            }\n          ),\n          // ...serializerOptions,\n          processModuleFilter: config.serializer.processModuleFilter,\n          createModuleId: this.metro._createModuleId,\n          getRunModuleStatement: config.serializer.getRunModuleStatement,\n          includeAsyncPaths: graphOptions.lazy,\n          dev: transformOptions.dev,\n          projectRoot: config.projectRoot,\n          modulesOnly: serializerOptions.modulesOnly,\n          runBeforeMainModule: config.serializer.getModulesRunBeforeMainModule(\n            resolvedEntryFilePath\n            // path.relative(config.projectRoot, entryFile)\n          ),\n          runModule: serializerOptions.runModule,\n          sourceMapUrl: serializerOptions.sourceMapUrl,\n          sourceUrl: serializerOptions.sourceUrl,\n          inlineSourceMap: serializerOptions.inlineSourceMap,\n          serverRoot: config.server.unstable_serverRoot ?? config.projectRoot,\n          shouldAddToIgnoreList,\n\n          // @ts-expect-error: passed to our serializer to enable non-serial return values.\n          serializerOptions,\n        }\n      );\n\n      this.metro._reporter.update({\n        buildID: getBuildID(buildNumber),\n        type: 'bundle_build_done',\n      });\n\n      bundlePerfLogger?.point('serializingBundle_end');\n\n      let bundleCode: string | null = null;\n      let bundleMap: string | null = null;\n\n      // @ts-expect-error: If the output is multi-bundle...\n      if (serializerOptions.output === 'static') {\n        try {\n          const parsed = typeof bundle === 'string' ? JSON.parse(bundle) : bundle;\n\n          assert(\n            'artifacts' in parsed && Array.isArray(parsed.artifacts),\n            'Expected serializer to return an object with key artifacts to contain an array of serial assets.'\n          );\n\n          const artifacts = parsed.artifacts as SerialAsset[];\n          const assets = parsed.assets;\n\n          const bundleCode = artifacts.filter((asset) => asset.type === 'js')[0];\n          const bundleMap = artifacts.filter((asset) => asset.type === 'map')?.[0]?.source ?? '';\n\n          return {\n            numModifiedFiles: delta.reset\n              ? delta.added.size + revision.prepend.length\n              : delta.added.size + delta.modified.size + delta.deleted.size,\n            lastModifiedDate: revision.date,\n            nextRevId: revision.id,\n            bundle: bundleCode.source,\n            map: bundleMap,\n            artifacts,\n            assets,\n          };\n        } catch (error: any) {\n          throw new Error(\n            'Serializer did not return expected format. The project copy of `expo/metro-config` may be out of date. Error: ' +\n              error.message\n          );\n        }\n      }\n\n      if (typeof bundle === 'string') {\n        bundleCode = bundle;\n\n        // Create the source map in a second pass...\n        let { prepend, graph } = revision;\n        if (serializerOptions.modulesOnly) {\n          prepend = [];\n        }\n\n        bundleMap = await sourceMapStringAsync(\n          [\n            //\n            ...prepend,\n            ...this.metro._getSortedModules(graph),\n          ],\n          {\n            excludeSource: serializerOptions.excludeSource,\n            processModuleFilter: config.serializer.processModuleFilter,\n            shouldAddToIgnoreList,\n          }\n        );\n      } else {\n        bundleCode = bundle.code;\n        bundleMap = bundle.map;\n      }\n\n      return {\n        numModifiedFiles: delta.reset\n          ? delta.added.size + revision.prepend.length\n          : delta.added.size + delta.modified.size + delta.deleted.size,\n        lastModifiedDate: revision.date,\n        nextRevId: revision.id,\n        bundle: bundleCode,\n        map: bundleMap,\n      };\n    } catch (error) {\n      // Mark the error so we know how to format and return it later.\n      // @ts-expect-error\n      error[IS_METRO_BUNDLE_ERROR_SYMBOL] = true;\n\n      this.metro._reporter.update({\n        buildID: getBuildID(buildNumber),\n        type: 'bundle_build_failed',\n      });\n\n      throw error;\n    }\n  }\n\n  private getMetroSerializer() {\n    return (\n      this.metro?._config?.serializer.customSerializer ||\n      ((entryPoint, preModules, graph, options) =>\n        bundleToString(baseJSBundle(entryPoint, preModules, graph, options)).code)\n    );\n  }\n\n  private getMetroRevision(\n    resolvedEntryFilePath: string,\n    {\n      graphOptions,\n      transformOptions,\n      resolverOptions,\n    }: {\n      transformOptions: TransformInputOptions;\n      resolverOptions: {\n        customResolverOptions: CustomResolverOptions;\n        dev: boolean;\n      };\n      graphOptions: {\n        shallow: boolean;\n        lazy: boolean;\n      };\n    }\n  ) {\n    assert(this.metro, 'Metro server must be running to bundle directly.');\n    const config = this.metro._config;\n\n    const graphId = getGraphId(resolvedEntryFilePath, transformOptions, {\n      unstable_allowRequireContext: config.transformer.unstable_allowRequireContext,\n      resolverOptions,\n      shallow: graphOptions.shallow,\n      lazy: graphOptions.lazy,\n    });\n    return this.metro.getBundler().getRevisionByGraphId(graphId);\n  }\n\n  private async resolveRelativePathAsync(\n    moduleId: string,\n    {\n      resolverOptions,\n      transformOptions,\n    }: {\n      transformOptions: TransformInputOptions;\n      resolverOptions: {\n        customResolverOptions: CustomResolverOptions;\n        dev: boolean;\n      };\n    }\n  ) {\n    assert(this.metro, 'cannot invoke resolveRelativePathAsync without metro instance');\n    return await this.metro._resolveRelativePath(convertPathToModuleSpecifier(moduleId), {\n      relativeTo: 'server',\n      resolverOptions,\n      transformOptions,\n    });\n  }\n}\n\nfunction getBuildID(buildNumber: number): string {\n  return buildNumber.toString(36);\n}\n\nfunction wrapBundle(str: string) {\n  // Skip the metro runtime so debugging is a bit easier.\n  // Replace the __r() call with an export statement.\n  // Use gm to apply to the last require line. This is needed when the bundle has side-effects.\n  return str.replace(/^(__r\\(.*\\);)$/gm, 'module.exports = $1');\n}\n\nasync function sourceMapStringAsync(\n  modules: readonly Module[],\n  options: SourceMapGeneratorOptions\n): Promise<string> {\n  return (await sourceMapGeneratorNonBlocking(modules, options)).toString(undefined, {\n    excludeSource: options.excludeSource,\n  });\n}\n\nfunction unique<T>(array: T[]): T[] {\n  return Array.from(new Set(array));\n}\n"], "names": ["MetroBundlerDevServer", "debug", "require", "EXPO_GO_METRO_PORT", "DEV_CLIENT_METRO_PORT", "BundlerDevServer", "name", "resolvePortAsync", "options", "port", "devClient", "Number", "process", "env", "RCT_METRO_PORT", "getFreePortAsync", "exportServerRoute", "contents", "artifactFilename", "files", "includeSourceMaps", "descriptor", "src", "map", "artifactBasename", "encodeURIComponent", "path", "basename", "replace", "parsedMap", "JSON", "parse", "mapData", "stringify", "version", "sources", "source", "startsWith", "projectRoot", "relative", "convertPathToModuleSpecifier", "sourcesContent", "Array", "length", "fill", "names", "mappings", "targetDomain", "set", "fileData", "exportMiddleware", "manifest", "appDir", "outputDir", "platform", "middleware", "middlewareFilePath", "isAbsolute", "file", "join", "bundleApiRoute", "middlewareId", "exportExpoRouterApiRoutesAsync", "prerenderManifest", "routerRoot", "instanceMetroOptions", "assert", "getExpoRouterRoutesManifestAsync", "Map", "rscPath", "isReactServerComponentsEnabled", "apiRoutes", "find", "route", "page", "push", "resolveFrom", "namedRegex", "routeKeys", "rsc", "filepath", "apiRouteId", "htmlRoutes", "exp", "getConfig", "fetchManifest", "extra", "router", "preserveRedirectAndRewrites", "as<PERSON><PERSON>", "CommandError", "getServerManifestAsync", "getBuildTimeServerManifestAsync", "getManifest", "ssrLoadModule", "environment", "isReactServerRoutesEnabled", "serverManifest", "htmlManifest", "getStaticRenderFunctionAsync", "url", "getDevServerUrlOrAssert", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "preserveApiRoutes", "renderAsync", "URL", "getStaticResourcesAsync", "mainModuleName", "clientBoundaries", "mode", "minify", "isExporting", "baseUrl", "reactCompiler", "asyncRoutes", "resolvedMainModuleName", "resolveMainModuleName", "metroImportAsArtifactsAsync", "splitChunks", "EXPO_NO_BUNDLE_SPLITTING", "serializerIncludeMaps", "lazy", "EXPO_NO_METRO_LAZY", "bytecode", "getStaticPageAsync", "pathname", "devBundleUrlPathname", "createBundleUrlPath", "bundleStaticHtml", "location", "artifacts", "resources", "staticHtml", "Promise", "all", "content", "serializeHtmlWithAssets", "template", "devBundleUrl", "hydrate", "EXPO_WEB_DEV_HYDRATE", "filePath", "specificOptions", "results", "ssrLoadModuleContents", "serializerOutput", "assets", "filename", "metroLoadModuleContents", "extraOptions", "opts", "inlineSourceMap", "engine", "expoBundleOptions", "getMetroDirectBundleOptions", "resolverOptions", "customResolverOptions", "dev", "transformOptions", "hot", "type", "unstable_transformProfile", "customTransformOptions", "Object", "create", "runtimeBytecodeVersion", "resolvedEntryFilePath", "resolveRelativePathAsync", "createBundleOsPath", "_bundleDirectAsync", "graphOptions", "shallow", "serializerOptions", "modulesOnly", "runModule", "sourceUrl", "sourceMapUrl", "bundle", "rest", "scriptContents", "wrapBundle", "cachedSourceMaps", "nativeExportBundleAsync", "singlePageReactServerComponentExportAsync", "legacySinglePageExportBundleAsync", "getReactServerReferences", "unique", "filter", "a", "artifact", "metadata", "reactServerReferences", "ref", "fileURLToFilePath", "flat", "Boolean", "reactClientReferences", "serverActionReferencesInServer", "cssModules", "rsc<PERSON><PERSON><PERSON>", "getExpoRouterClientReferencesAsync", "domRoot", "processClientBoundaries", "newReactServerReferences", "Error", "allKnownReactServerReferences", "nestedClientBoundaries", "exportServerActionsAsync", "entryPoints", "hasUniqueClientBoundaries", "some", "boundary", "includes", "concat", "serverRoot", "getMetroServerRoot", "clientBoundariesAsOpaqueIds", "toPosixPath", "moduleIdToSplitBundle", "paths", "values", "reduce", "acc", "ssrManifest", "keys", "for<PERSON>ach", "routerOptions", "exportRoutesAsync", "fromEntries", "from", "entries", "key", "value", "output", "watchEnvironmentVariables", "instance", "metro", "envFiles", "runtimeEnv", "getFiles", "NODE_ENV", "fileName", "observeFileChanges", "server", "load", "force", "startImplementationAsync", "urlCreator", "getUrlCreator", "config", "skipSDKVersionRequirement", "experiments", "reactServerComponentRoutes", "reactServerFunctions", "isReactServerActionsOnlyEnabled", "useServerRendering", "web", "hasApiRoutes", "getBaseUrlFromExpoConfig", "getAsyncRoutesFromExpoConfig", "getRouterDirectoryModuleIdWithManifest", "origin", "config<PERSON><PERSON>", "dynamicConfigPath", "staticConfigPath", "configFileName", "parsedOptions", "maxWorkers", "resetCache", "resetDevServer", "EXPO_DEV_SERVER_ORIGIN", "hmrServer", "messageSocket", "instantiateMetroAsync", "manifestMiddleware", "getManifestMiddlewareAsync", "prependMiddleware", "ContextModuleSourceMapsMiddleware", "<PERSON><PERSON><PERSON><PERSON>", "use", "InterstitialPageMiddleware", "scheme", "DevToolsPluginMiddleware", "devToolsPluginManager", "deepLinkMiddleware", "RuntimeRedirectMiddleware", "getLocation", "runtime", "constructDevClientUrl", "constructUrl", "domComponent<PERSON><PERSON><PERSON>", "createDomComponentsMiddleware", "metroRoot", "CreateFileMiddleware", "isTargetingWeb", "ServeStaticMiddleware", "FaviconMiddleware", "observeAnyFileChanges", "events", "invalidateApiRouteCache", "hasWarnedAboutApiRoutes", "event", "isApiRouteConvention", "warnInvalidWebOutput", "bindRSCDevModuleInjectionHandler", "rscMiddleware", "createServerComponentsMiddleware", "bind", "ssrLoadModuleArtifacts", "useClientRouter", "createModuleId", "_createModuleId", "onReloadRscEvent", "HistoryFallbackMiddleware", "internal", "createRouteHandlerMiddleware", "functionFilePath", "ssrImportApiRoute", "html", "getSingleHtmlTemplateAsync", "originalClose", "close", "callback", "err", "ssrHmrClients", "host", "protocol", "registerSsrHmrAsync", "onReload", "has", "sendFn", "message", "data", "String", "update", "body", "isInitialUpdate", "added", "modified", "deleted", "hasUpdate", "globalThis", "__c", "allModuleIds", "Set", "m", "module", "platforms", "moduleId", "match", "Log", "error", "_bundler", "_revisionsByGraphId", "client", "onClientConnect", "optedIntoHMR", "_registerEntryPoint", "waitForTypeScriptAsync", "resolve", "off", "metroWatchTypeScriptFiles", "tsconfig", "throttle", "eventTypes", "TypeScriptProjectPrerequisite", "req", "bootstrapAsync", "log", "chalk", "red", "exception", "startTypeScriptServices", "startTypescriptTypeGenerationAsync", "getConfigModuleIds", "pendingRouteOperations", "get", "bundleAsync", "undefined", "relativePath", "apiRoute", "evalMetroNoHandling", "htmlServerError", "getErrorOverlayHtmlAsync", "Response", "status", "headers", "internalError", "clear", "__expo_rsc_inject_module", "sendClientModule", "code", "id", "broadcastMessage", "setupHmr", "toString", "_config", "buildNumber", "getNewBuildNumber", "bundlePerfLogger", "unstable_perfLoggerFactory", "onProgress", "transformedFileCount", "totalFileCount", "_reporter", "buildID", "getBuildID", "revPromise", "getMetroRevision", "point", "annotate", "bool", "initial_build", "bundleDetails", "bundleType", "entryFile", "isPrefetch", "delta", "revision", "props", "getBundler", "initializeGraph", "updateGraph", "attachImportStackToRootMessage", "dropStackIfContainsCodeFrame", "int", "graph_node_count", "graph", "dependencies", "size", "shouldAddToIgnoreList", "_shouldAddModuleToIgnoreList", "serializer", "getMetroSerializer", "prepend", "asyncRequireModulePath", "_resolveRelativePath", "transformer", "relativeTo", "processModuleFilter", "getRunModuleStatement", "includeAsyncPaths", "runBeforeMainModule", "getModulesRunBeforeMainModule", "unstable_serverRoot", "bundleCode", "bundleMap", "parsed", "isArray", "asset", "numModifiedFiles", "reset", "lastModifiedDate", "date", "nextRevId", "sourceMapStringAsync", "_getSortedModules", "excludeSource", "IS_METRO_BUNDLE_ERROR_SYMBOL", "customSerializer", "entryPoint", "preModules", "bundleToString", "baseJSBundle", "graphId", "getGraphId", "unstable_allowRequireContext", "getRevisionByGraphId", "extras", "res", "evalMetroAndWrapFunctions", "str", "modules", "sourceMapGeneratorNonBlocking", "array"], "mappings": "AAAA;;;;;CAKC;;;;+BAiIYA;;;eAAAA;;;;yBAhIyB;;;;;;;yBACH;;;;;;;iEACP;;;;;;;gEACH;;;;;;;yBAIlB;;;;;;;gEAYoB;;;;;;;gEACJ;;;;;;;gEAIJ;;;;;;;gEACD;;;;;;;gEACD;;;;;;;gEACO;;;;;;kDAKjB;6CACsC;qCACa;kCACpB;qCAM/B;2CACmC;wBAMnC;+BACiC;qDACkB;qBAMtC;sBACA;wBACS;0BACD;sBACK;kCACwC;0CAKlE;mDAC2C;sCACb;0CACI;yCACK;mCACZ;2CACQ;4CACC;oCACL;2CACI;uCACJ;8BAS/B;2BAC2B;+CACiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCnD,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,qDAAqD,GACrD,MAAMC,qBAAqB;AAE3B,iGAAiG,GACjG,MAAMC,wBAAwB;AAEvB,MAAMJ,8BAA8BK,kCAAgB;IAOzD,IAAIC,OAAe;QACjB,OAAO;IACT;IAEA,MAAMC,iBAAiBC,UAAwC,CAAC,CAAC,EAAmB;QAClF,MAAMC,OACJ,yEAAyE;QACzED,QAAQC,IAAI,IACZ,8DAA8D;QAC7DD,CAAAA,QAAQE,SAAS,GAEdC,OAAOC,QAAQC,GAAG,CAACC,cAAc,KAAKV,wBAEtC,MAAMW,IAAAA,sBAAgB,EAACZ,mBAAkB;QAE/C,OAAOM;IACT;IAEA,MAAcO,kBAAkB,EAC9BC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,EACLC,iBAAiB,EACjBC,UAAU,EAQX,EAAE;QACD,IAAI,CAACJ,UAAU;QAEf,IAAIK,MAAML,SAASK,GAAG;QACtB,IAAIF,qBAAqBH,SAASM,GAAG,EAAE;YACrC,+DAA+D;YAC/D,uHAAuH;YACvH,wDAAwD;YACxD,MAAMC,mBAAmBC,mBAAmBC,eAAI,CAACC,QAAQ,CAACT,oBAAoB;YAC9EI,MAAMA,IAAIM,OAAO,CAAC,8BAA8B,CAAC,qBAAqB,EAAEJ,kBAAkB;YAC1F,MAAMK,YAAY,OAAOZ,SAASM,GAAG,KAAK,WAAWO,KAAKC,KAAK,CAACd,SAASM,GAAG,IAAIN,SAASM,GAAG;YAC5F,MAAMS,UAAe;gBACnB,GAAGX,UAAU;gBACbJ,UAAUa,KAAKG,SAAS,CAAC;oBACvBC,SAASL,UAAUK,OAAO;oBAC1BC,SAASN,UAAUM,OAAO,CAACZ,GAAG,CAAC,CAACa;wBAC9BA,SACE,OAAOA,WAAW,YAAYA,OAAOC,UAAU,CAAC,IAAI,CAACC,WAAW,IAC5DZ,eAAI,CAACa,QAAQ,CAAC,IAAI,CAACD,WAAW,EAAEF,UAChCA;wBACN,OAAOI,IAAAA,0CAA4B,EAACJ;oBACtC;oBACAK,gBAAgB,IAAIC,MAAMb,UAAUM,OAAO,CAACQ,MAAM,EAAEC,IAAI,CAAC;oBACzDC,OAAOhB,UAAUgB,KAAK;oBACtBC,UAAUjB,UAAUiB,QAAQ;gBAC9B;gBACAC,cAAc;YAChB;YACA5B,MAAM6B,GAAG,CAAC9B,mBAAmB,QAAQc;QACvC;QACA,MAAMiB,WAAkC;YACtC,GAAG5B,UAAU;YACbJ,UAAUK;YACVyB,cAAc;QAChB;QACA5B,MAAM6B,GAAG,CAAC9B,kBAAkB+B;IAC9B;IAEA,MAAcC,iBAAiB,EAC7BC,QAAQ,EACRC,MAAM,EACNC,SAAS,EACTlC,KAAK,EACLmC,QAAQ,EACRlC,iBAAiB,EAQlB,EAAE;QACD,IAAI,CAAC+B,SAASI,UAAU,EAAE;QAE1B,MAAMC,qBAAqB9B,eAAI,CAAC+B,UAAU,CAACN,SAASI,UAAU,CAACG,IAAI,IAC/DP,SAASI,UAAU,CAACG,IAAI,GACxBhC,eAAI,CAACiC,IAAI,CAACP,QAAQD,SAASI,UAAU,CAACG,IAAI;QAC9C,MAAMzC,WAAW,MAAM,IAAI,CAAC2C,cAAc,CAACJ,oBAAoB;YAAEF;QAAS;QAC1E,MAAMpC,mBAAmBsB,IAAAA,0CAA4B,EACnDd,eAAI,CAACiC,IAAI,CAACN,WAAW3B,eAAI,CAACa,QAAQ,CAACa,QAAQI,mBAAmB5B,OAAO,CAAC,cAAc;QAGtF,MAAM,IAAI,CAACZ,iBAAiB,CAAC;YAC3BC;YACAC;YACAC;YACAC;YACAC,YAAY;gBACVwC,cAAc;YAChB;QACF;QAEA,0DAA0D;QAC1DV,SAASI,UAAU,CAACG,IAAI,GAAGxC;IAC7B;IAEA,MAAM4C,+BAA+B,EACnC1C,iBAAiB,EACjBiC,SAAS,EACTU,iBAAiB,EACjBT,QAAQ,EAOT,EAAoF;QACnF,MAAM,EAAEU,UAAU,EAAE,GAAG,IAAI,CAACC,oBAAoB;QAChDC,IAAAA,iBAAM,EACJF,cAAc,MACd;QAGF,MAAMZ,SAAS1B,eAAI,CAACiC,IAAI,CAAC,IAAI,CAACrB,WAAW,EAAE0B;QAC3C,MAAMb,WAAW,MAAM,IAAI,CAACgB,gCAAgC,CAAC;YAAEf;QAAO;QAEtE,MAAMjC,QAAwB,IAAIiD;QAElC,yBAAyB;QACzB,MAAMC,UAAU;QAEhB,IACE,IAAI,CAACC,8BAA8B,IACnC,2DAA2D;QAC3D,CAACnB,SAASoB,SAAS,CAACC,IAAI,CAAC,CAACC,QAAUA,MAAMC,IAAI,CAACrC,UAAU,CAAC,eAC1D;YACApC,MAAM,qCAAqCoE;YAC3C,wEAAwE;YACxElB,SAASoB,SAAS,CAACI,IAAI,CAAC;gBACtBjB,MAAMkB,IAAAA,sBAAW,EAAC,IAAI,CAACtC,WAAW,EAAE;gBACpCoC,MAAML;gBACNQ,YAAY;gBACZC,WAAW;oBAAEC,KAAK;gBAAM;YAC1B;QACF;QAEA,MAAM,IAAI,CAAC7B,gBAAgB,CAAC;YAC1BC;YACAC;YACAC;YACAlC;YACAmC;YACAlC;QACF;QAEA,KAAK,MAAMqD,SAAStB,SAASoB,SAAS,CAAE;YACtC,MAAMS,WAAWtD,eAAI,CAAC+B,UAAU,CAACgB,MAAMf,IAAI,IAAIe,MAAMf,IAAI,GAAGhC,eAAI,CAACiC,IAAI,CAACP,QAAQqB,MAAMf,IAAI;YACxF,MAAMzC,WAAW,MAAM,IAAI,CAAC2C,cAAc,CAACoB,UAAU;gBAAE1B;YAAS;YAEhE,MAAMpC,mBACJuD,MAAMC,IAAI,KAAKL,UAEX7B,IAAAA,0CAA4B,EAACd,eAAI,CAACiC,IAAI,CAACN,WAAW,MAAMgB,UAAU,UAClE7B,IAAAA,0CAA4B,EAC1Bd,eAAI,CAACiC,IAAI,CAACN,WAAW3B,eAAI,CAACa,QAAQ,CAACa,QAAQ4B,SAASpD,OAAO,CAAC,cAAc;YAGlF,MAAM,IAAI,CAACZ,iBAAiB,CAAC;gBAC3BC;gBACAC;gBACAC;gBACAC;gBACAC,YAAY;oBACV4D,YAAYR,MAAMC,IAAI;gBACxB;YACF;YACA,0DAA0D;YAC1DD,MAAMf,IAAI,GAAGxC;QACf;QAEA,OAAO;YACLiC,UAAU;gBACR,GAAGA,QAAQ;gBACX+B,YAAYnB,kBAAkBmB,UAAU;YAC1C;YACA/D;QACF;IACF;IAEA,MAAMgD,iCAAiC,EAAEf,MAAM,EAAsB,EAAE;YAIhE+B;QAHL,6BAA6B;QAC7B,MAAM,EAAEA,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAAC,IAAI,CAAC9C,WAAW;QAC1C,MAAMa,WAAW,MAAMkC,IAAAA,kCAAa,EAAC,IAAI,CAAC/C,WAAW,EAAE;gBAClD6C,aAAAA,IAAIG,KAAK,qBAATH,WAAWI,MAAM,AAApB;YACAC,6BAA6B;YAC7BC,QAAQ;YACRrC;QACF;QAEA,IAAI,CAACD,UAAU;YACb,MAAM,IAAIuC,oBAAY,CACpB,+BACA;QAEJ;QAEA,OAAOvC;IACT;IAEA,MAAMwC,yBAGH;YAW4DR,YACtBA;QAXvC,MAAM,EAAEA,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAAC,IAAI,CAAC9C,WAAW;QAC1C,4GAA4G;QAC5G,MAAM,EAAEsD,+BAA+B,EAAEC,WAAW,EAAE,GAAG,MAAM,IAAI,CAACC,aAAa,CAE/E,iDAAiD;YACjD,iGAAiG;YACjGC,aAAa,IAAI,CAACC,0BAA0B,GAAG,iBAAiB;QAClE;QAEA,OAAO;YACLC,gBAAgB,MAAML,gCAAgC;oBAAKT,aAAAA,IAAIG,KAAK,qBAATH,WAAWI,MAAM,AAApB;YAAqB;YAC7EW,cAAc,MAAML,YAAY;oBAAKV,cAAAA,IAAIG,KAAK,qBAATH,YAAWI,MAAM,AAApB;YAAqB;QACzD;IACF;IAEA,MAAMY,+BAIH;YAiBMhB,YAGsDA;QAnB7D,MAAMiB,MAAM,IAAI,CAACC,uBAAuB;QAExC,MAAM,EAAEC,gBAAgB,EAAET,WAAW,EAAED,+BAA+B,EAAE,GACtE,MAAM,IAAI,CAACE,aAAa,CACtB,8BACA;YACE,gGAAgG;YAChG,uEAAuE;YACvEC,aAAa;QACf;QAGJ,MAAM,EAAEZ,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAAC,IAAI,CAAC9C,WAAW;QAE1C,OAAO;YACL2D,gBAAgB,MAAML,gCAAgC;oBACjDT,aAAAA,IAAIG,KAAK,qBAATH,WAAWI,MAAM,AAApB;YACF;YACA,+BAA+B;YAC/BpC,UAAU,MAAM0C,YAAY;gBAAEU,mBAAmB;oBAAUpB,cAAAA,IAAIG,KAAK,qBAATH,YAAWI,MAAM,AAApB;YAAqB;YAC7E,gCAAgC;YAChC,MAAMiB,aAAY9E,IAAY;gBAC5B,OAAO,MAAM4E,iBAAiB,IAAIG,IAAI/E,MAAM0E;YAC9C;QACF;IACF;IAEA,MAAMM,wBAAwB,EAC5BtF,iBAAiB,EACjBuF,cAAc,EACdC,mBAAmB,IAAI,CAAC3C,oBAAoB,CAAC2C,gBAAgB,IAAI,EAAE,EACnEtD,WAAW,KAAK,EAMjB,GAAG,CAAC,CAAC,EAAE;QACN,MAAM,EAAEuD,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,aAAa,EAAEjD,UAAU,EAAEkD,WAAW,EAAE,GAClF,IAAI,CAACjD,oBAAoB;QAC3BC,IAAAA,iBAAM,EACJ2C,QAAQ,QACNE,eAAe,QACfC,WAAW,QACXhD,cAAc,QACdiD,iBAAiB,QACjBC,eAAe,MACjB;QAGF,MAAMC,yBACJR,kBAAkB,OAAOS,IAAAA,yCAAqB,EAAC,IAAI,CAAC9E,WAAW,EAAE;YAAEgB;QAAS;QAC9E,OAAO,MAAM,IAAI,CAAC+D,2BAA2B,CAACF,wBAAwB;YACpEG,aAAaP,eAAe,CAAClG,SAAG,CAAC0G,wBAAwB;YACzDjE;YACAuD;YACAC;YACAf,aAAa;YACbyB,uBAAuBpG;YACvBuF,gBAAgBQ;YAChBM,MAAM,CAAC5G,SAAG,CAAC6G,kBAAkB;YAC7BR;YACAF;YACAD;YACA/C;YACA4C;YACAK;YACAU,UAAU;QACZ;IACF;IAEA,MAAcC,mBAAmBC,QAAgB,EAAE;QACjD,MAAM,EAAEhB,IAAI,EAAEE,WAAW,EAAEH,gBAAgB,EAAEI,OAAO,EAAEC,aAAa,EAAEjD,UAAU,EAAEkD,WAAW,EAAE,GAC5F,IAAI,CAACjD,oBAAoB;QAC3BC,IAAAA,iBAAM,EACJ2C,QAAQ,QACNE,eAAe,QACfC,WAAW,QACXC,iBAAiB,QACjBjD,cAAc,QACdkD,eAAe,MACjB;QAEF,MAAM5D,WAAW;QAEjB,MAAMwE,uBAAuBC,IAAAA,iCAAmB,EAAC;YAC/CT,aAAaP,eAAe,CAAClG,SAAG,CAAC0G,wBAAwB;YACzDjE;YACAuD;YACAd,aAAa;YACbkB;YACAN,gBAAgBS,IAAAA,yCAAqB,EAAC,IAAI,CAAC9E,WAAW,EAAE;gBAAEgB;YAAS;YACnEmE,MAAM,CAAC5G,SAAG,CAAC6G,kBAAkB;YAC7BV;YACAD;YACAG;YACAlD;YACA4C;YACAe,UAAU;QACZ;QAEA,MAAMK,mBAAmB;YACvB,MAAM,EAAE1B,gBAAgB,EAAE,GAAG,MAAM,IAAI,CAACR,aAAa,CAEnD,8BAA8B;gBAC9B,gGAAgG;gBAChG,uEAAuE;gBACvEC,aAAa;gBACbe,QAAQ;gBACRC;gBACAzD;YACF;YAEA,MAAM2E,WAAW,IAAIxB,IAAIoB,UAAU,IAAI,CAACxB,uBAAuB;YAC/D,OAAO,MAAMC,iBAAiB2B;QAChC;QAEA,MAAM,CAAC,EAAEC,WAAWC,SAAS,EAAE,EAAEC,WAAW,GAAG,MAAMC,QAAQC,GAAG,CAAC;YAC/D,IAAI,CAAC5B,uBAAuB,CAAC;gBAC3BE,kBAAkB,EAAE;YACtB;YACAoB;SACD;QACD,MAAMO,UAAUC,IAAAA,sCAAuB,EAAC;YACtCzB;YACAoB;YACAM,UAAUL;YACVM,cAAcZ;YACdd;YACA2B,SAAS9H,SAAG,CAAC+H,oBAAoB;QACnC;QACA,OAAO;YACLL;YACAJ;QACF;IACF;IAgCA,MAAcd,4BACZwB,QAAgB,EAChBC,kBAAuE,CAAC,CAAC,EACzE;QACA,MAAMC,UAAU,MAAM,IAAI,CAACC,qBAAqB,CAACH,UAAU;YACzDI,kBAAkB;YAClB,GAAGH,eAAe;QACpB;QAEA,mEAAmE;QACnE,IAAIC,QAAQb,SAAS,IAAIa,QAAQG,MAAM,EAAE;YACvC,OAAO;gBACLhB,WAAWa,QAAQb,SAAS;gBAC5BgB,QAAQH,QAAQG,MAAM;gBACtB5H,KAAKyH,QAAQzH,GAAG;gBAChB6H,UAAUJ,QAAQI,QAAQ;gBAC1B5H,KAAKwH,QAAQxH,GAAG;YAClB;QACF;QACA,MAAM,IAAImE,oBAAY,CAAC,8BAA8BqD;IACvD;IAEA,MAAcK,wBACZP,QAAgB,EAChBC,eAAiC,EACjCO,eAGI,CAAC,CAAC,EAC8B;QACpC,MAAM,EAAErC,OAAO,EAAE,GAAG,IAAI,CAAC/C,oBAAoB;QAC7CC,IAAAA,iBAAM,EAAC8C,WAAW,MAAM;QAExB,MAAMsC,OAAyB;YAC7B,4DAA4D;YAC5D,4BAA4B;YAC5B7B,MAAM;YACNP,aAAa;YACbqC,iBAAiB;YACjBC,QAAQ;YACR1C,QAAQ;YACR,mBAAmB;YACnB,kCAAkC;YAClCf,aAAa;YACb,mBAAmB;YACnB,uBAAuB;YACvB,EAAE;YACF,GAAG,IAAI,CAAC9B,oBAAoB;YAC5B+C;YACA,cAAc;YACd,eAAe;YACf,GAAG8B,eAAe;QACpB;QAEA,MAAMW,oBAAoBC,IAAAA,yCAA2B,EAACJ;QAEtD,MAAMK,kBAAkB;YACtBC,uBAAuBH,kBAAkBG,qBAAqB,IAAI,CAAC;YACnEC,KAAKJ,kBAAkBI,GAAG,IAAI;QAChC;QAEA,MAAMC,mBAA0C;YAC9CD,KAAKJ,kBAAkBI,GAAG,IAAI;YAC9BE,KAAK;YACLjD,QAAQ2C,kBAAkB3C,MAAM,IAAI;YACpCkD,MAAM;YACNC,2BACEZ,aAAaY,yBAAyB,IACtCR,kBAAkBQ,yBAAyB,IAC3C;YACFC,wBAAwBT,kBAAkBS,sBAAsB,IAAIC,OAAOC,MAAM,CAAC;YAClF9G,UAAUmG,kBAAkBnG,QAAQ,IAAI;YACxC,8GAA8G;YAC9G+G,wBAAwBZ,kBAAkBY,sBAAsB;QAClE;QAEA,MAAMC,wBAAwB,MAAM,IAAI,CAACC,wBAAwB,CAAC1B,UAAU;YAC1Ec;YACAG;QACF;QAEA,MAAMX,WAAWqB,IAAAA,gCAAkB,EAAC;YAClC,GAAGlB,IAAI;YACP3C,gBAAgB2D;QAClB;QAEA,wIAAwI;QACxI,MAAMvB,UAAU,MAAM,IAAI,CAAC0B,kBAAkB,CAACH,uBAAuB;YACnEI,cAAc;gBACZjD,MAAMgC,kBAAkBhC,IAAI,IAAI;gBAChCkD,SAASlB,kBAAkBkB,OAAO,IAAI;YACxC;YACAhB;YACAiB,mBAAmB;gBACjB,GAAGnB,kBAAkBmB,iBAAiB;gBAEtCrB,iBAAiBE,kBAAkBF,eAAe,IAAI;gBACtDsB,aAAapB,kBAAkBoB,WAAW,IAAI;gBAC9CC,WAAWrB,kBAAkBqB,SAAS,IAAI;gBAC1C,mBAAmB;gBACnBC,WAAWtB,kBAAkBsB,SAAS;gBACtC,mBAAmB;gBACnBC,cAAc3B,aAAa2B,YAAY,IAAIvB,kBAAkBuB,YAAY;YAC3E;YACAlB;QACF;QAEA,OAAO;YACL,GAAGf,OAAO;YACVI;QACF;IACF;IAEA,MAAcH,sBACZH,QAAgB,EAChBC,kBAA6C,CAAC,CAAC,EACb;QAClC,MAAM,EAAE9B,OAAO,EAAEhD,UAAU,EAAE+C,WAAW,EAAE,GAAG,IAAI,CAAC9C,oBAAoB;QACtEC,IAAAA,iBAAM,EACJ8C,WAAW,QAAQhD,cAAc,QAAQ+C,eAAe,MACxD;QAGF,MAAMuC,OAAyB;YAC7B,4DAA4D;YAC5D3C,gBAAgBnE,IAAAA,0CAA4B,EAACqG;YAC7CpB,MAAM;YACNP,aAAa;YACbqC,iBAAiB;YACjBC,QAAQ;YACR1C,QAAQ;YACRa,UAAU;YACV,wDAAwD;YACxD5B,aAAa,IAAI,CAACzB,8BAA8B,GAAG,iBAAiB;YACpEhB,UAAU;YACVuD,MAAM;YACN,EAAE;YACF,GAAG,IAAI,CAAC5C,oBAAoB;YAE5B,0CAA0C;YAC1CgD,eAAe;YACfD;YACAhD;YACA+C;YAEA,GAAG+B,eAAe;QACpB;QAEA,wIAAwI;QACxI,MAAM,EAAEK,QAAQ,EAAE8B,MAAM,EAAE1J,GAAG,EAAE,GAAG2J,MAAM,GAAG,MAAM,IAAI,CAAC9B,uBAAuB,CAACP,UAAUS;QACxF,MAAM6B,iBAAiBC,WAAWH;QAElC,IAAI1J,KAAK;YACPtB,MAAM,mCAAmCkJ;YACzCkC,0CAAgB,CAACrI,GAAG,CAACmG,UAAU;gBAAE/C,KAAK,IAAI,CAAC9D,WAAW;gBAAEf;YAAI;QAC9D,OAAO;YACLtB,MAAM,gCAAgCkJ;QACxC;QAEA,OAAO;YACL,GAAG+B,IAAI;YACP5J,KAAK6J;YACLhC;YACA5H;QACF;IACF;IAEA,MAAM+J,wBACJnG,GAAe,EACf3E,OAGC,EACDW,KAAqB,EACrBkI,eAGI,CAAC,CAAC,EAKL;QACD,IAAI,IAAI,CAAC/E,8BAA8B,EAAE;YACvC,OAAO,IAAI,CAACiH,yCAAyC,CAACpG,KAAK3E,SAASW,OAAOkI;QAC7E;QAEA,OAAO,IAAI,CAACmC,iCAAiC,CAAChL,SAAS6I;IACzD;IAEA,MAAckC,0CACZpG,GAAe,EACf3E,OAGC,EACDW,KAAqB,EACrBkI,eAGI,CAAC,CAAC,EAKL;YAsIqBlE;QArItB,MAAMsG,2BAA2B,CAACvD;YAChC,iEAAiE;YACjE,OAAOwD,OACLxD,UACGyD,MAAM,CAAC,CAACC,IAAMA,EAAE5B,IAAI,KAAK,MACzBzI,GAAG,CAAC,CAACsK;oBACJA;wBAAAA,2CAAAA,SAASC,QAAQ,CAACC,qBAAqB,qBAAvCF,yCAAyCtK,GAAG,CAAC,CAACyK,MAAQC,IAAAA,mDAAiB,EAACD;cAE1E,yCAAyC;aACxCE,IAAI,GACJP,MAAM,CAACQ;QAEd;QAEA,wFAAwF;QACxF,IAAI,EACFC,uBAAuBxF,gBAAgB,EACvCmF,uBAAuBM,8BAA8B,EACrDC,UAAU,EACX,GAAG,MAAM,IAAI,CAACC,WAAW,CAAEC,kCAAkC,CAC5D;YACElJ,UAAU9C,QAAQ8C,QAAQ;YAC1BmJ,SAASjM,QAAQiM,OAAO;QAC1B,GACAtL;QAGF,iFAAiF;QAEjF,MAAMuL,0BAA0B,OAC9BX;YAKA9L,MAAM,gCAAgC2G;YAEtC,2DAA2D;YAC3D,MAAMqE,SAAS,MAAM,IAAI,CAACO,iCAAiC,CACzD;gBACE,GAAGhL,OAAO;gBACVoG;YACF,GACAyC;YAGF,iEAAiE;YACjE,MAAMsD,2BAA2BlB,yBAAyBR,OAAO/C,SAAS;YAE1E,IAAI,CAACyE,0BAA0B;gBAC7B,mDAAmD;gBACnD,MAAM,IAAIC,MACR;YAEJ;YACA3M,MAAM,+CAA+C0M;YAErD,MAAME,gCAAgCnB,OAAO;mBACxCK;mBACAY;aACJ;YAED,4IAA4I;YAC5I,MAAM,EAAE/F,kBAAkBkG,sBAAsB,EAAE,GAChD,MAAM,IAAI,CAACP,WAAW,CAAEQ,wBAAwB,CAC9C;gBACEzJ,UAAU9C,QAAQ8C,QAAQ;gBAC1BmJ,SAASjM,QAAQiM,OAAO;gBACxBO,aAAaH;YACf,GACA1L;YAGJ,iEAAiE;YACjE,MAAM8L,4BAA4BH,uBAAuBI,IAAI,CAC3D,CAACC,WAAa,CAACvG,iBAAiBwG,QAAQ,CAACD;YAG3C,IAAI,CAACF,2BAA2B;gBAC9B,OAAOhC;YACT;YAEAhL,MAAM,qDAAqD6M;YAE3DlG,mBAAmB8E,OAAO9E,iBAAiByG,MAAM,CAACP;YAElD,4HAA4H;YAC5H,2DAA2D;YAC3D,OAAOJ,wBAAwBG;QACjC;QAEA,MAAM5B,SAAS,MAAMyB,wBAAwBL;QAE7C,oEAAoE;QACpEpB,OAAO/C,SAAS,CAACvD,IAAI,IAAI2H;QAEzB,MAAMgB,aAAaC,IAAAA,2BAAkB,EAAC,IAAI,CAACjL,WAAW;QAEtD,qDAAqD;QACrD,MAAMkL,8BAA8B5G,iBAAiBrF,GAAG,CAAC,CAAC4L,WACxD,kFAAkF;YAClFM,IAAAA,qBAAW,EAAC/L,eAAI,CAACa,QAAQ,CAAC+K,YAAYH;QAExC,MAAMO,wBAAwB,AAC5BzC,OAAO/C,SAAS,CACb3G,GAAG,CAAC,CAACsK;gBAAaA;mBAAAA,CAAAA,6BAAAA,qBAAAA,SAAUC,QAAQ,qBAAlBD,mBAAoB8B,KAAK,KAAIxD,OAAOyD,MAAM,CAAC/B,SAASC,QAAQ,CAAC6B,KAAK;WACpFhC,MAAM,CAACQ,SACPD,IAAI,GACP2B,MAAM,CAAC,CAACC,KAAKH,QAAW,CAAA;gBAAE,GAAGG,GAAG;gBAAE,GAAGH,KAAK;YAAC,CAAA,GAAI,CAAC;QAElD1N,MAAM,iBAAiByN,uBAAuBF;QAE9C,MAAMO,cAAc,IAAI3J;QAExB,IAAI+F,OAAO6D,IAAI,CAACN,uBAAuB/K,MAAM,EAAE;YAC7C6K,4BAA4BS,OAAO,CAAC,CAACd;gBACnC,IAAIA,YAAYO,uBAAuB;oBACrCK,YAAY/K,GAAG,CAACmK,UAAUO,qBAAqB,CAACP,SAAS;gBAC3D,OAAO;oBACL,MAAM,IAAIP,MACR,CAAC,yBAAyB,EAAEO,SAAS,kCAAkC,EAAEhD,OAAO6D,IAAI,CAACN,uBAAuB/J,IAAI,CAAC,OAAO;gBAE5H;YACF;QACF,OAAO;YACL,8CAA8C;YAC9C1D,MAAM;YACNuN,4BAA4BS,OAAO,CAAC,CAACd;gBACnC,mBAAmB;gBACnBY,YAAY/K,GAAG,CAACmK,UAAU;YAC5B;QACF;QAEA,MAAMe,iBAAgB/I,aAAAA,IAAIG,KAAK,qBAATH,WAAWI,MAAM;QAEvC,8BAA8B;QAC9B,MAAM,IAAI,CAACgH,WAAW,CAAE4B,iBAAiB,CACvC;YACE7K,UAAU9C,QAAQ8C,QAAQ;YAC1ByK;YACAG;QACF,GACA/M;QAGF,4GAA4G;QAC5GA,MAAM6B,GAAG,CAAC,CAAC,UAAU,EAAExC,QAAQ8C,QAAQ,CAAC,gBAAgB,CAAC,EAAE;YACzDP,cAAc;YACd9B,UACE,sBACAa,KAAKG,SAAS,CACZ,yGAAyG;YACzGkI,OAAOiE,WAAW,CAChB1L,MAAM2L,IAAI,CAACN,YAAYO,OAAO,IAAI/M,GAAG,CAAC,CAAC,CAACgN,KAAKC,MAAM,GAAK;oBACtD,2BAA2B;oBAC3B,OAAOf,IAAAA,qBAAW,EAAC/L,eAAI,CAACa,QAAQ,CAAC,IAAI,CAACD,WAAW,EAAEZ,eAAI,CAACiC,IAAI,CAAC2J,YAAYiB;oBACzE;wBAACA;wBAAKC;qBAAM;iBACb;QAGT;QAEA,OAAO;YAAE,GAAGvD,MAAM;YAAE9J;QAAM;IAC5B;IAEA,MAAMqK,kCACJhL,OAGC,EACD6I,eAGI,CAAC,CAAC,EAC+E;QACrF,MAAM,EAAErC,OAAO,EAAEhD,UAAU,EAAE+C,WAAW,EAAE,GAAG,IAAI,CAAC9C,oBAAoB;QACtEC,IAAAA,iBAAM,EAAC1D,QAAQmG,cAAc,IAAI,MAAM;QACvCzC,IAAAA,iBAAM,EACJ8C,WAAW,QAAQhD,cAAc,QAAQ+C,eAAe,MACxD;QAGF,MAAMuC,OAAyB;YAC7B,GAAG,IAAI,CAACrF,oBAAoB;YAC5B+C;YACAhD;YACA+C;YACA,GAAGvG,OAAO;YACVuF,aAAa;YACbkD,kBAAkB;QACpB;QAEA,wIAAwI;QACxI,IAAI,CAACK,KAAK3C,cAAc,CAACtE,UAAU,CAAC,QAAQ,CAACX,eAAI,CAAC+B,UAAU,CAAC6F,KAAK3C,cAAc,GAAG;YACjF2C,KAAK3C,cAAc,GAAG,OAAO2C,KAAK3C,cAAc;QAClD;QAEA,MAAM8H,SAAS,MAAM,IAAI,CAACrF,uBAAuB,CAACE,KAAK3C,cAAc,EAAE2C,MAAMD;QAE7E,OAAO;YACLnB,WAAWuG,OAAOvG,SAAS;YAC3BgB,QAAQuF,OAAOvF,MAAM;QACvB;IACF;IAEA,MAAMwF,4BAA4B;QAChC,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;YAClB,MAAM,IAAI/B,MACR;QAEJ;QACA,IAAI,CAAC,IAAI,CAACgC,KAAK,EAAE;YACf,4FAA4F;YAC5F,WAAW;YACX3O,MAAM;YACN;QACF;QAEA,MAAM4O,WAAWC,OACdC,QAAQ,CAACnO,QAAQC,GAAG,CAACmO,QAAQ,EAC7BzN,GAAG,CAAC,CAAC0N,WAAavN,eAAI,CAACiC,IAAI,CAAC,IAAI,CAACrB,WAAW,EAAE2M;QAEjDC,IAAAA,uDAAkB,EAChB;YACEN,OAAO,IAAI,CAACA,KAAK;YACjBO,QAAQ,IAAI,CAACR,QAAQ,CAACQ,MAAM;QAC9B,GACAN,UACA;YACE5O,MAAM;YACN,0CAA0C;YAC1C6O,OAAWM,IAAI,CAAC,IAAI,CAAC9M,WAAW,EAAE;gBAAE+M,OAAO;YAAK;QAClD;IAEJ;IAIA,MAAgBC,yBACd9O,OAA4B,EACA;YAQxB2E,kBAAiDA,mBAElDA,mBAAiDA,mBAEhBA,mBAEqBA,UACFA,WAI/BA,mBAIFA,YAEgBA,WAOAA,mBAAAA;QA/BtC3E,QAAQC,IAAI,GAAG,MAAM,IAAI,CAACF,gBAAgB,CAACC;QAC3C,IAAI,CAAC+O,UAAU,GAAG,IAAI,CAACC,aAAa,CAAChP;QAErC,MAAMiP,SAASrK,IAAAA,mBAAS,EAAC,IAAI,CAAC9C,WAAW,EAAE;YAAEoN,2BAA2B;QAAK;QAC7E,MAAM,EAAEvK,GAAG,EAAE,GAAGsK;QAChB,+HAA+H;QAC/H,MAAMnL,iCACJ,CAAC,GAACa,mBAAAA,IAAIwK,WAAW,qBAAfxK,iBAAiByK,0BAA0B,KAAI,CAAC,GAACzK,oBAAAA,IAAIwK,WAAW,qBAAfxK,kBAAiB0K,oBAAoB;QAC1F,MAAMC,kCACJ,GAAC3K,oBAAAA,IAAIwK,WAAW,qBAAfxK,kBAAiByK,0BAA0B,KAAI,CAAC,GAACzK,oBAAAA,IAAIwK,WAAW,qBAAfxK,kBAAiB0K,oBAAoB;QACzF,IAAI,CAACvL,8BAA8B,GAAGA;QACtC,IAAI,CAAC0B,0BAA0B,GAAG,CAAC,GAACb,oBAAAA,IAAIwK,WAAW,qBAAfxK,kBAAiByK,0BAA0B;QAE/E,MAAMG,qBAAqB;YAAC;YAAU;SAAS,CAAC3C,QAAQ,CAACjI,EAAAA,WAAAA,IAAI6K,GAAG,qBAAP7K,SAASsJ,MAAM,KAAI;QAC5E,MAAMwB,eAAe3L,kCAAkCa,EAAAA,YAAAA,IAAI6K,GAAG,qBAAP7K,UAASsJ,MAAM,MAAK;QAC3E,MAAMzH,UAAUkJ,IAAAA,sCAAwB,EAAC/K;QACzC,MAAM+B,cAAciJ,IAAAA,0CAA4B,EAAChL,KAAK3E,QAAQqG,IAAI,IAAI,eAAe;QACrF,MAAM7C,aAAaoM,IAAAA,8CAAsC,EAAC,IAAI,CAAC9N,WAAW,EAAE6C;QAC5E,MAAM8B,gBAAgB,CAAC,GAAC9B,oBAAAA,IAAIwK,WAAW,qBAAfxK,kBAAiB8B,aAAa;QACtD,MAAM7D,SAAS1B,eAAI,CAACiC,IAAI,CAAC,IAAI,CAACrB,WAAW,EAAE0B;QAC3C,MAAM6C,OAAOrG,QAAQqG,IAAI,IAAI;QAE7B,MAAMqH,iBAAgB/I,aAAAA,IAAIG,KAAK,qBAATH,WAAWI,MAAM;QAEvC,IAAIjB,kCAAkCa,EAAAA,YAAAA,IAAI6K,GAAG,qBAAP7K,UAASsJ,MAAM,MAAK,UAAU;YAClE,MAAM,IAAI/I,oBAAY,CACpB,CAAC,oEAAoE,EAAEP,IAAI6K,GAAG,CAAEvB,MAAM,CAAC,gEAAgE,CAAC;QAE5J;QAEA,2FAA2F;QAC3F,IAAInK,kCAAkCa,CAAAA,wBAAAA,cAAAA,IAAKG,KAAK,sBAAVH,oBAAAA,YAAYI,MAAM,qBAAlBJ,kBAAoBkL,MAAM,MAAK,OAAO;YAC1E,MAAMC,aAAab,OAAOc,iBAAiB,IAAId,OAAOe,gBAAgB,IAAI;YAC1E,MAAMC,iBAAiB/O,eAAI,CAACC,QAAQ,CAAC2O;YACrC,MAAM,IAAI5K,oBAAY,CACpB,CAAC,sDAAsD,EAAE+K,eAAe,gFAAgF,EAAEA,eAAe,oBAAoB,CAAC;QAElM;QAEA,MAAMxM,uBAAuB;YAC3B8C,aAAa,CAAC,CAACvG,QAAQuG,WAAW;YAClCC;YACAH;YACA7C;YACAiD;YACAH,QAAQtG,QAAQsG,MAAM;YACtBI;QAEF;QACA,IAAI,CAACjD,oBAAoB,GAAGA;QAE5B,MAAMyM,gBAAgB;YACpBjQ,MAAMD,QAAQC,IAAI;YAClBkQ,YAAYnQ,QAAQmQ,UAAU;YAC9BC,YAAYpQ,QAAQqQ,cAAc;QACpC;QAEA,8BAA8B;QAC9BjQ,QAAQC,GAAG,CAACiQ,sBAAsB,GAAG,CAAC,iBAAiB,EAAEtQ,QAAQC,IAAI,EAAE;QAEvE,MAAM,EAAEmO,KAAK,EAAEmC,SAAS,EAAE5B,MAAM,EAAE5L,UAAU,EAAEyN,aAAa,EAAE,GAAG,MAAMC,IAAAA,uCAAqB,EACzF,IAAI,EACJP,eACA;YACE3J,aAAa,CAAC,CAACvG,QAAQuG,WAAW;YAClC5B;QACF;QAGF,IAAI,CAAC3E,QAAQuG,WAAW,EAAE;YACxB,MAAMmK,qBAAqB,MAAM,IAAI,CAACC,0BAA0B,CAAC3Q;YAEjE,8EAA8E;YAC9E4Q,IAAAA,4BAAiB,EAAC7N,YAAY,IAAI8N,oEAAiC,GAAGC,UAAU;YAEhF,wEAAwE;YACxE,yEAAyE;YACzE,0EAA0E;YAC1E,2EAA2E;YAC3E,gDAAgD;YAChD,4CAA4C;YAC5CF,IAAAA,4BAAiB,EAAC7N,YAAY2N,mBAAmBI,UAAU;YAE3D/N,WAAWgO,GAAG,CACZ,IAAIC,sDAA0B,CAAC,IAAI,CAAClP,WAAW,EAAE;gBAC/C,0CAA0C;gBAC1CmP,QAAQjR,QAAQyH,QAAQ,CAACwJ,MAAM,IAAI;YACrC,GAAGH,UAAU;YAEf/N,WAAWgO,GAAG,CACZ,IAAIG,kDAAwB,CAAC,IAAI,CAACpP,WAAW,EAAE,IAAI,CAACqP,qBAAqB,EAAEL,UAAU;YAGvF,MAAMM,qBAAqB,IAAIC,oDAAyB,CAAC,IAAI,CAACvP,WAAW,EAAE;gBACzEwP,aAAa,CAAC,EAAEC,OAAO,EAAE;oBACvB,IAAIA,YAAY,UAAU;4BACjB;wBAAP,QAAO,mBAAA,IAAI,CAACxC,UAAU,qBAAf,iBAAiByC,qBAAqB;oBAC/C,OAAO;4BACE;wBAAP,QAAO,oBAAA,IAAI,CAACzC,UAAU,qBAAf,kBAAiB0C,YAAY,CAAC;4BACnCR,QAAQ;wBACV;oBACF;gBACF;YACF;YACAlO,WAAWgO,GAAG,CAACK,mBAAmBN,UAAU;YAE5C,MAAMhE,aAAaC,IAAAA,2BAAkB,EAAC,IAAI,CAACjL,WAAW;YAEtD,MAAM4P,uBAAuBC,IAAAA,sDAA6B,EACxD;gBACEC,WAAW9E;gBACXhL,aAAa,IAAI,CAACA,WAAW;YAC/B,GACA2B;YAEF,kCAAkC;YAClC,yCAAyC;YACzCV,WAAWgO,GAAG,CAACW;YAEf3O,WAAWgO,GAAG,CAAC,IAAIc,0CAAoB,CAAC,IAAI,CAAC/P,WAAW,EAAEgP,UAAU;YAEpE,mFAAmF;YACnF,IAAI,IAAI,CAACgB,cAAc,IAAI;gBACzB,oHAAoH;gBACpH/O,WAAWgO,GAAG,CAAC,IAAIgB,4CAAqB,CAAC,IAAI,CAACjQ,WAAW,EAAEgP,UAAU;gBAErE,0GAA0G;gBAC1G/N,WAAWgO,GAAG,CAAC,IAAIiB,oCAAiB,CAAC,IAAI,CAAClQ,WAAW,EAAEgP,UAAU;YACnE;YAEA,IAAIvB,sBAAsBzL,gCAAgC;gBACxDmO,IAAAA,0DAAqB,EACnB;oBACE7D;oBACAO;gBACF,GACA,CAACuD;oBACC,IAAIzC,cAAc;wBAChB,+FAA+F;wBAC/F,+FAA+F;wBAC/F,sGAAsG;wBACtG,yGAAyG;wBACzG,gCAAgC;wBAChC,IAAI,CAAC0C,uBAAuB;oBAC9B,OAAO,IAAI,CAACC,IAAAA,+BAAuB,KAAI;wBACrC,KAAK,MAAMC,SAASH,OAAQ;gCAExB,gHAAgH;4BAChH,6CAA6C;4BAC7CG;4BAHF,IAGEA,EAAAA,kBAAAA,MAAM/G,QAAQ,qBAAd+G,gBAAgB7I,IAAI,MAAK,OACzB,gGAAgG;4BAChG6I,MAAMhK,QAAQ,CAACxG,UAAU,CAACe,WAC1B0P,IAAAA,4BAAoB,EAACD,MAAMhK,QAAQ,GACnC;gCACAkK,IAAAA,4BAAoB;4BACtB;wBACF;oBACF;gBACF;YAEJ;YAEA,qEAAqE;YACrE,IAAIzO,gCAAgC;gBAClC,IAAI,CAAC0O,gCAAgC;gBACrC,MAAMC,gBAAgBC,IAAAA,kEAAgC,EAAC,IAAI,CAAC5Q,WAAW,EAAE;oBACvE2B,sBAAsB,IAAI,CAACA,oBAAoB;oBAC/CI,SAAS;oBACTyB,eAAe,IAAI,CAACA,aAAa,CAACqN,IAAI,CAAC,IAAI;oBAC3CC,wBAAwB,IAAI,CAAC/L,2BAA2B,CAAC8L,IAAI,CAAC,IAAI;oBAClEE,iBAAiBvD;oBACjBwD,gBAAgB1E,MAAM2E,eAAe,CAACJ,IAAI,CAACvE;oBAC3CV;gBACF;gBACA,IAAI,CAAC3B,WAAW,GAAG0G;gBACnB1P,WAAWgO,GAAG,CAAC0B,cAAc1P,UAAU;gBACvC,IAAI,CAACiQ,gBAAgB,GAAGP,cAAcO,gBAAgB;YACxD;YAEA,mFAAmF;YACnF,IAAI,IAAI,CAAClB,cAAc,IAAI;gBACzB,IAAI,CAACvC,oBAAoB;oBACvB,8CAA8C;oBAC9CxM,WAAWgO,GAAG,CACZ,IAAIkC,oDAAyB,CAACvC,mBAAmBI,UAAU,GAAGoC,QAAQ,EAAEpC,UAAU;gBAEtF,OAAO;wBAME7B;oBALPlM,WAAWgO,GAAG,CACZoC,IAAAA,yDAA4B,EAAC,IAAI,CAACrR,WAAW,EAAE;wBAC7Cc;wBACAY;wBACAyL;4BACGA,oBAAAA,OAAOtK,GAAG,CAACG,KAAK,qBAAhBmK,kBAAkBlK,MAAM,AAA3B;wBACA3B,gBAAgB,CAACgQ,mBACf,IAAI,CAACC,iBAAiB,CAACD,kBAAkB;gCAAEtQ,UAAU;4BAAM;wBAC7DsE,oBAAoB,OAAOC;4BACzB,kDAAkD;4BAClD,IAAIvD,gCAAgC;gCAClC,2GAA2G;gCAC3G,4HAA4H;gCAC5H,MAAMwP,OAAO,MAAM5C,mBAAmB6C,0BAA0B;gCAChE,OAAO;oCAAExL,SAASuL;gCAAK;4BACzB;4BAEA,qFAAqF;4BACrF,OAAO,IAAI,CAAClM,kBAAkB,CAACC;wBACjC;oBACF;gBAEJ;YACF;QACF,OAAO;YACL,qEAAqE;YACrE,IAAIvD,gCAAgC;gBAClC,IAAI,CAAC0O,gCAAgC;gBACrC,MAAMC,gBAAgBC,IAAAA,kEAAgC,EAAC,IAAI,CAAC5Q,WAAW,EAAE;oBACvE2B,sBAAsB,IAAI,CAACA,oBAAoB;oBAC/CI,SAAS;oBACTyB,eAAe,IAAI,CAACA,aAAa,CAACqN,IAAI,CAAC,IAAI;oBAC3CC,wBAAwB,IAAI,CAAC/L,2BAA2B,CAAC8L,IAAI,CAAC,IAAI;oBAClEE,iBAAiBvD;oBACjBwD,gBAAgB1E,MAAM2E,eAAe,CAACJ,IAAI,CAACvE;oBAC3CV;gBACF;gBACA,IAAI,CAAC3B,WAAW,GAAG0G;YACrB;QACF;QACA,qEAAqE;QACrE,MAAMe,gBAAgB7E,OAAO8E,KAAK,CAACd,IAAI,CAAChE;QAExCA,OAAO8E,KAAK,GAAG,CAACC;YACd,OAAOF,cAAc,CAACG;gBACpB,IAAI,CAACxF,QAAQ,GAAG;gBAChB,IAAI,CAACC,KAAK,GAAG;gBACb,IAAI,CAACmC,SAAS,GAAG;gBACjB,IAAI,CAACqD,aAAa,GAAG,IAAIhQ;gBACzB8P,4BAAAA,SAAWC;YACb;QACF;QAEA,IAAI,CAACvF,KAAK,GAAGA;QACb,IAAI,CAACmC,SAAS,GAAGA;QACjB,OAAO;YACL5B;YACAlH,UAAU;gBACR,mDAAmD;gBACnDxH,MAAMD,QAAQC,IAAI;gBAClB,kCAAkC;gBAClC4T,MAAM;gBACN,iDAAiD;gBACjDjO,KAAK,CAAC,iBAAiB,EAAE5F,QAAQC,IAAI,EAAE;gBACvC6T,UAAU;YACZ;YACA/Q;YACAyN;QACF;IACF;IAIA,MAAcuD,oBAAoBnO,GAAW,EAAEoO,QAAsC,EAAE;QACrF,IAAI,CAAC,IAAI,CAACzD,SAAS,IAAI,IAAI,CAACqD,aAAa,CAACK,GAAG,CAACrO,MAAM;YAClD;QACF;QAEAnG,MAAM,uBAAuBmG;QAE7B,MAAMsO,SAAS,CAACC;YACd,MAAMC,OAAO9S,KAAKC,KAAK,CAAC8S,OAAOF;YAE/B,OAAQC,KAAK5K,IAAI;gBACf,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH;gBACF,KAAK;oBACH;wBACE,MAAM8K,SAASF,KAAKG,IAAI;wBACxB,MAAM,EACJC,eAAe,EACfC,KAAK,EACLC,QAAQ,EACRC,OAAO,EACR,GAaGL;wBAEJ,MAAMM,YAAYH,MAAMtS,MAAM,IAAIuS,SAASvS,MAAM,IAAIwS,QAAQxS,MAAM;wBAEnE,gHAAgH;wBAChH,IAAI,CAACqS,mBAAmBI,WAAW;4BACjC,yIAAyI;4BACzI,mBAAmB;4BACnB,IAAI,OAAOC,WAAWC,GAAG,KAAK,YAAYD,WAAWC,GAAG;4BAExD,MAAMC,eAAe,IAAIC,IACvB;mCAAIP;mCAAUC;6BAAS,CAAC3T,GAAG,CAAC,CAACkU,IAAMA,EAAEC,MAAM,CAAC,EAAE,EAAErI,MAAM,CAAC8H;4BAGzD,MAAMQ,YAAYjK,OAChBhJ,MAAM2L,IAAI,CAACkH,cACRhU,GAAG,CAAC,CAACqU;oCAKGA;gCAJP,IAAI,OAAOA,aAAa,UAAU;oCAChC,OAAO;gCACT;gCACA,yCAAyC;gCACzC,OAAOA,EAAAA,kBAAAA,SAASC,KAAK,CAAC,4CAAfD,eAAwC,CAAC,EAAE,KAAI;4BACxD,GACCjK,MAAM,CAACQ;4BAGZqI,SAASmB;wBACX;oBACF;oBACA;gBACF,KAAK;wBAICf;oBAHJ,6GAA6G;oBAC7GkB,QAAG,CAACC,KAAK,CAAC,sBAAsBjU,KAAKG,SAAS,CAAC2S,MAAM,MAAM;oBAE3D,IAAIA,EAAAA,aAAAA,KAAKG,IAAI,qBAATH,WAAW5K,IAAI,MAAK,sBAAsB;4BAG1C,mBAAmB;wBAClB;wBAHH8L,QAAG,CAACC,KAAK,CACP,2BAEA,EAAC,cAAA,IAAI,CAACnH,KAAK,qBAAV,YAAYoH,QAAQ,CAACC,mBAAmB,EAASjI,IAAI;oBAE1D;oBACA;gBACF;oBACE/N,MAAM,wBAAwB2U;oBAC9B;YACJ;QACF;QAEA,MAAMsB,SAAS,MAAM,IAAI,CAACnF,SAAS,CAAEoF,eAAe,CAAC/P,KAAKsO;QAC1D,IAAI,CAACN,aAAa,CAACpR,GAAG,CAACoD,KAAK8P;QAC5B,YAAY;QACZA,OAAOE,YAAY,GAAG;QACtB,MAAM,IAAI,CAACrF,SAAS,CAAEsF,mBAAmB,CAACH,QAAQ9P,KAAKsO;IACzD;IAEA,MAAa4B,yBAA2C;QACtD,IAAI,CAAC,IAAI,CAAC3H,QAAQ,EAAE;YAClB,MAAM,IAAI/B,MAAM;QAClB;QAEA,OAAO,IAAIvE,QAAiB,CAACkO;YAC3B,IAAI,CAAC,IAAI,CAAC3H,KAAK,EAAE;gBACf,4FAA4F;gBAC5F,4FAA4F;gBAC5F,mCAAmC;gBACnC3O,MAAM;gBACN,OAAOsW,QAAQ;YACjB;YAEA,MAAMC,MAAMC,IAAAA,oDAAyB,EAAC;gBACpCnU,aAAa,IAAI,CAACA,WAAW;gBAC7B6M,QAAQ,IAAI,CAACR,QAAQ,CAAEQ,MAAM;gBAC7BP,OAAO,IAAI,CAACA,KAAK;gBACjB8H,UAAU;gBACVC,UAAU;gBACVC,YAAY;oBAAC;oBAAU;iBAAM;gBAC7B1C,UAAU;oBACR,iGAAiG;oBACjGsC;oBACA,MAAM,EAAEK,6BAA6B,EAAE,GAAG,MAAM,mEAAA,QAC9C;oBAGF,IAAI;wBACF,MAAMC,MAAM,IAAID,8BAA8B,IAAI,CAACvU,WAAW;wBAC9D,MAAMwU,IAAIC,cAAc;wBACxBR,QAAQ;oBACV,EAAE,OAAOR,OAAY;wBACnB,iEAAiE;wBACjE,wCAAwC;wBACxCD,QAAG,CAACkB,GAAG;wBACPlB,QAAG,CAACC,KAAK,CACPkB,gBAAK,CAACC,GAAG,CAAC,gGAAgG,CAAC;wBAE7GpB,QAAG,CAACqB,SAAS,CAACpB;wBACdQ,QAAQ;oBACV;gBACF;YACF;QACF;IACF;IAEA,MAAaa,0BAA0B;YAE3B;QADV,OAAOC,IAAAA,iEAAkC,EAAC;YACxClI,MAAM,GAAE,iBAAA,IAAI,CAACR,QAAQ,qBAAb,eAAeQ,MAAM;YAC7BP,OAAO,IAAI,CAACA,KAAK;YACjBtM,aAAa,IAAI,CAACA,WAAW;QAC/B;IACF;IAEUgV,qBAA+B;QACvC,OAAO;YAAC;YAAqB;YAAuB;SAAqB;IAC3E;IAMA,gGAAgG;IAChG,MAAc1T,eACZiF,QAAgB,EAChB,EAAEvF,QAAQ,EAAwB,EACmB;QACrD,IAAI,IAAI,CAACiU,sBAAsB,CAAC9C,GAAG,CAAC5L,WAAW;YAC7C,OAAO,IAAI,CAAC0O,sBAAsB,CAACC,GAAG,CAAC3O;QACzC;QACA,MAAM4O,cAAc;YAClB,IAAI;gBACFxX,MAAM,qBAAqB,IAAI,CAACgE,oBAAoB,CAACD,UAAU,EAAE6E;gBACjE,OAAO,MAAM,IAAI,CAACG,qBAAqB,CAACH,UAAU;oBAChD9B,aAAa,IAAI,CAAC9C,oBAAoB,CAAC8C,WAAW;oBAClDzD;gBACF;YACF,EAAE,OAAOyS,OAAY;oBACJ;gBAAf,MAAM3S,SAAS,EAAA,6BAAA,IAAI,CAACa,oBAAoB,qBAAzB,2BAA2BD,UAAU,IAChDtC,eAAI,CAACiC,IAAI,CAAC,IAAI,CAACrB,WAAW,EAAE,IAAI,CAAC2B,oBAAoB,CAAED,UAAU,IACjE0T;gBACJ,MAAMC,eAAevU,SAAS1B,eAAI,CAACa,QAAQ,CAACa,QAAQyF,YAAYA;gBAEhE,wDAAwD;gBACxD,qDAAqD;gBACrD,MAAMsL,MAAM,IAAIzO,oBAAY,CAC1B,aACAuR,IAAAA,gBAAK,CAAA,CAAC,kCAAkC,EAAEU,aAAa,KAAK,CAAC,GAAG5B,MAAMpB,OAAO;gBAG/E,IAAK,MAAMpG,OAAOwH,MAAO;oBACvB,mBAAmB;oBACnB5B,GAAG,CAAC5F,IAAI,GAAGwH,KAAK,CAACxH,IAAI;gBACvB;gBAEA,MAAM4F;YACR,SAAU;YACR,2CAA2C;YAC7C;QACF;QACA,MAAM1P,QAAQgT;QAEd,IAAI,CAACF,sBAAsB,CAACvU,GAAG,CAAC6F,UAAUpE;QAC1C,OAAOA;IACT;IAEA,MAAcoP,kBACZhL,QAAgB,EAChB,EAAEvF,QAAQ,EAAwB,EACmB;QACrD,sCAAsC;QACtC,IAAI;YACF,MAAMsU,WAAW,MAAM,IAAI,CAAChU,cAAc,CAACiF,UAAU;gBAAEvF;YAAS;YAEhE,IAAI,EAACsU,4BAAAA,SAAUtW,GAAG,GAAE;gBAClB,OAAO;YACT;YACA,OAAOuW,IAAAA,6CAAmB,EAAC,IAAI,CAACvV,WAAW,EAAEsV,SAAStW,GAAG,EAAEsW,SAASzO,QAAQ;QAC9E,EAAE,OAAO4M,OAAO;YACd,4EAA4E;YAC5E,IAAIA,iBAAiBnJ,OAAO;gBAC1B,IAAI;oBACF,MAAMkL,kBAAkB,MAAMC,IAAAA,6CAAwB,EAAC;wBACrDhC;wBACAzT,aAAa,IAAI,CAACA,WAAW;wBAC7B0B,YAAY,IAAI,CAACC,oBAAoB,CAACD,UAAU;oBAClD;oBAEA,OAAO,IAAIgU,SAASF,iBAAiB;wBACnCG,QAAQ;wBACRC,SAAS;4BACP,gBAAgB;wBAClB;oBACF;gBACF,EAAE,OAAOC,eAAe;oBACtBlY,MAAM,iEAAiEkY;oBACvE,MAAMpC;gBACR;YACF,OAAO;gBACL,MAAMA;YACR;QACF;IACF;IAEQpD,0BAA0B;QAChC,IAAI,CAAC4E,sBAAsB,CAACa,KAAK;IACnC;IAEA,+EAA+E;IACvEpF,mCAAmC;QACzC,uDAAuD;QACvD,mBAAmB;QACnBqC,WAAWgD,wBAAwB,GAAG,IAAI,CAACC,gBAAgB,CAACnF,IAAI,CAAC,IAAI;IACvE;IAEA,gEAAgE;IAChE,8DAA8D;IACtDmF,iBAAiB,EAAEC,IAAI,EAAEC,EAAE,EAAgC,EAAE;QACnE,IAAI,CAACC,gBAAgB,CAAC,kBAAkB;YACtCnY,MAAM;YACNsU,MAAM;gBACJ2D;gBACAC;YACF;QACF;IACF;IAEA,YAAY;IAEJE,SAAStS,GAAQ,EAAE;QACzB,MAAMoO,WAAW,CAACmB,YAAsB,EAAE;YACxC,wDAAwD;YAExD,IAAI,CAACA,UAAUhT,MAAM,EAAE;gBACrB,6BAA6B;gBAC7B,IAAI,CAAC8V,gBAAgB,CAAC,kBAAkB;oBACtCnY,MAAM;gBACR;YACF,OAAO;gBACL,KAAK,MAAMgD,YAAYqS,UAAW;oBAChC,IAAI,CAACnC,gBAAgB,oBAArB,IAAI,CAACA,gBAAgB,MAArB,IAAI,EAAoBlQ;oBACxB,IAAI,CAACmV,gBAAgB,CAAC,kBAAkB;wBACtCnY,MAAM;wBACNgD;oBACF;gBACF;YACF;QACF;QAEA,IAAI,CAACiR,mBAAmB,CAACnO,IAAIuS,QAAQ,IAAInE;IAC3C;IAEA,sBAAsB;IAEtB,wFAAwF;IACxF,MAAc/J,mBACZH,qBAA6B,EAC7B,EACER,gBAAgB,EAChBH,eAAe,EACfe,YAAY,EACZE,iBAAiB,EAmBlB,EAC4B;YA6B7B;QA5BA1G,IAAAA,iBAAM,EAAC,IAAI,CAAC0K,KAAK,EAAE;QACnB,MAAMa,SAAS,IAAI,CAACb,KAAK,CAACgK,OAAO;QACjC,MAAMC,cAAc,IAAI,CAACjK,KAAK,CAACkK,iBAAiB;QAChD,MAAMC,mBAAmBtJ,OAAOuJ,0BAA0B,oBAAjCvJ,OAAOuJ,0BAA0B,MAAjCvJ,QAAoC,oBAAoB;YAC/ElB,KAAKsK;QACP;QAEA,MAAMI,aAAa,CAACC,sBAA8BC;gBAChD,8BAAA,uBAAA;aAAA,cAAA,IAAI,CAACvK,KAAK,sBAAV,wBAAA,YAAYwK,SAAS,sBAArB,+BAAA,sBAAuBtE,MAAM,qBAA7B,kCAAA,uBAAgC;gBAC9BuE,SAASC,WAAWT;gBACpB7O,MAAM;gBACNkP;gBACAC;YACF;QACF;QAEA,MAAMI,aAAa,IAAI,CAACC,gBAAgB,CAAClP,uBAAuB;YAC9DI;YACAZ;YACAH;QACF;QAEAoP,oCAAAA,iBAAkBU,KAAK,CAAC;QACxBV,oCAAAA,iBAAkBW,QAAQ,CAAC;YACzBC,MAAM;gBACJC,eAAeL,cAAc;YAC/B;QACF;SACA,cAAA,IAAI,CAAC3K,KAAK,qBAAV,YAAYwK,SAAS,CAACtE,MAAM,CAAC;YAC3BuE,SAASC,WAAWT;YACpBgB,eAAe;gBACbC,YAAYhQ,iBAAiBE,IAAI;gBACjCH,KAAKC,iBAAiBD,GAAG;gBACzBkQ,WAAWzP;gBACXxD,QAAQgD,iBAAiBhD,MAAM;gBAC/BxD,UAAUwG,iBAAiBxG,QAAQ;gBACnCsG,uBAAuBD,gBAAgBC,qBAAqB;gBAC5DM,wBAAwBJ,iBAAiBI,sBAAsB,IAAI,CAAC;YACtE;YACA8P,YAAY;YACZhQ,MAAM;QACR;QAEA,IAAI;YACF,IAAIiQ;YACJ,IAAIC;YAEJ,IAAI;oBAGEpQ;gBAFJ,+FAA+F;gBAC/F,mGAAmG;gBACnG,IAAIA,EAAAA,2CAAAA,iBAAiBI,sBAAsB,qBAAvCJ,yCAAyC/D,WAAW,MAAK,gBAAgB;oBAC3E,MAAMoU,QAAQ,MAAM,IAAI,CAACvL,KAAK,CAACwL,UAAU,GAAGC,eAAe,CACzD,iFAAiF;oBACjF,aAAa;oBACb/P,uBAEAR,kBACAH,iBACA;wBACEsP;wBACAtO,SAASD,aAAaC,OAAO;wBAC7BlD,MAAMiD,aAAajD,IAAI;oBACzB;oBAEFwS,QAAQE,MAAMF,KAAK;oBACnBC,WAAWC,MAAMD,QAAQ;gBAC3B,OAAO;oBACL,MAAMC,QAAQ,MAAOZ,CAAAA,cAAc,OAC/B,IAAI,CAAC3K,KAAK,CAACwL,UAAU,GAAGE,WAAW,CAAC,MAAMf,YAAY,SACtD,IAAI,CAAC3K,KAAK,CAACwL,UAAU,GAAGC,eAAe,CACrC,iFAAiF;oBACjF,aAAa;oBACb/P,uBAEAR,kBACAH,iBACA;wBACEsP;wBACAtO,SAASD,aAAaC,OAAO;wBAC7BlD,MAAMiD,aAAajD,IAAI;oBACzB,EACF;oBACJwS,QAAQE,MAAMF,KAAK;oBACnBC,WAAWC,MAAMD,QAAQ;gBAC3B;YACF,EAAE,OAAOnE,OAAO;gBACdwE,IAAAA,mDAA8B,EAACxE;gBAC/ByE,IAAAA,iDAA4B,EAACzE;gBAC7B,MAAMA;YACR;YAEAgD,oCAAAA,iBAAkBW,QAAQ,CAAC;gBACzBe,KAAK;oBACHC,kBAAkBR,SAASS,KAAK,CAACC,YAAY,CAACC,IAAI;gBACpD;YACF;YACA9B,oCAAAA,iBAAkBU,KAAK,CAAC;YACxBV,oCAAAA,iBAAkBU,KAAK,CAAC;YAExB,MAAMqB,wBAAwB,IAAI,CAAClM,KAAK,CAACmM,4BAA4B,CAAC5H,IAAI,CAAC,IAAI,CAACvE,KAAK;YAErF,MAAMoM,aAAa,IAAI,CAACC,kBAAkB;YAE1C,MAAMhQ,SAAS,MAAM+P,WACnB,iFAAiF;YACjF,aAAa;YACb1Q,uBAEA4P,SAASgB,OAAO,EAChBhB,SAASS,KAAK,EACd;gBACEQ,wBAAwB,MAAM,IAAI,CAACvM,KAAK,CAACwM,oBAAoB,CAC3D3L,OAAO4L,WAAW,CAACF,sBAAsB,EACzC;oBACEG,YAAY;oBACZ3R;oBACAG;gBACF;gBAEF,wBAAwB;gBACxByR,qBAAqB9L,OAAOuL,UAAU,CAACO,mBAAmB;gBAC1DjI,gBAAgB,IAAI,CAAC1E,KAAK,CAAC2E,eAAe;gBAC1CiI,uBAAuB/L,OAAOuL,UAAU,CAACQ,qBAAqB;gBAC9DC,mBAAmB/Q,aAAajD,IAAI;gBACpCoC,KAAKC,iBAAiBD,GAAG;gBACzBvH,aAAamN,OAAOnN,WAAW;gBAC/BuI,aAAaD,kBAAkBC,WAAW;gBAC1C6Q,qBAAqBjM,OAAOuL,UAAU,CAACW,6BAA6B,CAClErR;gBAGFQ,WAAWF,kBAAkBE,SAAS;gBACtCE,cAAcJ,kBAAkBI,YAAY;gBAC5CD,WAAWH,kBAAkBG,SAAS;gBACtCxB,iBAAiBqB,kBAAkBrB,eAAe;gBAClD+D,YAAYmC,OAAON,MAAM,CAACyM,mBAAmB,IAAInM,OAAOnN,WAAW;gBACnEwY;gBAEA,iFAAiF;gBACjFlQ;YACF;YAGF,IAAI,CAACgE,KAAK,CAACwK,SAAS,CAACtE,MAAM,CAAC;gBAC1BuE,SAASC,WAAWT;gBACpB7O,MAAM;YACR;YAEA+O,oCAAAA,iBAAkBU,KAAK,CAAC;YAExB,IAAIoC,aAA4B;YAChC,IAAIC,YAA2B;YAE/B,qDAAqD;YACrD,IAAIlR,kBAAkB6D,MAAM,KAAK,UAAU;gBACzC,IAAI;wBAYgBvG,oBAAAA;oBAXlB,MAAM6T,SAAS,OAAO9Q,WAAW,WAAWnJ,KAAKC,KAAK,CAACkJ,UAAUA;oBAEjE/G,IAAAA,iBAAM,EACJ,eAAe6X,UAAUrZ,MAAMsZ,OAAO,CAACD,OAAO7T,SAAS,GACvD;oBAGF,MAAMA,YAAY6T,OAAO7T,SAAS;oBAClC,MAAMgB,SAAS6S,OAAO7S,MAAM;oBAE5B,MAAM2S,aAAa3T,UAAUyD,MAAM,CAAC,CAACsQ,QAAUA,MAAMjS,IAAI,KAAK,KAAK,CAAC,EAAE;oBACtE,MAAM8R,YAAY5T,EAAAA,oBAAAA,UAAUyD,MAAM,CAAC,CAACsQ,QAAUA,MAAMjS,IAAI,KAAK,4BAA3C9B,qBAAAA,iBAAmD,CAAC,EAAE,qBAAtDA,mBAAwD9F,MAAM,KAAI;oBAEpF,OAAO;wBACL8Z,kBAAkBjC,MAAMkC,KAAK,GACzBlC,MAAMhF,KAAK,CAAC4F,IAAI,GAAGX,SAASgB,OAAO,CAACvY,MAAM,GAC1CsX,MAAMhF,KAAK,CAAC4F,IAAI,GAAGZ,MAAM/E,QAAQ,CAAC2F,IAAI,GAAGZ,MAAM9E,OAAO,CAAC0F,IAAI;wBAC/DuB,kBAAkBlC,SAASmC,IAAI;wBAC/BC,WAAWpC,SAAS1B,EAAE;wBACtBvN,QAAQ4Q,WAAWzZ,MAAM;wBACzBb,KAAKua;wBACL5T;wBACAgB;oBACF;gBACF,EAAE,OAAO6M,OAAY;oBACnB,MAAM,IAAInJ,MACR,mHACEmJ,MAAMpB,OAAO;gBAEnB;YACF;YAEA,IAAI,OAAO1J,WAAW,UAAU;gBAC9B4Q,aAAa5Q;gBAEb,4CAA4C;gBAC5C,IAAI,EAAEiQ,OAAO,EAAEP,KAAK,EAAE,GAAGT;gBACzB,IAAItP,kBAAkBC,WAAW,EAAE;oBACjCqQ,UAAU,EAAE;gBACd;gBAEAY,YAAY,MAAMS,qBAChB;oBACE,EAAE;uBACCrB;uBACA,IAAI,CAACtM,KAAK,CAAC4N,iBAAiB,CAAC7B;iBACjC,EACD;oBACE8B,eAAe7R,kBAAkB6R,aAAa;oBAC9ClB,qBAAqB9L,OAAOuL,UAAU,CAACO,mBAAmB;oBAC1DT;gBACF;YAEJ,OAAO;gBACLe,aAAa5Q,OAAOsN,IAAI;gBACxBuD,YAAY7Q,OAAO1J,GAAG;YACxB;YAEA,OAAO;gBACL2a,kBAAkBjC,MAAMkC,KAAK,GACzBlC,MAAMhF,KAAK,CAAC4F,IAAI,GAAGX,SAASgB,OAAO,CAACvY,MAAM,GAC1CsX,MAAMhF,KAAK,CAAC4F,IAAI,GAAGZ,MAAM/E,QAAQ,CAAC2F,IAAI,GAAGZ,MAAM9E,OAAO,CAAC0F,IAAI;gBAC/DuB,kBAAkBlC,SAASmC,IAAI;gBAC/BC,WAAWpC,SAAS1B,EAAE;gBACtBvN,QAAQ4Q;gBACRta,KAAKua;YACP;QACF,EAAE,OAAO/F,OAAO;YACd,+DAA+D;YAC/D,mBAAmB;YACnBA,KAAK,CAAC2G,iDAA4B,CAAC,GAAG;YAEtC,IAAI,CAAC9N,KAAK,CAACwK,SAAS,CAACtE,MAAM,CAAC;gBAC1BuE,SAASC,WAAWT;gBACpB7O,MAAM;YACR;YAEA,MAAM+L;QACR;IACF;IAEQkF,qBAAqB;YAEzB,qBAAA;QADF,OACE,EAAA,cAAA,IAAI,CAACrM,KAAK,sBAAV,sBAAA,YAAYgK,OAAO,qBAAnB,oBAAqBoC,UAAU,CAAC2B,gBAAgB,KAC/C,CAAA,CAACC,YAAYC,YAAYlC,OAAOna,UAC/Bsc,IAAAA,yBAAc,EAACC,IAAAA,uBAAY,EAACH,YAAYC,YAAYlC,OAAOna,UAAU+X,IAAI,AAAD;IAE9E;IAEQiB,iBACNlP,qBAA6B,EAC7B,EACEI,YAAY,EACZZ,gBAAgB,EAChBH,eAAe,EAWhB,EACD;QACAzF,IAAAA,iBAAM,EAAC,IAAI,CAAC0K,KAAK,EAAE;QACnB,MAAMa,SAAS,IAAI,CAACb,KAAK,CAACgK,OAAO;QAEjC,MAAMoE,UAAUC,IAAAA,qBAAU,EAAC3S,uBAAuBR,kBAAkB;YAClEoT,8BAA8BzN,OAAO4L,WAAW,CAAC6B,4BAA4B;YAC7EvT;YACAgB,SAASD,aAAaC,OAAO;YAC7BlD,MAAMiD,aAAajD,IAAI;QACzB;QACA,OAAO,IAAI,CAACmH,KAAK,CAACwL,UAAU,GAAG+C,oBAAoB,CAACH;IACtD;IAEA,MAAczS,yBACZqL,QAAgB,EAChB,EACEjM,eAAe,EACfG,gBAAgB,EAOjB,EACD;QACA5F,IAAAA,iBAAM,EAAC,IAAI,CAAC0K,KAAK,EAAE;QACnB,OAAO,MAAM,IAAI,CAACA,KAAK,CAACwM,oBAAoB,CAAC5Y,IAAAA,0CAA4B,EAACoT,WAAW;YACnF0F,YAAY;YACZ3R;YACAG;QACF;IACF;;QA7rDK,qBACG8E,QAA4B,WAC5BmC,YAAmD,WACnDqD,gBAA6C,IAAIhQ,OAyXzD,kCAAkC;aAC1BH,uBAAkD,CAAC,QAEnD6B,gBAAmC,OACzC+C,UACAC,kBAAkB,CAAC,CAAC,EACpBsU,SAAS,CAAC,CAAC;YAEX,MAAMC,MAAM,MAAM,IAAI,CAACrU,qBAAqB,CAACH,UAAUC;YAEvD,IACE,mFAAmF;YACnFsU,OAAOrT,GAAG,IACV,IAAI,CAAC9F,oBAAoB,CAAC8C,WAAW,KAAK,MAC1C;gBACA,mBAAmB;gBACnB,MAAMuG,aAAaC,IAAAA,2BAAkB,EAAC,IAAI,CAACjL,WAAW;gBACtD,MAAMqV,eAAejW,eAAI,CAACa,QAAQ,CAAC+K,YAAY+P,IAAIlU,QAAQ;gBAC3D,MAAM/C,MAAM,IAAIK,IAAIkR,cAAc,IAAI,CAACtR,uBAAuB;gBAC9D,IAAI,CAACqS,QAAQ,CAACtS;YAChB;YAEA,OAAOkX,IAAAA,mDAAyB,EAC9B,IAAI,CAAChb,WAAW,EAChB+a,IAAI/b,GAAG,EACP+b,IAAIlU,QAAQ,EACZL,gBAAgB/B,WAAW,IAAI,IAAI,CAAC9C,oBAAoB,CAAC8C,WAAW;QAExE,QA4bAwF,cAAmF,WAuQ3EiH,mBAAwD,MAwJhE,aAAa;aAEL+D,yBAAyB,IAAInT;;AAycvC;AAEA,SAASkV,WAAWT,WAAmB;IACrC,OAAOA,YAAYF,QAAQ,CAAC;AAC9B;AAEA,SAASvN,WAAWmS,GAAW;IAC7B,uDAAuD;IACvD,mDAAmD;IACnD,6FAA6F;IAC7F,OAAOA,IAAI3b,OAAO,CAAC,oBAAoB;AACzC;AAEA,eAAe2a,qBACbiB,OAA0B,EAC1Bhd,OAAkC;IAElC,OAAO,AAAC,CAAA,MAAMid,IAAAA,mDAA6B,EAACD,SAAShd,QAAO,EAAGmY,QAAQ,CAACjB,WAAW;QACjF+E,eAAejc,QAAQic,aAAa;IACtC;AACF;AAEA,SAAS/Q,OAAUgS,KAAU;IAC3B,OAAOhb,MAAM2L,IAAI,CAAC,IAAImH,IAAIkI;AAC5B"}