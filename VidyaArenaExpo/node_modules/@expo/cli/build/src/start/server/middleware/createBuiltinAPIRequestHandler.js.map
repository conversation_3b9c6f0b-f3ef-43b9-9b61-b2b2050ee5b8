{"version": 3, "sources": ["../../../../../src/start/server/middleware/createBuiltinAPIRequestHandler.ts"], "sourcesContent": ["import { convertRe<PERSON>, RequestHand<PERSON>, respond } from '@expo/server/adapter/http';\n\nimport type { ServerNext, ServerRequest, ServerResponse } from './server.types';\n\nexport function createBuiltinAPIRequestHandler(\n  matchRequest: (request: Request) => boolean,\n  handlers: Record<string, (req: Request) => Promise<Response>>\n): RequestHandler {\n  return createRequestHandler((req) => {\n    if (!matchRequest(req)) {\n      winterNext();\n    }\n    const handler = handlers[req.method];\n    if (!handler) {\n      notAllowed();\n    }\n    return handler(req);\n  });\n}\n\n/**\n * Returns a request handler for http that serves the response using Remix.\n */\nexport function createRequestHandler(\n  handleRequest: (request: Request) => Promise<Response>\n): RequestHandler {\n  return async (req: ServerRequest, res: ServerResponse, next: ServerNext) => {\n    if (!req?.url || !req.method) {\n      return next();\n    }\n    // These headers (added by other middleware) break the browser preview of RSC.\n    res.removeHeader('X-Content-Type-Options');\n    res.removeHeader('Cache-Control');\n    res.removeHeader('Expires');\n    res.removeHeader('Surrogate-Control');\n\n    try {\n      const request = convertRequest(req, res);\n      const response = await handleRequest(request);\n      return await respond(res, response);\n    } catch (error: unknown) {\n      if (error instanceof Error) {\n        return await respond(\n          res,\n          new Response('Internal Server Error: ' + error.message, {\n            status: 500,\n            headers: {\n              'Content-Type': 'text/plain',\n            },\n          })\n        );\n      } else if (error instanceof Response) {\n        return await respond(res, error);\n      }\n      // http doesn't support async functions, so we have to pass along the\n      // error manually using next().\n      // @ts-expect-error\n      next(error);\n    }\n  };\n}\n\nfunction notAllowed(): never {\n  throw new Response('Method Not Allowed', {\n    status: 405,\n    headers: {\n      'Content-Type': 'text/plain',\n    },\n  });\n}\n\nexport function winterNext(): never {\n  // eslint-disable-next-line no-throw-literal\n  throw undefined;\n}\n"], "names": ["createBuiltinAPIRequestHandler", "createRequestHandler", "winterNext", "matchRequest", "handlers", "req", "handler", "method", "notAllowed", "handleRequest", "res", "next", "url", "removeHeader", "request", "convertRequest", "response", "respond", "error", "Error", "Response", "message", "status", "headers", "undefined"], "mappings": ";;;;;;;;;;;IAIgBA,8BAA8B;eAA9BA;;IAmBAC,oBAAoB;eAApBA;;IAgDAC,UAAU;eAAVA;;;;yBAvEwC;;;;;;AAIjD,SAASF,+BACdG,YAA2C,EAC3CC,QAA6D;IAE7D,OAAOH,qBAAqB,CAACI;QAC3B,IAAI,CAACF,aAAaE,MAAM;YACtBH;QACF;QACA,MAAMI,UAAUF,QAAQ,CAACC,IAAIE,MAAM,CAAC;QACpC,IAAI,CAACD,SAAS;YACZE;QACF;QACA,OAAOF,QAAQD;IACjB;AACF;AAKO,SAASJ,qBACdQ,aAAsD;IAEtD,OAAO,OAAOJ,KAAoBK,KAAqBC;QACrD,IAAI,EAACN,uBAAAA,IAAKO,GAAG,KAAI,CAACP,IAAIE,MAAM,EAAE;YAC5B,OAAOI;QACT;QACA,8EAA8E;QAC9ED,IAAIG,YAAY,CAAC;QACjBH,IAAIG,YAAY,CAAC;QACjBH,IAAIG,YAAY,CAAC;QACjBH,IAAIG,YAAY,CAAC;QAEjB,IAAI;YACF,MAAMC,UAAUC,IAAAA,sBAAc,EAACV,KAAKK;YACpC,MAAMM,WAAW,MAAMP,cAAcK;YACrC,OAAO,MAAMG,IAAAA,eAAO,EAACP,KAAKM;QAC5B,EAAE,OAAOE,OAAgB;YACvB,IAAIA,iBAAiBC,OAAO;gBAC1B,OAAO,MAAMF,IAAAA,eAAO,EAClBP,KACA,IAAIU,SAAS,4BAA4BF,MAAMG,OAAO,EAAE;oBACtDC,QAAQ;oBACRC,SAAS;wBACP,gBAAgB;oBAClB;gBACF;YAEJ,OAAO,IAAIL,iBAAiBE,UAAU;gBACpC,OAAO,MAAMH,IAAAA,eAAO,EAACP,KAAKQ;YAC5B;YACA,qEAAqE;YACrE,+BAA+B;YAC/B,mBAAmB;YACnBP,KAAKO;QACP;IACF;AACF;AAEA,SAASV;IACP,MAAM,IAAIY,SAAS,sBAAsB;QACvCE,QAAQ;QACRC,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAEO,SAASrB;IACd,4CAA4C;IAC5C,MAAMsB;AACR"}