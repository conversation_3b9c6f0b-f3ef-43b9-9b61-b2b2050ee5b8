{"version": 3, "sources": ["../../../../../src/start/server/middleware/mutations.ts"], "sourcesContent": ["import type { Server as ConnectServer, HandleFunction } from 'connect';\n\n/**\n * Prepends a `middleware` to current server middleware stack.\n *\n * @param app connect app server instance\n * @param middleware target middleware to be prepended\n */\nexport function prependMiddleware(app: ConnectServer, middleware: HandleFunction) {\n  app.use(middleware);\n  app.stack.unshift(app.stack.pop()!);\n}\n\n/**\n * Replaces source middleware with a new middlware in connect app\n *\n * @param app connect app server instance\n * @param sourceMiddleware source middlware to be matched and replaces\n * @param targetMiddleware new middlware\n */\nexport function replaceMiddlewareWith(\n  app: ConnectServer,\n  sourceMiddleware: HandleFunction,\n  targetMiddleware: HandleFunction\n) {\n  const item = app.stack.find((middleware) => {\n    const handlerCode = middleware.handle.toString();\n    return !handlerCode.includes('[native code]') && handlerCode === sourceMiddleware.toString();\n  });\n  if (item) {\n    item.handle = targetMiddleware;\n  }\n}\n"], "names": ["prependMiddleware", "replaceMiddlewareWith", "app", "middleware", "use", "stack", "unshift", "pop", "sourceMiddleware", "targetMiddleware", "item", "find", "handlerCode", "handle", "toString", "includes"], "mappings": ";;;;;;;;;;;IAQgBA,iBAAiB;eAAjBA;;IAYAC,qBAAqB;eAArBA;;;AAZT,SAASD,kBAAkBE,GAAkB,EAAEC,UAA0B;IAC9ED,IAAIE,GAAG,CAACD;IACRD,IAAIG,KAAK,CAACC,OAAO,CAACJ,IAAIG,KAAK,CAACE,GAAG;AACjC;AASO,SAASN,sBACdC,GAAkB,EAClBM,gBAAgC,EAChCC,gBAAgC;IAEhC,MAAMC,OAAOR,IAAIG,KAAK,CAACM,IAAI,CAAC,CAACR;QAC3B,MAAMS,cAAcT,WAAWU,MAAM,CAACC,QAAQ;QAC9C,OAAO,CAACF,YAAYG,QAAQ,CAAC,oBAAoBH,gBAAgBJ,iBAAiBM,QAAQ;IAC5F;IACA,IAAIJ,MAAM;QACRA,KAAKG,MAAM,GAAGJ;IAChB;AACF"}