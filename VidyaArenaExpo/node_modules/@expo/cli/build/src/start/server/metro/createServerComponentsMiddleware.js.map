{"version": 3, "sources": ["../../../../../src/start/server/metro/createServerComponentsMiddleware.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { getMetroServerRoot } from '@expo/config/paths';\nimport type { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport { getRscMiddleware } from '@expo/server/private';\nimport assert from 'assert';\nimport type { EntriesDev } from 'expo-router/build/rsc/server';\nimport path from 'path';\nimport url from 'url';\n\nimport { IS_METRO_BUNDLE_ERROR_SYMBOL, logMetroError } from './metroErrorInterface';\nimport { isPossiblyUnableToResolveError } from '../../../export/embed/xcodeCompilerLogger';\nimport type { ExportAssetMap } from '../../../export/saveAssets';\nimport { stripAnsi } from '../../../utils/ansi';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { memoize } from '../../../utils/fn';\nimport { getIpAddress } from '../../../utils/ip';\nimport { streamToStringAsync } from '../../../utils/stream';\nimport { createBuiltinAPIRequestHandler } from '../middleware/createBuiltinAPIRequestHandler';\nimport {\n  createBundleUrlSearchParams,\n  type ExpoMetroOptions,\n  getMetroOptionsFromUrl,\n} from '../middleware/metroOptions';\n\nconst debug = require('debug')('expo:rsc') as typeof console.log;\n\ntype SSRLoadModuleArtifactsFunc = (\n  filePath: string,\n  specificOptions?: Partial<ExpoMetroOptions>\n) => Promise<{ artifacts: SerialAsset[]; src: string }>;\n\ntype SSRLoadModuleFunc = <T extends Record<string, any>>(\n  filePath: string,\n  specificOptions?: Partial<ExpoMetroOptions>,\n  extras?: { hot?: boolean }\n) => Promise<T>;\n\nconst getMetroServerRootMemo = memoize(getMetroServerRoot);\n\nexport function createServerComponentsMiddleware(\n  projectRoot: string,\n  {\n    rscPath,\n    instanceMetroOptions,\n    ssrLoadModule,\n    ssrLoadModuleArtifacts,\n    useClientRouter,\n    createModuleId,\n    routerOptions,\n  }: {\n    rscPath: string;\n    instanceMetroOptions: Partial<ExpoMetroOptions>;\n    ssrLoadModule: SSRLoadModuleFunc;\n    ssrLoadModuleArtifacts: SSRLoadModuleArtifactsFunc;\n    useClientRouter: boolean;\n    createModuleId: (\n      filePath: string,\n      context: { platform: string; environment: string }\n    ) => string | number;\n    routerOptions: Record<string, any>;\n  }\n) {\n  const routerModule = useClientRouter\n    ? 'expo-router/build/rsc/router/noopRouter'\n    : 'expo-router/build/rsc/router/expo-definedRouter';\n\n  const rscMiddleware = getRscMiddleware({\n    config: {},\n    // Disabled in development\n    baseUrl: '',\n    rscPath,\n    onError: console.error,\n    renderRsc: async (args) => {\n      // In development we should add simulated versions of common production headers.\n      if (args.headers['x-real-ip'] == null) {\n        args.headers['x-real-ip'] = getIpAddress();\n      }\n      if (args.headers['x-forwarded-for'] == null) {\n        args.headers['x-forwarded-for'] = args.headers['x-real-ip'];\n      }\n      if (args.headers['x-forwarded-proto'] == null) {\n        args.headers['x-forwarded-proto'] = 'http';\n      }\n\n      // Dev server-only implementation.\n      try {\n        return await renderRscToReadableStream({\n          ...args,\n          headers: new Headers(args.headers),\n          body: args.body!,\n          routerOptions,\n        });\n      } catch (error: any) {\n        // If you get a codeFrame error during SSR like when using a Class component in React Server Components, then this\n        // will throw with:\n        // {\n        //   rawObject: {\n        //     type: 'TransformError',\n        //     lineNumber: 0,\n        //     errors: [ [Object] ],\n        //     name: 'SyntaxError',\n        //     message: '...',\n        //   }\n        // }\n\n        // TODO: Revisit all error handling now that we do direct metro bundling...\n        await logMetroError(projectRoot, { error });\n\n        if (error[IS_METRO_BUNDLE_ERROR_SYMBOL]) {\n          throw new Response(JSON.stringify(error), {\n            status: isPossiblyUnableToResolveError(error) ? 404 : 500,\n            headers: {\n              'Content-Type': 'application/json',\n            },\n          });\n        }\n\n        const sanitizedServerMessage = stripAnsi(error.message) ?? error.message;\n        throw new Response(sanitizedServerMessage, {\n          status: 500,\n          headers: {\n            'Content-Type': 'text/plain',\n          },\n        });\n      }\n    },\n  });\n\n  let rscPathPrefix = rscPath;\n  if (rscPathPrefix !== '/') {\n    rscPathPrefix += '/';\n  }\n\n  async function exportServerActionsAsync(\n    {\n      platform,\n      entryPoints,\n      domRoot,\n    }: { platform: string; entryPoints: string[]; domRoot?: string },\n    files: ExportAssetMap\n  ): Promise<{\n    clientBoundaries: string[];\n    manifest: Record<string, [string, string]>;\n  }> {\n    const uniqueEntryPoints = [...new Set(entryPoints)];\n    // TODO: Support multiple entry points in a single split server bundle...\n    const manifest: Record<string, [string, string]> = {};\n    const nestedClientBoundaries: string[] = [];\n    const nestedServerBoundaries: string[] = [];\n    const processedEntryPoints = new Set<string>();\n    async function processEntryPoint(entryPoint: string) {\n      processedEntryPoints.add(entryPoint);\n\n      const contents = await ssrLoadModuleArtifacts(entryPoint, {\n        environment: 'react-server',\n        platform,\n        // Ignore the metro runtime to avoid overwriting the original in the API route.\n        modulesOnly: true,\n        // Required\n        runModule: true,\n        // Required to ensure assets load as client boundaries.\n        domRoot,\n      });\n\n      const reactClientReferences = contents.artifacts\n        .filter((a) => a.type === 'js')[0]\n        .metadata.reactClientReferences?.map((ref) => fileURLToFilePath(ref));\n\n      if (reactClientReferences) {\n        nestedClientBoundaries.push(...reactClientReferences!);\n      }\n      const reactServerReferences = contents.artifacts\n        .filter((a) => a.type === 'js')[0]\n        .metadata.reactServerReferences?.map((ref) => fileURLToFilePath(ref));\n\n      if (reactServerReferences) {\n        nestedServerBoundaries.push(...reactServerReferences!);\n      }\n\n      // Naive check to ensure the module runtime is not included in the server action bundle.\n      if (contents.src.includes('The experimental Metro feature')) {\n        throw new Error(\n          'Internal error: module runtime should not be included in server action bundles: ' +\n            entryPoint\n        );\n      }\n\n      const relativeName = createModuleId(entryPoint, {\n        platform,\n        environment: 'react-server',\n      });\n      const safeName = path.basename(contents.artifacts.find((a) => a.type === 'js')!.filename!);\n\n      const outputName = `_expo/rsc/${platform}/${safeName}`;\n      // While we're here, export the router for the server to dynamically render RSC.\n      files.set(outputName, {\n        targetDomain: 'server',\n        contents: wrapBundle(contents.src),\n      });\n\n      // Match babel plugin.\n      const publicModuleId = './' + toPosixPath(path.relative(projectRoot, entryPoint));\n\n      // Import relative to `dist/server/_expo/rsc/web/router.js`\n      manifest[publicModuleId] = [String(relativeName), outputName];\n    }\n\n    async function processEntryPoints(entryPoints: string[], recursions = 0) {\n      // Arbitrary recursion limit to prevent infinite loops.\n      if (recursions > 10) {\n        throw new Error('Recursion limit exceeded while processing server boundaries');\n      }\n\n      for (const entryPoint of entryPoints) {\n        await processEntryPoint(entryPoint);\n      }\n\n      // When a server action has other server actions inside of it, we need to process those as well to ensure all entry points are in the manifest and accounted for.\n      let uniqueNestedServerBoundaries = [...new Set(nestedServerBoundaries)];\n      // Filter out values that have already been processed.\n      uniqueNestedServerBoundaries = uniqueNestedServerBoundaries.filter(\n        (value) => !processedEntryPoints.has(value)\n      );\n      if (uniqueNestedServerBoundaries.length) {\n        debug('bundling nested server action boundaries', uniqueNestedServerBoundaries);\n        return processEntryPoints(uniqueNestedServerBoundaries, recursions + 1);\n      }\n    }\n\n    await processEntryPoints(uniqueEntryPoints);\n\n    // Save the SSR manifest so we can perform more replacements in the server renderer and with server actions.\n    files.set(`_expo/rsc/${platform}/action-manifest.js`, {\n      targetDomain: 'server',\n      contents: 'module.exports = ' + JSON.stringify(manifest),\n    });\n\n    return { manifest, clientBoundaries: nestedClientBoundaries };\n  }\n\n  async function getExpoRouterClientReferencesAsync(\n    { platform, domRoot }: { platform: string; domRoot?: string },\n    files: ExportAssetMap\n  ): Promise<{\n    reactClientReferences: string[];\n    reactServerReferences: string[];\n    cssModules: SerialAsset[];\n  }> {\n    const contents = await ssrLoadModuleArtifacts(routerModule, {\n      environment: 'react-server',\n      platform,\n      modulesOnly: true,\n      domRoot,\n    });\n\n    // Extract the global CSS modules that are imported from the router.\n    // These will be injected in the head of the HTML document for the website.\n    const cssModules = contents.artifacts.filter((a) => a.type.startsWith('css'));\n\n    const reactServerReferences = contents.artifacts\n      .filter((a) => a.type === 'js')[0]\n      .metadata.reactServerReferences?.map((ref) => fileURLToFilePath(ref));\n\n    if (!reactServerReferences) {\n      throw new Error(\n        'Static server action references were not returned from the Metro SSR bundle for definedRouter'\n      );\n    }\n    debug('React client boundaries:', reactServerReferences);\n\n    const reactClientReferences = contents.artifacts\n      .filter((a) => a.type === 'js')[0]\n      .metadata.reactClientReferences?.map((ref) => fileURLToFilePath(ref));\n\n    if (!reactClientReferences) {\n      throw new Error(\n        'Static client references were not returned from the Metro SSR bundle for definedRouter'\n      );\n    }\n    debug('React client boundaries:', reactClientReferences);\n\n    // While we're here, export the router for the server to dynamically render RSC.\n    files.set(`_expo/rsc/${platform}/router.js`, {\n      targetDomain: 'server',\n      contents: wrapBundle(contents.src),\n    });\n\n    return { reactClientReferences, reactServerReferences, cssModules };\n  }\n\n  const routerCache = new Map<string, EntriesDev>();\n\n  async function getExpoRouterRscEntriesGetterAsync({\n    platform,\n    routerOptions,\n  }: {\n    platform: string;\n    routerOptions: Record<string, any>;\n  }) {\n    await ensureMemo();\n    // We can only cache this if we're using the client router since it doesn't change or use HMR\n    if (routerCache.has(platform) && useClientRouter) {\n      return routerCache.get(platform)!;\n    }\n\n    const router = await ssrLoadModule<\n      typeof import('expo-router/build/rsc/router/expo-definedRouter')\n    >(\n      routerModule,\n      {\n        environment: 'react-server',\n        modulesOnly: true,\n        platform,\n      },\n      {\n        hot: !useClientRouter,\n      }\n    );\n\n    const entries = router.default({\n      redirects: routerOptions?.redirects,\n      rewrites: routerOptions?.rewrites,\n    });\n\n    routerCache.set(platform, entries);\n    return entries;\n  }\n\n  function getResolveClientEntry(context: {\n    platform: string;\n    engine?: 'hermes' | null;\n    ssrManifest?: Map<string, string>;\n  }): (\n    file: string,\n    isServer: boolean\n  ) => {\n    id: string;\n    chunks: string[];\n  } {\n    const serverRoot = getMetroServerRootMemo(projectRoot);\n\n    const {\n      mode,\n      minify = false,\n      isExporting,\n      baseUrl,\n      routerRoot,\n      asyncRoutes,\n      preserveEnvVars,\n      reactCompiler,\n      lazy,\n    } = instanceMetroOptions;\n\n    assert(\n      isExporting != null &&\n        baseUrl != null &&\n        mode != null &&\n        routerRoot != null &&\n        asyncRoutes != null,\n      `The server must be started. (isExporting: ${isExporting}, baseUrl: ${baseUrl}, mode: ${mode}, routerRoot: ${routerRoot}, asyncRoutes: ${asyncRoutes})`\n    );\n\n    return (file: string, isServer: boolean) => {\n      const filePath = path.join(\n        projectRoot,\n        file.startsWith('file://') ? fileURLToFilePath(file) : file\n      );\n\n      if (isExporting) {\n        assert(context.ssrManifest, 'SSR manifest must exist when exporting');\n\n        const relativeFilePath = toPosixPath(path.relative(serverRoot, filePath));\n\n        assert(\n          context.ssrManifest.has(relativeFilePath),\n          `SSR manifest is missing client boundary \"${relativeFilePath}\"`\n        );\n\n        const chunk = context.ssrManifest.get(relativeFilePath);\n\n        return {\n          id: String(\n            createModuleId(filePath, { platform: context.platform, environment: 'client' })\n          ),\n          chunks: chunk != null ? [chunk] : [],\n        };\n      }\n\n      const environment = isServer ? 'react-server' : 'client';\n      const searchParams = createBundleUrlSearchParams({\n        mainModuleName: '',\n        platform: context.platform,\n        mode,\n        minify,\n        lazy,\n        preserveEnvVars,\n        asyncRoutes,\n        baseUrl,\n        routerRoot,\n        isExporting,\n        reactCompiler: !!reactCompiler,\n        engine: context.engine ?? undefined,\n        bytecode: false,\n        clientBoundaries: [],\n        inlineSourceMap: false,\n        environment,\n        modulesOnly: true,\n        runModule: false,\n      });\n\n      searchParams.set('resolver.clientboundary', String(true));\n\n      const clientReferenceUrl = new URL('http://a');\n\n      // TICKLE: Handshake 1\n      searchParams.set('xRSC', '1');\n\n      clientReferenceUrl.search = searchParams.toString();\n\n      const relativeFilePath = path.relative(serverRoot, filePath);\n\n      clientReferenceUrl.pathname = relativeFilePath;\n\n      // Ensure url.pathname ends with '.bundle'\n      if (!clientReferenceUrl.pathname.endsWith('.bundle')) {\n        clientReferenceUrl.pathname += '.bundle';\n      }\n\n      // Return relative URLs to help Android fetch from wherever it was loaded from since it doesn't support localhost.\n      const chunkName = clientReferenceUrl.pathname + clientReferenceUrl.search;\n\n      return {\n        id: String(createModuleId(filePath, { platform: context.platform, environment })),\n        chunks: [chunkName],\n      };\n    };\n  }\n\n  const rscRendererCache = new Map<string, typeof import('expo-router/build/rsc/rsc-renderer')>();\n\n  let ensurePromise: Promise<any> | null = null;\n  async function ensureSSRReady() {\n    // TODO: Extract CSS Modules / Assets from the bundler process\n    const runtime = await ssrLoadModule<typeof import('expo-router/build/rsc/rsc-renderer')>(\n      'metro-runtime/src/modules/empty-module.js',\n      {\n        environment: 'react-server',\n        platform: 'web',\n      }\n    );\n    return runtime;\n  }\n  const ensureMemo = () => {\n    ensurePromise ??= ensureSSRReady();\n    return ensurePromise;\n  };\n\n  async function getRscRendererAsync(platform: string) {\n    await ensureMemo();\n    // NOTE(EvanBacon): We memoize this now that there's a persistent server storage cache for Server Actions.\n    if (rscRendererCache.has(platform)) {\n      return rscRendererCache.get(platform)!;\n    }\n\n    // TODO: Extract CSS Modules / Assets from the bundler process\n    const renderer = await ssrLoadModule<typeof import('expo-router/build/rsc/rsc-renderer')>(\n      'expo-router/build/rsc/rsc-renderer',\n      {\n        environment: 'react-server',\n        platform,\n      }\n    );\n\n    rscRendererCache.set(platform, renderer);\n    return renderer;\n  }\n\n  const rscRenderContext = new Map<string, any>();\n\n  function getRscRenderContext(platform: string) {\n    // NOTE(EvanBacon): We memoize this now that there's a persistent server storage cache for Server Actions.\n    if (rscRenderContext.has(platform)) {\n      return rscRenderContext.get(platform)!;\n    }\n\n    const context = {};\n\n    rscRenderContext.set(platform, context);\n    return context;\n  }\n\n  async function renderRscToReadableStream(\n    {\n      input,\n      headers,\n      method,\n      platform,\n      body,\n      engine,\n      contentType,\n      ssrManifest,\n      decodedBody,\n      routerOptions,\n    }: {\n      input: string;\n      headers: Headers;\n      method: 'POST' | 'GET';\n      platform: string;\n      body?: ReadableStream<Uint8Array>;\n      engine?: 'hermes' | null;\n      contentType?: string;\n      ssrManifest?: Map<string, string>;\n      decodedBody?: unknown;\n      routerOptions: Record<string, any>;\n    },\n    isExporting: boolean | undefined = instanceMetroOptions.isExporting\n  ) {\n    assert(\n      isExporting != null,\n      'The server must be started before calling renderRscToReadableStream.'\n    );\n\n    if (method === 'POST') {\n      assert(body, 'Server request must be provided when method is POST (server actions)');\n    }\n\n    const context = getRscRenderContext(platform);\n\n    context['__expo_requestHeaders'] = headers;\n\n    const { renderRsc } = await getRscRendererAsync(platform);\n\n    return renderRsc(\n      {\n        body,\n        decodedBody,\n        context,\n        config: {},\n        input,\n        contentType,\n      },\n      {\n        isExporting,\n        entries: await getExpoRouterRscEntriesGetterAsync({ platform, routerOptions }),\n        resolveClientEntry: getResolveClientEntry({ platform, engine, ssrManifest }),\n        async loadServerModuleRsc(urlFragment) {\n          const serverRoot = getMetroServerRootMemo(projectRoot);\n\n          debug('[SSR] loadServerModuleRsc:', urlFragment);\n\n          const options = getMetroOptionsFromUrl(urlFragment);\n\n          return ssrLoadModule(\n            path.join(serverRoot, options.mainModuleName),\n\n            options,\n            {\n              hot: true,\n            }\n          );\n        },\n      }\n    );\n  }\n\n  return {\n    // Get the static client boundaries (no dead code elimination allowed) for the production export.\n    getExpoRouterClientReferencesAsync,\n    exportServerActionsAsync,\n\n    async exportRoutesAsync(\n      {\n        platform,\n        ssrManifest,\n        routerOptions,\n      }: {\n        platform: string;\n        ssrManifest: Map<string, string>;\n        routerOptions: Record<string, any>;\n      },\n      files: ExportAssetMap\n    ) {\n      // TODO: When we add web SSR support, we need to extract CSS Modules / Assets from the bundler process to prevent FLOUC.\n      const { getBuildConfig } = (\n        await getExpoRouterRscEntriesGetterAsync({ platform, routerOptions })\n      ).default;\n\n      // Get all the routes to render.\n      const buildConfig = await getBuildConfig!(async () =>\n        // TODO: Rework prefetching code to use Metro runtime.\n        []\n      );\n\n      await Promise.all(\n        Array.from(buildConfig).map(async ({ entries }) => {\n          for (const { input, isStatic } of entries || []) {\n            if (!isStatic) {\n              debug('Skipping static export for route', { input });\n              continue;\n            }\n            const destRscFile = path.join('_flight', platform, encodeInput(input));\n\n            const pipe = await renderRscToReadableStream(\n              {\n                input,\n                method: 'GET',\n                platform,\n                headers: new Headers(),\n                ssrManifest,\n                routerOptions,\n              },\n              true\n            );\n\n            const rsc = await streamToStringAsync(pipe);\n            debug('RSC Payload', { platform, input, rsc });\n\n            files.set(destRscFile, {\n              contents: rsc,\n              targetDomain: 'client',\n              rscId: input,\n            });\n          }\n        })\n      );\n    },\n\n    middleware: createBuiltinAPIRequestHandler(\n      // Match `/_flight/[platform]/[...path]`\n      (req) => {\n        return getFullUrl(req.url).pathname.startsWith(rscPathPrefix);\n      },\n      rscMiddleware\n    ),\n    onReloadRscEvent: (platform: string) => {\n      // NOTE: We cannot clear the renderer context because it would break the mounted context state.\n\n      rscRendererCache.delete(platform);\n      routerCache.delete(platform);\n    },\n  };\n}\n\nconst getFullUrl = (url: string) => {\n  try {\n    return new URL(url);\n  } catch {\n    return new URL(url, 'http://localhost:0');\n  }\n};\n\nexport const fileURLToFilePath = (fileURL: string) => {\n  try {\n    return url.fileURLToPath(fileURL);\n  } catch (error) {\n    if (error instanceof TypeError) {\n      throw Error(`Invalid URL: ${fileURL}`, { cause: error });\n    }\n    throw error;\n  }\n};\n\nconst encodeInput = (input: string) => {\n  if (input === '') {\n    return 'index.txt';\n  }\n  if (input === 'index') {\n    throw new Error('Input should not be `index`');\n  }\n  if (input.startsWith('/')) {\n    throw new Error('Input should not start with `/`');\n  }\n  if (input.endsWith('/')) {\n    throw new Error('Input should not end with `/`');\n  }\n  return input + '.txt';\n};\n\nfunction wrapBundle(str: string) {\n  // Skip the metro runtime so debugging is a bit easier.\n  // Replace the __r() call with an export statement.\n  // Use gm to apply to the last require line. This is needed when the bundle has side-effects.\n  return str.replace(/^(__r\\(.*\\);)$/gm, 'module.exports = $1');\n}\n"], "names": ["createServerComponentsMiddleware", "fileURLToFilePath", "debug", "require", "getMetroServerRootMemo", "memoize", "getMetroServerRoot", "projectRoot", "rscPath", "instanceMetroOptions", "ssrLoadModule", "ssrLoadModuleArtifacts", "useClientRouter", "createModuleId", "routerOptions", "routerModule", "rscMiddleware", "getRscMiddleware", "config", "baseUrl", "onError", "console", "error", "renderRsc", "args", "headers", "getIpAddress", "renderRscToReadableStream", "Headers", "body", "logMetroError", "IS_METRO_BUNDLE_ERROR_SYMBOL", "Response", "JSON", "stringify", "status", "isPossiblyUnableToResolveError", "sanitizedServerMessage", "stripAnsi", "message", "rscPathPrefix", "exportServerActionsAsync", "platform", "entryPoints", "domRoot", "files", "uniqueEntryPoints", "Set", "manifest", "nestedClientBoundaries", "nestedServerBoundaries", "processedEntryPoints", "processEntryPoint", "entryPoint", "contents", "add", "environment", "modulesOnly", "runModule", "reactClientReferences", "artifacts", "filter", "a", "type", "metadata", "map", "ref", "push", "reactServerReferences", "src", "includes", "Error", "relativeName", "safeName", "path", "basename", "find", "filename", "outputName", "set", "targetDomain", "wrapBundle", "publicModuleId", "toPosixPath", "relative", "String", "processEntryPoints", "recursions", "uniqueNestedServerBoundaries", "value", "has", "length", "clientBoundaries", "getExpoRouterClientReferencesAsync", "cssModules", "startsWith", "routerCache", "Map", "getExpoRouterRscEntriesGetterAsync", "ensureMemo", "get", "router", "hot", "entries", "default", "redirects", "rewrites", "getResolveClientEntry", "context", "serverRoot", "mode", "minify", "isExporting", "routerRoot", "asyncRoutes", "preserveEnvVars", "reactCompiler", "lazy", "assert", "file", "isServer", "filePath", "join", "ssrManifest", "relativeFilePath", "chunk", "id", "chunks", "searchParams", "createBundleUrlSearchParams", "mainModuleName", "engine", "undefined", "bytecode", "inlineSourceMap", "clientReferenceUrl", "URL", "search", "toString", "pathname", "endsWith", "chunkName", "rsc<PERSON><PERSON><PERSON><PERSON><PERSON>", "ensurePromise", "ensureSSRReady", "runtime", "getRscRendererAsync", "renderer", "rscRenderContext", "getRscRenderContext", "input", "method", "contentType", "decodedBody", "resolveClientEntry", "loadServerModuleRsc", "urlFragment", "options", "getMetroOptionsFromUrl", "exportRoutesAsync", "getBuildConfig", "buildConfig", "Promise", "all", "Array", "from", "isStatic", "destRscFile", "encodeInput", "pipe", "rsc", "streamToStringAsync", "rscId", "middleware", "createBuiltinAPIRequestHandler", "req", "getFullUrl", "url", "onReloadRscEvent", "delete", "fileURL", "fileURLToPath", "TypeError", "cause", "str", "replace"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IAuCeA,gCAAgC;eAAhCA;;IAomBHC,iBAAiB;eAAjBA;;;;yBA1oBsB;;;;;;;yBAEF;;;;;;;gEACd;;;;;;;gEAEF;;;;;;;gEACD;;;;;;qCAE4C;qCACb;sBAErB;0BACE;oBACJ;oBACK;wBACO;gDACW;8BAKxC;;;;;;AAEP,MAAMC,QAAQC,QAAQ,SAAS;AAa/B,MAAMC,yBAAyBC,IAAAA,WAAO,EAACC,2BAAkB;AAElD,SAASN,iCACdO,WAAmB,EACnB,EACEC,OAAO,EACPC,oBAAoB,EACpBC,aAAa,EACbC,sBAAsB,EACtBC,eAAe,EACfC,cAAc,EACdC,aAAa,EAYd;IAED,MAAMC,eAAeH,kBACjB,4CACA;IAEJ,MAAMI,gBAAgBC,IAAAA,2BAAgB,EAAC;QACrCC,QAAQ,CAAC;QACT,0BAA0B;QAC1BC,SAAS;QACTX;QACAY,SAASC,QAAQC,KAAK;QACtBC,WAAW,OAAOC;YAChB,gFAAgF;YAChF,IAAIA,KAAKC,OAAO,CAAC,YAAY,IAAI,MAAM;gBACrCD,KAAKC,OAAO,CAAC,YAAY,GAAGC,IAAAA,gBAAY;YAC1C;YACA,IAAIF,KAAKC,OAAO,CAAC,kBAAkB,IAAI,MAAM;gBAC3CD,KAAKC,OAAO,CAAC,kBAAkB,GAAGD,KAAKC,OAAO,CAAC,YAAY;YAC7D;YACA,IAAID,KAAKC,OAAO,CAAC,oBAAoB,IAAI,MAAM;gBAC7CD,KAAKC,OAAO,CAAC,oBAAoB,GAAG;YACtC;YAEA,kCAAkC;YAClC,IAAI;gBACF,OAAO,MAAME,0BAA0B;oBACrC,GAAGH,IAAI;oBACPC,SAAS,IAAIG,QAAQJ,KAAKC,OAAO;oBACjCI,MAAML,KAAKK,IAAI;oBACff;gBACF;YACF,EAAE,OAAOQ,OAAY;gBACnB,kHAAkH;gBAClH,mBAAmB;gBACnB,IAAI;gBACJ,iBAAiB;gBACjB,8BAA8B;gBAC9B,qBAAqB;gBACrB,4BAA4B;gBAC5B,2BAA2B;gBAC3B,sBAAsB;gBACtB,MAAM;gBACN,IAAI;gBAEJ,2EAA2E;gBAC3E,MAAMQ,IAAAA,kCAAa,EAACvB,aAAa;oBAAEe;gBAAM;gBAEzC,IAAIA,KAAK,CAACS,iDAA4B,CAAC,EAAE;oBACvC,MAAM,IAAIC,SAASC,KAAKC,SAAS,CAACZ,QAAQ;wBACxCa,QAAQC,IAAAA,mDAA8B,EAACd,SAAS,MAAM;wBACtDG,SAAS;4BACP,gBAAgB;wBAClB;oBACF;gBACF;gBAEA,MAAMY,yBAAyBC,IAAAA,eAAS,EAAChB,MAAMiB,OAAO,KAAKjB,MAAMiB,OAAO;gBACxE,MAAM,IAAIP,SAASK,wBAAwB;oBACzCF,QAAQ;oBACRV,SAAS;wBACP,gBAAgB;oBAClB;gBACF;YACF;QACF;IACF;IAEA,IAAIe,gBAAgBhC;IACpB,IAAIgC,kBAAkB,KAAK;QACzBA,iBAAiB;IACnB;IAEA,eAAeC,yBACb,EACEC,QAAQ,EACRC,WAAW,EACXC,OAAO,EACuD,EAChEC,KAAqB;QAKrB,MAAMC,oBAAoB;eAAI,IAAIC,IAAIJ;SAAa;QACnD,yEAAyE;QACzE,MAAMK,WAA6C,CAAC;QACpD,MAAMC,yBAAmC,EAAE;QAC3C,MAAMC,yBAAmC,EAAE;QAC3C,MAAMC,uBAAuB,IAAIJ;QACjC,eAAeK,kBAAkBC,UAAkB;gBAcnBC,4DAOAA;YApB9BH,qBAAqBI,GAAG,CAACF;YAEzB,MAAMC,WAAW,MAAM3C,uBAAuB0C,YAAY;gBACxDG,aAAa;gBACbd;gBACA,+EAA+E;gBAC/Ee,aAAa;gBACb,WAAW;gBACXC,WAAW;gBACX,uDAAuD;gBACvDd;YACF;YAEA,MAAMe,yBAAwBL,6DAAAA,SAASM,SAAS,CAC7CC,MAAM,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAK,KAAK,CAAC,EAAE,CACjCC,QAAQ,CAACL,qBAAqB,qBAFHL,2DAEKW,GAAG,CAAC,CAACC,MAAQjE,kBAAkBiE;YAElE,IAAIP,uBAAuB;gBACzBV,uBAAuBkB,IAAI,IAAIR;YACjC;YACA,MAAMS,yBAAwBd,6DAAAA,SAASM,SAAS,CAC7CC,MAAM,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAK,KAAK,CAAC,EAAE,CACjCC,QAAQ,CAACI,qBAAqB,qBAFHd,2DAEKW,GAAG,CAAC,CAACC,MAAQjE,kBAAkBiE;YAElE,IAAIE,uBAAuB;gBACzBlB,uBAAuBiB,IAAI,IAAIC;YACjC;YAEA,wFAAwF;YACxF,IAAId,SAASe,GAAG,CAACC,QAAQ,CAAC,mCAAmC;gBAC3D,MAAM,IAAIC,MACR,qFACElB;YAEN;YAEA,MAAMmB,eAAe3D,eAAewC,YAAY;gBAC9CX;gBACAc,aAAa;YACf;YACA,MAAMiB,WAAWC,eAAI,CAACC,QAAQ,CAACrB,SAASM,SAAS,CAACgB,IAAI,CAAC,CAACd,IAAMA,EAAEC,IAAI,KAAK,MAAOc,QAAQ;YAExF,MAAMC,aAAa,CAAC,UAAU,EAAEpC,SAAS,CAAC,EAAE+B,UAAU;YACtD,gFAAgF;YAChF5B,MAAMkC,GAAG,CAACD,YAAY;gBACpBE,cAAc;gBACd1B,UAAU2B,WAAW3B,SAASe,GAAG;YACnC;YAEA,sBAAsB;YACtB,MAAMa,iBAAiB,OAAOC,IAAAA,qBAAW,EAACT,eAAI,CAACU,QAAQ,CAAC7E,aAAa8C;YAErE,2DAA2D;YAC3DL,QAAQ,CAACkC,eAAe,GAAG;gBAACG,OAAOb;gBAAeM;aAAW;QAC/D;QAEA,eAAeQ,mBAAmB3C,WAAqB,EAAE4C,aAAa,CAAC;YACrE,uDAAuD;YACvD,IAAIA,aAAa,IAAI;gBACnB,MAAM,IAAIhB,MAAM;YAClB;YAEA,KAAK,MAAMlB,cAAcV,YAAa;gBACpC,MAAMS,kBAAkBC;YAC1B;YAEA,iKAAiK;YACjK,IAAImC,+BAA+B;mBAAI,IAAIzC,IAAIG;aAAwB;YACvE,sDAAsD;YACtDsC,+BAA+BA,6BAA6B3B,MAAM,CAChE,CAAC4B,QAAU,CAACtC,qBAAqBuC,GAAG,CAACD;YAEvC,IAAID,6BAA6BG,MAAM,EAAE;gBACvCzF,MAAM,4CAA4CsF;gBAClD,OAAOF,mBAAmBE,8BAA8BD,aAAa;YACvE;QACF;QAEA,MAAMD,mBAAmBxC;QAEzB,4GAA4G;QAC5GD,MAAMkC,GAAG,CAAC,CAAC,UAAU,EAAErC,SAAS,mBAAmB,CAAC,EAAE;YACpDsC,cAAc;YACd1B,UAAU,sBAAsBrB,KAAKC,SAAS,CAACc;QACjD;QAEA,OAAO;YAAEA;YAAU4C,kBAAkB3C;QAAuB;IAC9D;IAEA,eAAe4C,mCACb,EAAEnD,QAAQ,EAAEE,OAAO,EAA0C,EAC7DC,KAAqB;YAiBSS,4DAWAA;QAtB9B,MAAMA,WAAW,MAAM3C,uBAAuBI,cAAc;YAC1DyC,aAAa;YACbd;YACAe,aAAa;YACbb;QACF;QAEA,oEAAoE;QACpE,2EAA2E;QAC3E,MAAMkD,aAAaxC,SAASM,SAAS,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,IAAI,CAACgC,UAAU,CAAC;QAEtE,MAAM3B,yBAAwBd,6DAAAA,SAASM,SAAS,CAC7CC,MAAM,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAK,KAAK,CAAC,EAAE,CACjCC,QAAQ,CAACI,qBAAqB,qBAFHd,2DAEKW,GAAG,CAAC,CAACC,MAAQjE,kBAAkBiE;QAElE,IAAI,CAACE,uBAAuB;YAC1B,MAAM,IAAIG,MACR;QAEJ;QACArE,MAAM,4BAA4BkE;QAElC,MAAMT,yBAAwBL,6DAAAA,SAASM,SAAS,CAC7CC,MAAM,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAK,KAAK,CAAC,EAAE,CACjCC,QAAQ,CAACL,qBAAqB,qBAFHL,2DAEKW,GAAG,CAAC,CAACC,MAAQjE,kBAAkBiE;QAElE,IAAI,CAACP,uBAAuB;YAC1B,MAAM,IAAIY,MACR;QAEJ;QACArE,MAAM,4BAA4ByD;QAElC,gFAAgF;QAChFd,MAAMkC,GAAG,CAAC,CAAC,UAAU,EAAErC,SAAS,UAAU,CAAC,EAAE;YAC3CsC,cAAc;YACd1B,UAAU2B,WAAW3B,SAASe,GAAG;QACnC;QAEA,OAAO;YAAEV;YAAuBS;YAAuB0B;QAAW;IACpE;IAEA,MAAME,cAAc,IAAIC;IAExB,eAAeC,mCAAmC,EAChDxD,QAAQ,EACR5B,aAAa,EAId;QACC,MAAMqF;QACN,6FAA6F;QAC7F,IAAIH,YAAYN,GAAG,CAAChD,aAAa9B,iBAAiB;YAChD,OAAOoF,YAAYI,GAAG,CAAC1D;QACzB;QAEA,MAAM2D,SAAS,MAAM3F,cAGnBK,cACA;YACEyC,aAAa;YACbC,aAAa;YACbf;QACF,GACA;YACE4D,KAAK,CAAC1F;QACR;QAGF,MAAM2F,UAAUF,OAAOG,OAAO,CAAC;YAC7BC,SAAS,EAAE3F,iCAAAA,cAAe2F,SAAS;YACnCC,QAAQ,EAAE5F,iCAAAA,cAAe4F,QAAQ;QACnC;QAEAV,YAAYjB,GAAG,CAACrC,UAAU6D;QAC1B,OAAOA;IACT;IAEA,SAASI,sBAAsBC,OAI9B;QAOC,MAAMC,aAAazG,uBAAuBG;QAE1C,MAAM,EACJuG,IAAI,EACJC,SAAS,KAAK,EACdC,WAAW,EACX7F,OAAO,EACP8F,UAAU,EACVC,WAAW,EACXC,eAAe,EACfC,aAAa,EACbC,IAAI,EACL,GAAG5G;QAEJ6G,IAAAA,iBAAM,EACJN,eAAe,QACb7F,WAAW,QACX2F,QAAQ,QACRG,cAAc,QACdC,eAAe,MACjB,CAAC,0CAA0C,EAAEF,YAAY,WAAW,EAAE7F,QAAQ,QAAQ,EAAE2F,KAAK,cAAc,EAAEG,WAAW,eAAe,EAAEC,YAAY,CAAC,CAAC;QAGzJ,OAAO,CAACK,MAAcC;YACpB,MAAMC,WAAW/C,eAAI,CAACgD,IAAI,CACxBnH,aACAgH,KAAKxB,UAAU,CAAC,aAAa9F,kBAAkBsH,QAAQA;YAGzD,IAAIP,aAAa;gBACfM,IAAAA,iBAAM,EAACV,QAAQe,WAAW,EAAE;gBAE5B,MAAMC,mBAAmBzC,IAAAA,qBAAW,EAACT,eAAI,CAACU,QAAQ,CAACyB,YAAYY;gBAE/DH,IAAAA,iBAAM,EACJV,QAAQe,WAAW,CAACjC,GAAG,CAACkC,mBACxB,CAAC,yCAAyC,EAAEA,iBAAiB,CAAC,CAAC;gBAGjE,MAAMC,QAAQjB,QAAQe,WAAW,CAACvB,GAAG,CAACwB;gBAEtC,OAAO;oBACLE,IAAIzC,OACFxE,eAAe4G,UAAU;wBAAE/E,UAAUkE,QAAQlE,QAAQ;wBAAEc,aAAa;oBAAS;oBAE/EuE,QAAQF,SAAS,OAAO;wBAACA;qBAAM,GAAG,EAAE;gBACtC;YACF;YAEA,MAAMrE,cAAcgE,WAAW,iBAAiB;YAChD,MAAMQ,eAAeC,IAAAA,yCAA2B,EAAC;gBAC/CC,gBAAgB;gBAChBxF,UAAUkE,QAAQlE,QAAQ;gBAC1BoE;gBACAC;gBACAM;gBACAF;gBACAD;gBACA/F;gBACA8F;gBACAD;gBACAI,eAAe,CAAC,CAACA;gBACjBe,QAAQvB,QAAQuB,MAAM,IAAIC;gBAC1BC,UAAU;gBACVzC,kBAAkB,EAAE;gBACpB0C,iBAAiB;gBACjB9E;gBACAC,aAAa;gBACbC,WAAW;YACb;YAEAsE,aAAajD,GAAG,CAAC,2BAA2BM,OAAO;YAEnD,MAAMkD,qBAAqB,IAAIC,IAAI;YAEnC,sBAAsB;YACtBR,aAAajD,GAAG,CAAC,QAAQ;YAEzBwD,mBAAmBE,MAAM,GAAGT,aAAaU,QAAQ;YAEjD,MAAMd,mBAAmBlD,eAAI,CAACU,QAAQ,CAACyB,YAAYY;YAEnDc,mBAAmBI,QAAQ,GAAGf;YAE9B,0CAA0C;YAC1C,IAAI,CAACW,mBAAmBI,QAAQ,CAACC,QAAQ,CAAC,YAAY;gBACpDL,mBAAmBI,QAAQ,IAAI;YACjC;YAEA,kHAAkH;YAClH,MAAME,YAAYN,mBAAmBI,QAAQ,GAAGJ,mBAAmBE,MAAM;YAEzE,OAAO;gBACLX,IAAIzC,OAAOxE,eAAe4G,UAAU;oBAAE/E,UAAUkE,QAAQlE,QAAQ;oBAAEc;gBAAY;gBAC9EuE,QAAQ;oBAACc;iBAAU;YACrB;QACF;IACF;IAEA,MAAMC,mBAAmB,IAAI7C;IAE7B,IAAI8C,gBAAqC;IACzC,eAAeC;QACb,8DAA8D;QAC9D,MAAMC,UAAU,MAAMvI,cACpB,6CACA;YACE8C,aAAa;YACbd,UAAU;QACZ;QAEF,OAAOuG;IACT;IACA,MAAM9C,aAAa;QACjB4C,kBAAkBC;QAClB,OAAOD;IACT;IAEA,eAAeG,oBAAoBxG,QAAgB;QACjD,MAAMyD;QACN,0GAA0G;QAC1G,IAAI2C,iBAAiBpD,GAAG,CAAChD,WAAW;YAClC,OAAOoG,iBAAiB1C,GAAG,CAAC1D;QAC9B;QAEA,8DAA8D;QAC9D,MAAMyG,WAAW,MAAMzI,cACrB,sCACA;YACE8C,aAAa;YACbd;QACF;QAGFoG,iBAAiB/D,GAAG,CAACrC,UAAUyG;QAC/B,OAAOA;IACT;IAEA,MAAMC,mBAAmB,IAAInD;IAE7B,SAASoD,oBAAoB3G,QAAgB;QAC3C,0GAA0G;QAC1G,IAAI0G,iBAAiB1D,GAAG,CAAChD,WAAW;YAClC,OAAO0G,iBAAiBhD,GAAG,CAAC1D;QAC9B;QAEA,MAAMkE,UAAU,CAAC;QAEjBwC,iBAAiBrE,GAAG,CAACrC,UAAUkE;QAC/B,OAAOA;IACT;IAEA,eAAejF,0BACb,EACE2H,KAAK,EACL7H,OAAO,EACP8H,MAAM,EACN7G,QAAQ,EACRb,IAAI,EACJsG,MAAM,EACNqB,WAAW,EACX7B,WAAW,EACX8B,WAAW,EACX3I,aAAa,EAYd,EACDkG,cAAmCvG,qBAAqBuG,WAAW;QAEnEM,IAAAA,iBAAM,EACJN,eAAe,MACf;QAGF,IAAIuC,WAAW,QAAQ;YACrBjC,IAAAA,iBAAM,EAACzF,MAAM;QACf;QAEA,MAAM+E,UAAUyC,oBAAoB3G;QAEpCkE,OAAO,CAAC,wBAAwB,GAAGnF;QAEnC,MAAM,EAAEF,SAAS,EAAE,GAAG,MAAM2H,oBAAoBxG;QAEhD,OAAOnB,UACL;YACEM;YACA4H;YACA7C;YACA1F,QAAQ,CAAC;YACToI;YACAE;QACF,GACA;YACExC;YACAT,SAAS,MAAML,mCAAmC;gBAAExD;gBAAU5B;YAAc;YAC5E4I,oBAAoB/C,sBAAsB;gBAAEjE;gBAAUyF;gBAAQR;YAAY;YAC1E,MAAMgC,qBAAoBC,WAAW;gBACnC,MAAM/C,aAAazG,uBAAuBG;gBAE1CL,MAAM,8BAA8B0J;gBAEpC,MAAMC,UAAUC,IAAAA,oCAAsB,EAACF;gBAEvC,OAAOlJ,cACLgE,eAAI,CAACgD,IAAI,CAACb,YAAYgD,QAAQ3B,cAAc,GAE5C2B,SACA;oBACEvD,KAAK;gBACP;YAEJ;QACF;IAEJ;IAEA,OAAO;QACL,iGAAiG;QACjGT;QACApD;QAEA,MAAMsH,mBACJ,EACErH,QAAQ,EACRiF,WAAW,EACX7G,aAAa,EAKd,EACD+B,KAAqB;YAErB,wHAAwH;YACxH,MAAM,EAAEmH,cAAc,EAAE,GAAG,AACzB,CAAA,MAAM9D,mCAAmC;gBAAExD;gBAAU5B;YAAc,EAAC,EACpE0F,OAAO;YAET,gCAAgC;YAChC,MAAMyD,cAAc,MAAMD,eAAgB,UACxC,sDAAsD;gBACtD,EAAE;YAGJ,MAAME,QAAQC,GAAG,CACfC,MAAMC,IAAI,CAACJ,aAAahG,GAAG,CAAC,OAAO,EAAEsC,OAAO,EAAE;gBAC5C,KAAK,MAAM,EAAE+C,KAAK,EAAEgB,QAAQ,EAAE,IAAI/D,WAAW,EAAE,CAAE;oBAC/C,IAAI,CAAC+D,UAAU;wBACbpK,MAAM,oCAAoC;4BAAEoJ;wBAAM;wBAClD;oBACF;oBACA,MAAMiB,cAAc7F,eAAI,CAACgD,IAAI,CAAC,WAAWhF,UAAU8H,YAAYlB;oBAE/D,MAAMmB,OAAO,MAAM9I,0BACjB;wBACE2H;wBACAC,QAAQ;wBACR7G;wBACAjB,SAAS,IAAIG;wBACb+F;wBACA7G;oBACF,GACA;oBAGF,MAAM4J,MAAM,MAAMC,IAAAA,2BAAmB,EAACF;oBACtCvK,MAAM,eAAe;wBAAEwC;wBAAU4G;wBAAOoB;oBAAI;oBAE5C7H,MAAMkC,GAAG,CAACwF,aAAa;wBACrBjH,UAAUoH;wBACV1F,cAAc;wBACd4F,OAAOtB;oBACT;gBACF;YACF;QAEJ;QAEAuB,YAAYC,IAAAA,8DAA8B,EACxC,wCAAwC;QACxC,CAACC;YACC,OAAOC,WAAWD,IAAIE,GAAG,EAAEtC,QAAQ,CAAC5C,UAAU,CAACvD;QACjD,GACAxB;QAEFkK,kBAAkB,CAACxI;YACjB,+FAA+F;YAE/FoG,iBAAiBqC,MAAM,CAACzI;YACxBsD,YAAYmF,MAAM,CAACzI;QACrB;IACF;AACF;AAEA,MAAMsI,aAAa,CAACC;IAClB,IAAI;QACF,OAAO,IAAIzC,IAAIyC;IACjB,EAAE,OAAM;QACN,OAAO,IAAIzC,IAAIyC,KAAK;IACtB;AACF;AAEO,MAAMhL,oBAAoB,CAACmL;IAChC,IAAI;QACF,OAAOH,cAAG,CAACI,aAAa,CAACD;IAC3B,EAAE,OAAO9J,OAAO;QACd,IAAIA,iBAAiBgK,WAAW;YAC9B,MAAM/G,MAAM,CAAC,aAAa,EAAE6G,SAAS,EAAE;gBAAEG,OAAOjK;YAAM;QACxD;QACA,MAAMA;IACR;AACF;AAEA,MAAMkJ,cAAc,CAAClB;IACnB,IAAIA,UAAU,IAAI;QAChB,OAAO;IACT;IACA,IAAIA,UAAU,SAAS;QACrB,MAAM,IAAI/E,MAAM;IAClB;IACA,IAAI+E,MAAMvD,UAAU,CAAC,MAAM;QACzB,MAAM,IAAIxB,MAAM;IAClB;IACA,IAAI+E,MAAMV,QAAQ,CAAC,MAAM;QACvB,MAAM,IAAIrE,MAAM;IAClB;IACA,OAAO+E,QAAQ;AACjB;AAEA,SAASrE,WAAWuG,GAAW;IAC7B,uDAAuD;IACvD,mDAAmD;IACnD,6FAA6F;IAC7F,OAAOA,IAAIC,OAAO,CAAC,oBAAoB;AACzC"}