{"version": 3, "sources": ["../../../../../src/start/server/middleware/ServeStaticMiddleware.ts"], "sourcesContent": ["import path from 'path';\nimport send from 'send';\nimport { parse } from 'url';\n\nimport { parsePlatformHeader } from './resolvePlatform';\nimport { ServerRequest, ServerResponse } from './server.types';\nimport { env } from '../../../utils/env';\n\nconst debug = require('debug')('expo:start:server:middleware:serveStatic') as typeof console.log;\n\n/**\n * Adds support for serving the files in the static `public/` folder to web apps.\n */\nexport class ServeStaticMiddleware {\n  constructor(private projectRoot: string) {}\n  getHandler() {\n    const publicPath = path.join(this.projectRoot, env.EXPO_PUBLIC_FOLDER);\n\n    debug(`Serving static files from:`, publicPath);\n    const opts = {\n      root: publicPath,\n    };\n    return (req: ServerRequest, res: ServerResponse, next: any) => {\n      if (!req?.url || (req.method !== 'GET' && req.method !== 'HEAD')) {\n        return next();\n      }\n\n      const platform = parsePlatformHeader(req);\n      // Currently this is web-only\n      if (platform && platform !== 'web') {\n        return next();\n      }\n\n      const pathname = parse(req.url).pathname;\n      if (!pathname) {\n        return next();\n      }\n\n      debug(`Maybe serve static:`, pathname);\n      const stream = send(req, pathname, opts);\n\n      // add file listener for fallthrough\n      let forwardError = false;\n      stream.on('file', function onFile() {\n        // once file is determined, always forward error\n        forwardError = true;\n      });\n\n      // forward errors\n      stream.on('error', function error(err: any) {\n        if (forwardError || !(err.statusCode < 500)) {\n          next(err);\n          return;\n        }\n\n        next();\n      });\n\n      // pipe\n      stream.pipe(res);\n    };\n  }\n}\n"], "names": ["ServeStaticMiddleware", "debug", "require", "constructor", "projectRoot", "<PERSON><PERSON><PERSON><PERSON>", "publicPath", "path", "join", "env", "EXPO_PUBLIC_FOLDER", "opts", "root", "req", "res", "next", "url", "method", "platform", "parsePlatformHeader", "pathname", "parse", "stream", "send", "forward<PERSON><PERSON>r", "on", "onFile", "error", "err", "statusCode", "pipe"], "mappings": ";;;;+BAaaA;;;eAAAA;;;;gEAbI;;;;;;;gEACA;;;;;;;yBACK;;;;;;iCAEc;qBAEhB;;;;;;AAEpB,MAAMC,QAAQC,QAAQ,SAAS;AAKxB,MAAMF;IACXG,YAAY,AAAQC,WAAmB,CAAE;aAArBA,cAAAA;IAAsB;IAC1CC,aAAa;QACX,MAAMC,aAAaC,eAAI,CAACC,IAAI,CAAC,IAAI,CAACJ,WAAW,EAAEK,QAAG,CAACC,kBAAkB;QAErET,MAAM,CAAC,0BAA0B,CAAC,EAAEK;QACpC,MAAMK,OAAO;YACXC,MAAMN;QACR;QACA,OAAO,CAACO,KAAoBC,KAAqBC;YAC/C,IAAI,EAACF,uBAAAA,IAAKG,GAAG,KAAKH,IAAII,MAAM,KAAK,SAASJ,IAAII,MAAM,KAAK,QAAS;gBAChE,OAAOF;YACT;YAEA,MAAMG,WAAWC,IAAAA,oCAAmB,EAACN;YACrC,6BAA6B;YAC7B,IAAIK,YAAYA,aAAa,OAAO;gBAClC,OAAOH;YACT;YAEA,MAAMK,WAAWC,IAAAA,YAAK,EAACR,IAAIG,GAAG,EAAEI,QAAQ;YACxC,IAAI,CAACA,UAAU;gBACb,OAAOL;YACT;YAEAd,MAAM,CAAC,mBAAmB,CAAC,EAAEmB;YAC7B,MAAME,SAASC,IAAAA,eAAI,EAACV,KAAKO,UAAUT;YAEnC,oCAAoC;YACpC,IAAIa,eAAe;YACnBF,OAAOG,EAAE,CAAC,QAAQ,SAASC;gBACzB,gDAAgD;gBAChDF,eAAe;YACjB;YAEA,iBAAiB;YACjBF,OAAOG,EAAE,CAAC,SAAS,SAASE,MAAMC,GAAQ;gBACxC,IAAIJ,gBAAgB,CAAEI,CAAAA,IAAIC,UAAU,GAAG,GAAE,GAAI;oBAC3Cd,KAAKa;oBACL;gBACF;gBAEAb;YACF;YAEA,OAAO;YACPO,OAAOQ,IAAI,CAAChB;QACd;IACF;AACF"}