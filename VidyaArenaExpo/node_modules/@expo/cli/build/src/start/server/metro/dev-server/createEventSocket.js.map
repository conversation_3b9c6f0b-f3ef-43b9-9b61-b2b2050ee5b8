{"version": 3, "sources": ["../../../../../../src/start/server/metro/dev-server/createEventSocket.ts"], "sourcesContent": ["import { format as prettyFormat, plugins as prettyPlugins } from 'pretty-format';\nimport { WebSocketServer } from 'ws';\n\nimport type { createMessagesSocket } from './createMessageSocket';\nimport { createBroadcaster } from './utils/createSocketBroadcaster';\nimport { createSocketMap } from './utils/createSocketMap';\nimport { parseRawMessage, serializeMessage } from './utils/socketMessages';\n\nconst debug = require('debug')('expo:metro:devserver:eventsSocket') as typeof console.log;\n\ntype EventsSocketOptions = {\n  /** The message endpoint broadcaster, used to relay commands from Metro */\n  broadcast: ReturnType<typeof createMessagesSocket>['broadcast'];\n};\n\n/**\n * Metro events server that dispatches all Metro events to connected clients.\n * This includes logs, errors, bundling progression, etc.\n */\nexport function createEventsSocket(options: EventsSocketOptions) {\n  const clients = createSocketMap();\n  const broadcast = createBroadcaster(clients.map);\n\n  const server = new WebSocketServer({\n    noServer: true,\n    verifyClient({ origin }: { origin: string }) {\n      // This exposes the full JS logs and enables issuing commands like reload\n      // so let's make sure only locally running stuff can connect to it\n      // origin is only checked if it is set, e.g. when the request is made from a (CORS) browser\n      // any 'back-end' connection isn't CORS at all, and has full control over the origin header,\n      // so there is no point in checking it security wise\n      return !origin || origin.startsWith('http://localhost:') || origin.startsWith('file:');\n    },\n  });\n\n  server.on('connection', (socket) => {\n    const client = clients.registerSocket(socket);\n\n    // Register disconnect handlers\n    socket.on('close', client.terminate);\n    socket.on('error', client.terminate);\n    // Register message handler\n    socket.on('message', (data, isBinary) => {\n      const message = parseRawMessage<Command>(data, isBinary);\n      if (!message) return;\n\n      if (message.type === 'command') {\n        options.broadcast(message.command, message.params);\n      } else {\n        debug(`Received unknown message type: ${message.type}`);\n      }\n    });\n  });\n\n  return {\n    endpoint: '/events' as const,\n    server: new WebSocketServer({ noServer: true }),\n    reportMetroEvent: (event: any) => {\n      // Avoid serializing data if there are no clients\n      if (!clients.map.size) {\n        return;\n      }\n\n      return broadcast(null, serializeMetroEvent(event));\n    },\n  };\n}\n\ntype Command = {\n  type: 'command';\n  command: string;\n  params?: any;\n};\n\nfunction serializeMetroEvent(message: any): string {\n  // Some types reported by Metro are not serializable\n  if (message && message.error && message.error instanceof Error) {\n    return serializeMessage({\n      ...message,\n      error: prettyFormat(message.error, {\n        escapeString: true,\n        highlight: true,\n        maxDepth: 3,\n        min: true,\n      }),\n    });\n  }\n\n  if (message && message.type === 'client_log') {\n    return serializeMessage({\n      ...message,\n      data: message.data.map((item: any) =>\n        typeof item === 'string'\n          ? item\n          : prettyFormat(item, {\n              escapeString: true,\n              highlight: true,\n              maxDepth: 3,\n              min: true,\n              plugins: [prettyPlugins.ReactElement],\n            })\n      ),\n    });\n  }\n\n  return serializeMessage(message);\n}\n"], "names": ["createEventsSocket", "debug", "require", "options", "clients", "createSocketMap", "broadcast", "createBroadcaster", "map", "server", "WebSocketServer", "noServer", "verifyClient", "origin", "startsWith", "on", "socket", "client", "registerSocket", "terminate", "data", "isBinary", "message", "parseRawMessage", "type", "command", "params", "endpoint", "reportMetroEvent", "event", "size", "serializeMetroEvent", "error", "Error", "serializeMessage", "prettyFormat", "escapeString", "highlight", "max<PERSON><PERSON><PERSON>", "min", "item", "plugins", "prettyPlugins", "ReactElement"], "mappings": ";;;;+BAmBgBA;;;eAAAA;;;;yBAnBiD;;;;;;;yBACjC;;;;;;yCAGE;iCACF;gCACkB;AAElD,MAAMC,QAAQC,QAAQ,SAAS;AAWxB,SAASF,mBAAmBG,OAA4B;IAC7D,MAAMC,UAAUC,IAAAA,gCAAe;IAC/B,MAAMC,YAAYC,IAAAA,0CAAiB,EAACH,QAAQI,GAAG;IAE/C,MAAMC,SAAS,IAAIC,CAAAA,KAAc,iBAAC,CAAC;QACjCC,UAAU;QACVC,cAAa,EAAEC,MAAM,EAAsB;YACzC,yEAAyE;YACzE,kEAAkE;YAClE,2FAA2F;YAC3F,4FAA4F;YAC5F,oDAAoD;YACpD,OAAO,CAACA,UAAUA,OAAOC,UAAU,CAAC,wBAAwBD,OAAOC,UAAU,CAAC;QAChF;IACF;IAEAL,OAAOM,EAAE,CAAC,cAAc,CAACC;QACvB,MAAMC,SAASb,QAAQc,cAAc,CAACF;QAEtC,+BAA+B;QAC/BA,OAAOD,EAAE,CAAC,SAASE,OAAOE,SAAS;QACnCH,OAAOD,EAAE,CAAC,SAASE,OAAOE,SAAS;QACnC,2BAA2B;QAC3BH,OAAOD,EAAE,CAAC,WAAW,CAACK,MAAMC;YAC1B,MAAMC,UAAUC,IAAAA,+BAAe,EAAUH,MAAMC;YAC/C,IAAI,CAACC,SAAS;YAEd,IAAIA,QAAQE,IAAI,KAAK,WAAW;gBAC9BrB,QAAQG,SAAS,CAACgB,QAAQG,OAAO,EAAEH,QAAQI,MAAM;YACnD,OAAO;gBACLzB,MAAM,CAAC,+BAA+B,EAAEqB,QAAQE,IAAI,EAAE;YACxD;QACF;IACF;IAEA,OAAO;QACLG,UAAU;QACVlB,QAAQ,IAAIC,CAAAA,KAAc,iBAAC,CAAC;YAAEC,UAAU;QAAK;QAC7CiB,kBAAkB,CAACC;YACjB,iDAAiD;YACjD,IAAI,CAACzB,QAAQI,GAAG,CAACsB,IAAI,EAAE;gBACrB;YACF;YAEA,OAAOxB,UAAU,MAAMyB,oBAAoBF;QAC7C;IACF;AACF;AAQA,SAASE,oBAAoBT,OAAY;IACvC,oDAAoD;IACpD,IAAIA,WAAWA,QAAQU,KAAK,IAAIV,QAAQU,KAAK,YAAYC,OAAO;QAC9D,OAAOC,IAAAA,gCAAgB,EAAC;YACtB,GAAGZ,OAAO;YACVU,OAAOG,IAAAA,sBAAY,EAACb,QAAQU,KAAK,EAAE;gBACjCI,cAAc;gBACdC,WAAW;gBACXC,UAAU;gBACVC,KAAK;YACP;QACF;IACF;IAEA,IAAIjB,WAAWA,QAAQE,IAAI,KAAK,cAAc;QAC5C,OAAOU,IAAAA,gCAAgB,EAAC;YACtB,GAAGZ,OAAO;YACVF,MAAME,QAAQF,IAAI,CAACZ,GAAG,CAAC,CAACgC,OACtB,OAAOA,SAAS,WACZA,OACAL,IAAAA,sBAAY,EAACK,MAAM;oBACjBJ,cAAc;oBACdC,WAAW;oBACXC,UAAU;oBACVC,KAAK;oBACLE,SAAS;wBAACC,uBAAa,CAACC,YAAY;qBAAC;gBACvC;QAER;IACF;IAEA,OAAOT,IAAAA,gCAAgB,EAACZ;AAC1B"}