{"version": 3, "sources": ["../../../../../src/start/server/metro/MetroTerminalReporter.ts"], "sourcesContent": ["import type { Terminal } from '@expo/metro/metro-core';\nimport chalk from 'chalk';\nimport path from 'path';\nimport { stripVTControlCharacters } from 'util';\n\nimport { logWarning, TerminalReporter } from './TerminalReporter';\nimport {\n  BuildPhase,\n  BundleDetails,\n  BundleProgress,\n  SnippetError,\n  TerminalReportableEvent,\n} from './TerminalReporter.types';\nimport { NODE_STDLIB_MODULES } from './externals';\nimport { env } from '../../../utils/env';\nimport { learnMore } from '../../../utils/link';\nimport {\n  logLikeMetro,\n  maybeSymbolicateAndFormatJSErrorStackLogAsync,\n  parseErrorStringToObject,\n} from '../serverLogLikeMetro';\nimport { attachImportStackToRootMessage, nearestImportStack } from './metroErrorInterface';\n\nconst debug = require('debug')('expo:metro:logger') as typeof console.log;\n\nconst MAX_PROGRESS_BAR_CHAR_WIDTH = 16;\nconst DARK_BLOCK_CHAR = '\\u2593';\nconst LIGHT_BLOCK_CHAR = '\\u2591';\n/**\n * Extends the default Metro logger and adds some additional features.\n * Also removes the giant Metro logo from the output.\n */\nexport class MetroTerminalReporter extends TerminalReporter {\n  constructor(\n    public projectRoot: string,\n    terminal: Terminal\n  ) {\n    super(terminal);\n  }\n\n  _log(event: TerminalReportableEvent): void {\n    switch (event.type) {\n      case 'unstable_server_log':\n        if (typeof event.data?.[0] === 'string') {\n          const message = event.data[0];\n          if (message.match(/JavaScript logs have moved/)) {\n            // Hide this very loud message from upstream React Native in favor of the note in the terminal UI:\n            // The \"› Press j │ open debugger\"\n\n            // logger?.info(\n            //   '\\u001B[1m\\u001B[7m💡 JavaScript logs have moved!\\u001B[22m They can now be ' +\n            //     'viewed in React Native DevTools. Tip: Type \\u001B[1mj\\u001B[22m in ' +\n            //     'the terminal to open (requires Google Chrome or Microsoft Edge).' +\n            //     '\\u001B[27m',\n            // );\n            return;\n          }\n\n          if (!env.EXPO_DEBUG) {\n            // In the context of developing an iOS app or website, the MetroInspectorProxy \"connection\" logs are very confusing.\n            // Here we'll hide them behind EXPO_DEBUG or DEBUG=expo:*. In the future we can reformat them to clearly indicate that the \"Connection\" is regarding the debugger.\n            // These logs are also confusing because they can say \"connection established\" even when the debugger is not in a usable state. Really they belong in a UI or behind some sort of debug logging.\n            if (message.match(/Connection (closed|established|failed|terminated)/i)) {\n              // Skip logging.\n              return;\n            }\n          }\n        }\n        break;\n      case 'client_log': {\n        if (this.shouldFilterClientLog(event)) {\n          return;\n        }\n        const { level } = event;\n\n        if (!level) {\n          break;\n        }\n\n        const mode = event.mode === 'NOBRIDGE' || event.mode === 'BRIDGE' ? '' : (event.mode ?? '');\n        // @ts-expect-error\n        if (level === 'warn' || level === 'error') {\n          let hasStack = false;\n          const parsed = event.data.map((msg) => {\n            // Quick check to see if an unsymbolicated stack is being logged.\n            if (msg.includes('.bundle//&platform=')) {\n              const stack = parseErrorStringToObject(msg);\n              if (stack) {\n                hasStack = true;\n              }\n              return stack;\n            }\n            return msg;\n          });\n\n          if (hasStack) {\n            (async () => {\n              const symbolicating = parsed.map((p) => {\n                if (typeof p === 'string') return p;\n                return maybeSymbolicateAndFormatJSErrorStackLogAsync(this.projectRoot, level, p);\n              });\n\n              let usefulStackCount = 0;\n              const fallbackIndices: number[] = [];\n              const symbolicated = (await Promise.allSettled(symbolicating)).map((s, index) => {\n                if (s.status === 'rejected') {\n                  debug('Error formatting stack', parsed[index], s.reason);\n                  return parsed[index];\n                } else if (typeof s.value === 'string') {\n                  return s.value;\n                } else {\n                  if (!s.value.isFallback) {\n                    usefulStackCount++;\n                  } else {\n                    fallbackIndices.push(index);\n                  }\n                  return s.value.stack;\n                }\n              });\n\n              // Using EXPO_DEBUG we can print all stack\n              const filtered =\n                usefulStackCount && !env.EXPO_DEBUG\n                  ? symbolicated.filter((_, index) => !fallbackIndices.includes(index))\n                  : symbolicated;\n\n              logLikeMetro(this.terminal.log.bind(this.terminal), level, mode, ...filtered);\n            })();\n            return;\n          }\n        }\n\n        // Overwrite the Metro terminal logging so we can improve the warnings, symbolicate stacks, and inject extra info.\n        logLikeMetro(this.terminal.log.bind(this.terminal), level, mode, ...event.data);\n        return;\n      }\n    }\n    return super._log(event);\n  }\n\n  // Used for testing\n  _getElapsedTime(startTime: bigint): bigint {\n    return process.hrtime.bigint() - startTime;\n  }\n  /**\n   * Extends the bundle progress to include the current platform that we're bundling.\n   *\n   * @returns `iOS path/to/bundle.js ▓▓▓▓▓░░░░░░░░░░░ 36.6% (4790/7922)`\n   */\n  _getBundleStatusMessage(progress: BundleProgress, phase: BuildPhase): string {\n    const env = getEnvironmentForBuildDetails(progress.bundleDetails);\n    const platform = env || getPlatformTagForBuildDetails(progress.bundleDetails);\n    const inProgress = phase === 'in_progress';\n\n    let localPath: string;\n\n    if (\n      typeof progress.bundleDetails?.customTransformOptions?.dom === 'string' &&\n      progress.bundleDetails.customTransformOptions.dom.includes(path.sep)\n    ) {\n      // Because we use a generated entry file for DOM components, we need to adjust the logging path so it\n      // shows a unique path for each component.\n      // Here, we take the relative import path and remove all the starting slashes.\n      localPath = progress.bundleDetails.customTransformOptions.dom.replace(/^(\\.?\\.[\\\\/])+/, '');\n    } else {\n      const inputFile = progress.bundleDetails.entryFile;\n\n      localPath = path.isAbsolute(inputFile)\n        ? path.relative(this.projectRoot, inputFile)\n        : inputFile;\n    }\n\n    if (!inProgress) {\n      const status = phase === 'done' ? `Bundled ` : `Bundling failed `;\n      const color = phase === 'done' ? chalk.green : chalk.red;\n\n      const startTime = this._bundleTimers.get(progress.bundleDetails.buildID!);\n\n      let time: string = '';\n\n      if (startTime != null) {\n        const elapsed: bigint = this._getElapsedTime(startTime);\n        const micro = Number(elapsed) / 1000;\n        const converted = Number(elapsed) / 1e6;\n        // If the milliseconds are < 0.5 then it will display as 0, so we display in microseconds.\n        if (converted <= 0.5) {\n          const tenthFractionOfMicro = ((micro * 10) / 1000).toFixed(0);\n          // Format as microseconds to nearest tenth\n          time = chalk.cyan.bold(`0.${tenthFractionOfMicro}ms`);\n        } else {\n          time = chalk.dim(converted.toFixed(0) + 'ms');\n        }\n      }\n\n      // iOS Bundled 150ms\n      const plural = progress.totalFileCount === 1 ? '' : 's';\n      return (\n        color(platform + status) +\n        time +\n        chalk.reset.dim(` ${localPath} (${progress.totalFileCount} module${plural})`)\n      );\n    }\n\n    const filledBar = Math.floor(progress.ratio * MAX_PROGRESS_BAR_CHAR_WIDTH);\n\n    const _progress = inProgress\n      ? chalk.green.bgGreen(DARK_BLOCK_CHAR.repeat(filledBar)) +\n        chalk.bgWhite.white(LIGHT_BLOCK_CHAR.repeat(MAX_PROGRESS_BAR_CHAR_WIDTH - filledBar)) +\n        chalk.bold(` ${(100 * progress.ratio).toFixed(1).padStart(4)}% `) +\n        chalk.dim(\n          `(${progress.transformedFileCount\n            .toString()\n            .padStart(progress.totalFileCount.toString().length)}/${progress.totalFileCount})`\n        )\n      : '';\n\n    return (\n      platform +\n      chalk.reset.dim(`${path.dirname(localPath)}${path.sep}`) +\n      chalk.bold(path.basename(localPath)) +\n      ' ' +\n      _progress\n    );\n  }\n\n  _logInitializing(port: number, hasReducedPerformance: boolean): void {\n    // Don't print a giant logo...\n    this.terminal.log(chalk.dim('Starting Metro Bundler'));\n  }\n\n  shouldFilterClientLog(event: { type: 'client_log'; data: unknown[] }): boolean {\n    return isAppRegistryStartupMessage(event.data);\n  }\n\n  shouldFilterBundleEvent(event: TerminalReportableEvent): boolean {\n    return 'bundleDetails' in event && event.bundleDetails?.bundleType === 'map';\n  }\n\n  /** Print the cache clear message. */\n  transformCacheReset(): void {\n    logWarning(\n      this.terminal,\n      chalk`Bundler cache is empty, rebuilding {dim (this may take a minute)}`\n    );\n  }\n\n  /** One of the first logs that will be printed */\n  dependencyGraphLoading(hasReducedPerformance: boolean): void {\n    // this.terminal.log('Dependency graph is loading...');\n    if (hasReducedPerformance) {\n      // Extends https://github.com/facebook/metro/blob/347b1d7ed87995d7951aaa9fd597c04b06013dac/packages/metro/src/lib/TerminalReporter.js#L283-L290\n      this.terminal.log(\n        chalk.red(\n          [\n            'Metro is operating with reduced performance.',\n            'Fix the problem above and restart Metro.',\n          ].join('\\n')\n        )\n      );\n    }\n  }\n\n  _logBundlingError(error: SnippetError): void {\n    const moduleResolutionError = formatUsingNodeStandardLibraryError(this.projectRoot, error);\n    if (moduleResolutionError) {\n      let message = maybeAppendCodeFrame(moduleResolutionError, error.message);\n      message += '\\n\\n' + nearestImportStack(error);\n      return this.terminal.log(message);\n    }\n\n    attachImportStackToRootMessage(error);\n    return super._logBundlingError(error);\n  }\n}\n\n/**\n * Formats an error where the user is attempting to import a module from the Node.js standard library.\n * Exposed for testing.\n *\n * @param error\n * @returns error message or null if not a module resolution error\n */\nexport function formatUsingNodeStandardLibraryError(\n  projectRoot: string,\n  error: SnippetError\n): string | null {\n  if (!error.message) {\n    return null;\n  }\n  const { targetModuleName, originModulePath } = error;\n  if (!targetModuleName || !originModulePath) {\n    return null;\n  }\n  const relativePath = path.relative(projectRoot, originModulePath);\n\n  const DOCS_PAGE_URL =\n    'https://docs.expo.dev/workflow/using-libraries/#using-third-party-libraries';\n\n  if (isNodeStdLibraryModule(targetModuleName)) {\n    if (originModulePath.includes('node_modules')) {\n      return [\n        `The package at \"${chalk.bold(\n          relativePath\n        )}\" attempted to import the Node standard library module \"${chalk.bold(\n          targetModuleName\n        )}\".`,\n        `It failed because the native React runtime does not include the Node standard library.`,\n        learnMore(DOCS_PAGE_URL),\n      ].join('\\n');\n    } else {\n      return [\n        `You attempted to import the Node standard library module \"${chalk.bold(\n          targetModuleName\n        )}\" from \"${chalk.bold(relativePath)}\".`,\n        `It failed because the native React runtime does not include the Node standard library.`,\n        learnMore(DOCS_PAGE_URL),\n      ].join('\\n');\n    }\n  }\n  return `Unable to resolve \"${targetModuleName}\" from \"${relativePath}\"`;\n}\n\nexport function isNodeStdLibraryModule(moduleName: string): boolean {\n  return /^node:/.test(moduleName) || NODE_STDLIB_MODULES.includes(moduleName);\n}\n\n/** If the code frame can be found then append it to the existing message.  */\nfunction maybeAppendCodeFrame(message: string, rawMessage: string): string {\n  const codeFrame = extractCodeFrame(stripMetroInfo(rawMessage));\n  if (codeFrame) {\n    message += '\\n' + codeFrame;\n  }\n  return message;\n}\n\n/** Extract fist code frame presented in the error message */\nexport function extractCodeFrame(errorMessage: string): string {\n  const codeFrameLine = /^(?:\\s*(?:>?\\s*\\d+\\s*\\||\\s*\\|).*\\n?)+/;\n  let wasPreviousLineCodeFrame: boolean | null = null;\n  return errorMessage\n    .split('\\n')\n    .filter((line) => {\n      if (wasPreviousLineCodeFrame === false) return false;\n      const keep = codeFrameLine.test(stripVTControlCharacters(line));\n      if (keep && wasPreviousLineCodeFrame === null) wasPreviousLineCodeFrame = true;\n      else if (!keep && wasPreviousLineCodeFrame) wasPreviousLineCodeFrame = false;\n      return keep;\n    })\n    .join('\\n');\n}\n\n/**\n * Remove the Metro cache clearing steps if they exist.\n * In future versions we won't need this.\n * Returns the remaining code frame logs.\n */\nexport function stripMetroInfo(errorMessage: string): string {\n  // Newer versions of Metro don't include the list.\n  if (!errorMessage.includes('4. Remove the cache')) {\n    return errorMessage;\n  }\n  const lines = errorMessage.split('\\n');\n  const index = lines.findIndex((line) => line.includes('4. Remove the cache'));\n  if (index === -1) {\n    return errorMessage;\n  }\n  return lines.slice(index + 1).join('\\n');\n}\n\n/** @returns if the message matches the initial startup log */\nfunction isAppRegistryStartupMessage(body: any[]): boolean {\n  return (\n    body.length === 1 &&\n    (/^Running application \"main\" with appParams:/.test(body[0]) ||\n      /^Running \"main\" with \\{/.test(body[0]))\n  );\n}\n\n/** @returns platform specific tag for a `BundleDetails` object */\nfunction getPlatformTagForBuildDetails(bundleDetails?: BundleDetails | null): string {\n  const platform = bundleDetails?.platform ?? null;\n  if (platform) {\n    const formatted = { ios: 'iOS', android: 'Android', web: 'Web' }[platform] || platform;\n    return `${chalk.bold(formatted)} `;\n  }\n\n  return '';\n}\n/** @returns platform specific tag for a `BundleDetails` object */\nfunction getEnvironmentForBuildDetails(bundleDetails?: BundleDetails | null): string {\n  // Expo CLI will pass `customTransformOptions.environment = 'node'` when bundling for the server.\n  const env = bundleDetails?.customTransformOptions?.environment ?? null;\n  if (env === 'node') {\n    return chalk.bold('λ') + ' ';\n  } else if (env === 'react-server') {\n    return chalk.bold(`RSC(${getPlatformTagForBuildDetails(bundleDetails).trim()})`) + ' ';\n  }\n\n  if (\n    bundleDetails?.customTransformOptions?.dom &&\n    typeof bundleDetails?.customTransformOptions?.dom === 'string'\n  ) {\n    return chalk.bold(`DOM`) + ' ';\n  }\n\n  return '';\n}\n"], "names": ["MetroTerminalReporter", "extractCodeFrame", "formatUsingNodeStandardLibraryError", "isNodeStdLibraryModule", "stripMetroInfo", "debug", "require", "MAX_PROGRESS_BAR_CHAR_WIDTH", "DARK_BLOCK_CHAR", "LIGHT_BLOCK_CHAR", "TerminalReporter", "constructor", "projectRoot", "terminal", "_log", "event", "type", "data", "message", "match", "env", "EXPO_DEBUG", "shouldFilterClientLog", "level", "mode", "hasStack", "parsed", "map", "msg", "includes", "stack", "parseErrorStringToObject", "symbolicating", "p", "maybeSymbolicateAndFormatJSErrorStackLogAsync", "usefulStackCount", "fallbackIndices", "symbolicated", "Promise", "allSettled", "s", "index", "status", "reason", "value", "<PERSON><PERSON><PERSON><PERSON>", "push", "filtered", "filter", "_", "logLikeMetro", "log", "bind", "_getElapsedTime", "startTime", "process", "hrtime", "bigint", "_getBundleStatusMessage", "progress", "phase", "getEnvironmentForBuildDetails", "bundleDetails", "platform", "getPlatformTagForBuildDetails", "inProgress", "localPath", "customTransformOptions", "dom", "path", "sep", "replace", "inputFile", "entryFile", "isAbsolute", "relative", "color", "chalk", "green", "red", "_bundleTimers", "get", "buildID", "time", "elapsed", "micro", "Number", "converted", "tenthFractionOfMicro", "toFixed", "cyan", "bold", "dim", "plural", "totalFileCount", "reset", "<PERSON><PERSON><PERSON>", "Math", "floor", "ratio", "_progress", "bgGreen", "repeat", "bgWhite", "white", "padStart", "transformedFileCount", "toString", "length", "dirname", "basename", "_logInitializing", "port", "hasReducedPerformance", "isAppRegistryStartupMessage", "shouldFilterBundleEvent", "bundleType", "transformCacheReset", "logWarning", "dependencyGraphLoading", "join", "_logBundlingError", "error", "moduleResolutionError", "maybeAppendCodeFrame", "nearestImportStack", "attachImportStackToRootMessage", "targetModuleName", "originModulePath", "relativePath", "DOCS_PAGE_URL", "learnMore", "moduleName", "test", "NODE_STDLIB_MODULES", "rawMessage", "codeFrame", "errorMessage", "codeFrameLine", "wasPreviousLineCodeFrame", "split", "line", "keep", "stripVTControlCharacters", "lines", "findIndex", "slice", "body", "formatted", "ios", "android", "web", "environment", "trim"], "mappings": ";;;;;;;;;;;IAgCaA,qBAAqB;eAArBA;;IAgTGC,gBAAgB;eAAhBA;;IAtDAC,mCAAmC;eAAnCA;;IAwCAC,sBAAsB;eAAtBA;;IAkCAC,cAAc;eAAdA;;;;gEAnWE;;;;;;;gEACD;;;;;;;yBACwB;;;;;;kCAEI;2BAQT;qBAChB;sBACM;oCAKnB;qCAC4D;;;;;;AAEnE,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,8BAA8B;AACpC,MAAMC,kBAAkB;AACxB,MAAMC,mBAAmB;AAKlB,MAAMT,8BAA8BU,kCAAgB;IACzDC,YACE,AAAOC,WAAmB,EAC1BC,QAAkB,CAClB;QACA,KAAK,CAACA,gBAHCD,cAAAA;IAIT;IAEAE,KAAKC,KAA8B,EAAQ;QACzC,OAAQA,MAAMC,IAAI;YAChB,KAAK;oBACQD;gBAAX,IAAI,SAAOA,cAAAA,MAAME,IAAI,qBAAVF,WAAY,CAAC,EAAE,MAAK,UAAU;oBACvC,MAAMG,UAAUH,MAAME,IAAI,CAAC,EAAE;oBAC7B,IAAIC,QAAQC,KAAK,CAAC,+BAA+B;wBAC/C,kGAAkG;wBAClG,kCAAkC;wBAElC,gBAAgB;wBAChB,oFAAoF;wBACpF,8EAA8E;wBAC9E,2EAA2E;wBAC3E,oBAAoB;wBACpB,KAAK;wBACL;oBACF;oBAEA,IAAI,CAACC,QAAG,CAACC,UAAU,EAAE;wBACnB,oHAAoH;wBACpH,kKAAkK;wBAClK,gMAAgM;wBAChM,IAAIH,QAAQC,KAAK,CAAC,uDAAuD;4BACvE,gBAAgB;4BAChB;wBACF;oBACF;gBACF;gBACA;YACF,KAAK;gBAAc;oBACjB,IAAI,IAAI,CAACG,qBAAqB,CAACP,QAAQ;wBACrC;oBACF;oBACA,MAAM,EAAEQ,KAAK,EAAE,GAAGR;oBAElB,IAAI,CAACQ,OAAO;wBACV;oBACF;oBAEA,MAAMC,OAAOT,MAAMS,IAAI,KAAK,cAAcT,MAAMS,IAAI,KAAK,WAAW,KAAMT,MAAMS,IAAI,IAAI;oBACxF,mBAAmB;oBACnB,IAAID,UAAU,UAAUA,UAAU,SAAS;wBACzC,IAAIE,WAAW;wBACf,MAAMC,SAASX,MAAME,IAAI,CAACU,GAAG,CAAC,CAACC;4BAC7B,iEAAiE;4BACjE,IAAIA,IAAIC,QAAQ,CAAC,wBAAwB;gCACvC,MAAMC,QAAQC,IAAAA,4CAAwB,EAACH;gCACvC,IAAIE,OAAO;oCACTL,WAAW;gCACb;gCACA,OAAOK;4BACT;4BACA,OAAOF;wBACT;wBAEA,IAAIH,UAAU;4BACX,CAAA;gCACC,MAAMO,gBAAgBN,OAAOC,GAAG,CAAC,CAACM;oCAChC,IAAI,OAAOA,MAAM,UAAU,OAAOA;oCAClC,OAAOC,IAAAA,iEAA6C,EAAC,IAAI,CAACtB,WAAW,EAAEW,OAAOU;gCAChF;gCAEA,IAAIE,mBAAmB;gCACvB,MAAMC,kBAA4B,EAAE;gCACpC,MAAMC,eAAe,AAAC,CAAA,MAAMC,QAAQC,UAAU,CAACP,cAAa,EAAGL,GAAG,CAAC,CAACa,GAAGC;oCACrE,IAAID,EAAEE,MAAM,KAAK,YAAY;wCAC3BrC,MAAM,0BAA0BqB,MAAM,CAACe,MAAM,EAAED,EAAEG,MAAM;wCACvD,OAAOjB,MAAM,CAACe,MAAM;oCACtB,OAAO,IAAI,OAAOD,EAAEI,KAAK,KAAK,UAAU;wCACtC,OAAOJ,EAAEI,KAAK;oCAChB,OAAO;wCACL,IAAI,CAACJ,EAAEI,KAAK,CAACC,UAAU,EAAE;4CACvBV;wCACF,OAAO;4CACLC,gBAAgBU,IAAI,CAACL;wCACvB;wCACA,OAAOD,EAAEI,KAAK,CAACd,KAAK;oCACtB;gCACF;gCAEA,0CAA0C;gCAC1C,MAAMiB,WACJZ,oBAAoB,CAACf,QAAG,CAACC,UAAU,GAC/BgB,aAAaW,MAAM,CAAC,CAACC,GAAGR,QAAU,CAACL,gBAAgBP,QAAQ,CAACY,UAC5DJ;gCAENa,IAAAA,gCAAY,EAAC,IAAI,CAACrC,QAAQ,CAACsC,GAAG,CAACC,IAAI,CAAC,IAAI,CAACvC,QAAQ,GAAGU,OAAOC,SAASuB;4BACtE,CAAA;4BACA;wBACF;oBACF;oBAEA,kHAAkH;oBAClHG,IAAAA,gCAAY,EAAC,IAAI,CAACrC,QAAQ,CAACsC,GAAG,CAACC,IAAI,CAAC,IAAI,CAACvC,QAAQ,GAAGU,OAAOC,SAAST,MAAME,IAAI;oBAC9E;gBACF;QACF;QACA,OAAO,KAAK,CAACH,KAAKC;IACpB;IAEA,mBAAmB;IACnBsC,gBAAgBC,SAAiB,EAAU;QACzC,OAAOC,QAAQC,MAAM,CAACC,MAAM,KAAKH;IACnC;IACA;;;;GAIC,GACDI,wBAAwBC,QAAwB,EAAEC,KAAiB,EAAU;YAQlED,gDAAAA;QAPT,MAAMvC,MAAMyC,8BAA8BF,SAASG,aAAa;QAChE,MAAMC,WAAW3C,OAAO4C,8BAA8BL,SAASG,aAAa;QAC5E,MAAMG,aAAaL,UAAU;QAE7B,IAAIM;QAEJ,IACE,SAAOP,0BAAAA,SAASG,aAAa,sBAAtBH,iDAAAA,wBAAwBQ,sBAAsB,qBAA9CR,+CAAgDS,GAAG,MAAK,YAC/DT,SAASG,aAAa,CAACK,sBAAsB,CAACC,GAAG,CAACvC,QAAQ,CAACwC,eAAI,CAACC,GAAG,GACnE;YACA,qGAAqG;YACrG,0CAA0C;YAC1C,8EAA8E;YAC9EJ,YAAYP,SAASG,aAAa,CAACK,sBAAsB,CAACC,GAAG,CAACG,OAAO,CAAC,kBAAkB;QAC1F,OAAO;YACL,MAAMC,YAAYb,SAASG,aAAa,CAACW,SAAS;YAElDP,YAAYG,eAAI,CAACK,UAAU,CAACF,aACxBH,eAAI,CAACM,QAAQ,CAAC,IAAI,CAAC/D,WAAW,EAAE4D,aAChCA;QACN;QAEA,IAAI,CAACP,YAAY;YACf,MAAMvB,SAASkB,UAAU,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACjE,MAAMgB,QAAQhB,UAAU,SAASiB,gBAAK,CAACC,KAAK,GAAGD,gBAAK,CAACE,GAAG;YAExD,MAAMzB,YAAY,IAAI,CAAC0B,aAAa,CAACC,GAAG,CAACtB,SAASG,aAAa,CAACoB,OAAO;YAEvE,IAAIC,OAAe;YAEnB,IAAI7B,aAAa,MAAM;gBACrB,MAAM8B,UAAkB,IAAI,CAAC/B,eAAe,CAACC;gBAC7C,MAAM+B,QAAQC,OAAOF,WAAW;gBAChC,MAAMG,YAAYD,OAAOF,WAAW;gBACpC,0FAA0F;gBAC1F,IAAIG,aAAa,KAAK;oBACpB,MAAMC,uBAAuB,AAAC,CAAA,AAACH,QAAQ,KAAM,IAAG,EAAGI,OAAO,CAAC;oBAC3D,0CAA0C;oBAC1CN,OAAON,gBAAK,CAACa,IAAI,CAACC,IAAI,CAAC,CAAC,EAAE,EAAEH,qBAAqB,EAAE,CAAC;gBACtD,OAAO;oBACLL,OAAON,gBAAK,CAACe,GAAG,CAACL,UAAUE,OAAO,CAAC,KAAK;gBAC1C;YACF;YAEA,oBAAoB;YACpB,MAAMI,SAASlC,SAASmC,cAAc,KAAK,IAAI,KAAK;YACpD,OACElB,MAAMb,WAAWrB,UACjByC,OACAN,gBAAK,CAACkB,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC,EAAE1B,UAAU,EAAE,EAAEP,SAASmC,cAAc,CAAC,OAAO,EAAED,OAAO,CAAC,CAAC;QAEhF;QAEA,MAAMG,YAAYC,KAAKC,KAAK,CAACvC,SAASwC,KAAK,GAAG5F;QAE9C,MAAM6F,YAAYnC,aACdY,gBAAK,CAACC,KAAK,CAACuB,OAAO,CAAC7F,gBAAgB8F,MAAM,CAACN,cAC3CnB,gBAAK,CAAC0B,OAAO,CAACC,KAAK,CAAC/F,iBAAiB6F,MAAM,CAAC/F,8BAA8ByF,cAC1EnB,gBAAK,CAACc,IAAI,CAAC,CAAC,CAAC,EAAE,AAAC,CAAA,MAAMhC,SAASwC,KAAK,AAAD,EAAGV,OAAO,CAAC,GAAGgB,QAAQ,CAAC,GAAG,EAAE,CAAC,IAChE5B,gBAAK,CAACe,GAAG,CACP,CAAC,CAAC,EAAEjC,SAAS+C,oBAAoB,CAC9BC,QAAQ,GACRF,QAAQ,CAAC9C,SAASmC,cAAc,CAACa,QAAQ,GAAGC,MAAM,EAAE,CAAC,EAAEjD,SAASmC,cAAc,CAAC,CAAC,CAAC,IAEtF;QAEJ,OACE/B,WACAc,gBAAK,CAACkB,KAAK,CAACH,GAAG,CAAC,GAAGvB,eAAI,CAACwC,OAAO,CAAC3C,aAAaG,eAAI,CAACC,GAAG,EAAE,IACvDO,gBAAK,CAACc,IAAI,CAACtB,eAAI,CAACyC,QAAQ,CAAC5C,cACzB,MACAkC;IAEJ;IAEAW,iBAAiBC,IAAY,EAAEC,qBAA8B,EAAQ;QACnE,8BAA8B;QAC9B,IAAI,CAACpG,QAAQ,CAACsC,GAAG,CAAC0B,gBAAK,CAACe,GAAG,CAAC;IAC9B;IAEAtE,sBAAsBP,KAA8C,EAAW;QAC7E,OAAOmG,4BAA4BnG,MAAME,IAAI;IAC/C;IAEAkG,wBAAwBpG,KAA8B,EAAW;YAC5BA;QAAnC,OAAO,mBAAmBA,SAASA,EAAAA,uBAAAA,MAAM+C,aAAa,qBAAnB/C,qBAAqBqG,UAAU,MAAK;IACzE;IAEA,mCAAmC,GACnCC,sBAA4B;QAC1BC,IAAAA,4BAAU,EACR,IAAI,CAACzG,QAAQ,EACbgE,IAAAA,gBAAK,CAAA,CAAC,iEAAiE,CAAC;IAE5E;IAEA,+CAA+C,GAC/C0C,uBAAuBN,qBAA8B,EAAQ;QAC3D,uDAAuD;QACvD,IAAIA,uBAAuB;YACzB,+IAA+I;YAC/I,IAAI,CAACpG,QAAQ,CAACsC,GAAG,CACf0B,gBAAK,CAACE,GAAG,CACP;gBACE;gBACA;aACD,CAACyC,IAAI,CAAC;QAGb;IACF;IAEAC,kBAAkBC,KAAmB,EAAQ;QAC3C,MAAMC,wBAAwBzH,oCAAoC,IAAI,CAACU,WAAW,EAAE8G;QACpF,IAAIC,uBAAuB;YACzB,IAAIzG,UAAU0G,qBAAqBD,uBAAuBD,MAAMxG,OAAO;YACvEA,WAAW,SAAS2G,IAAAA,uCAAkB,EAACH;YACvC,OAAO,IAAI,CAAC7G,QAAQ,CAACsC,GAAG,CAACjC;QAC3B;QAEA4G,IAAAA,mDAA8B,EAACJ;QAC/B,OAAO,KAAK,CAACD,kBAAkBC;IACjC;AACF;AASO,SAASxH,oCACdU,WAAmB,EACnB8G,KAAmB;IAEnB,IAAI,CAACA,MAAMxG,OAAO,EAAE;QAClB,OAAO;IACT;IACA,MAAM,EAAE6G,gBAAgB,EAAEC,gBAAgB,EAAE,GAAGN;IAC/C,IAAI,CAACK,oBAAoB,CAACC,kBAAkB;QAC1C,OAAO;IACT;IACA,MAAMC,eAAe5D,eAAI,CAACM,QAAQ,CAAC/D,aAAaoH;IAEhD,MAAME,gBACJ;IAEF,IAAI/H,uBAAuB4H,mBAAmB;QAC5C,IAAIC,iBAAiBnG,QAAQ,CAAC,iBAAiB;YAC7C,OAAO;gBACL,CAAC,gBAAgB,EAAEgD,gBAAK,CAACc,IAAI,CAC3BsC,cACA,wDAAwD,EAAEpD,gBAAK,CAACc,IAAI,CACpEoC,kBACA,EAAE,CAAC;gBACL,CAAC,sFAAsF,CAAC;gBACxFI,IAAAA,eAAS,EAACD;aACX,CAACV,IAAI,CAAC;QACT,OAAO;YACL,OAAO;gBACL,CAAC,0DAA0D,EAAE3C,gBAAK,CAACc,IAAI,CACrEoC,kBACA,QAAQ,EAAElD,gBAAK,CAACc,IAAI,CAACsC,cAAc,EAAE,CAAC;gBACxC,CAAC,sFAAsF,CAAC;gBACxFE,IAAAA,eAAS,EAACD;aACX,CAACV,IAAI,CAAC;QACT;IACF;IACA,OAAO,CAAC,mBAAmB,EAAEO,iBAAiB,QAAQ,EAAEE,aAAa,CAAC,CAAC;AACzE;AAEO,SAAS9H,uBAAuBiI,UAAkB;IACvD,OAAO,SAASC,IAAI,CAACD,eAAeE,8BAAmB,CAACzG,QAAQ,CAACuG;AACnE;AAEA,4EAA4E,GAC5E,SAASR,qBAAqB1G,OAAe,EAAEqH,UAAkB;IAC/D,MAAMC,YAAYvI,iBAAiBG,eAAemI;IAClD,IAAIC,WAAW;QACbtH,WAAW,OAAOsH;IACpB;IACA,OAAOtH;AACT;AAGO,SAASjB,iBAAiBwI,YAAoB;IACnD,MAAMC,gBAAgB;IACtB,IAAIC,2BAA2C;IAC/C,OAAOF,aACJG,KAAK,CAAC,MACN5F,MAAM,CAAC,CAAC6F;QACP,IAAIF,6BAA6B,OAAO,OAAO;QAC/C,MAAMG,OAAOJ,cAAcL,IAAI,CAACU,IAAAA,gCAAwB,EAACF;QACzD,IAAIC,QAAQH,6BAA6B,MAAMA,2BAA2B;aACrE,IAAI,CAACG,QAAQH,0BAA0BA,2BAA2B;QACvE,OAAOG;IACT,GACCtB,IAAI,CAAC;AACV;AAOO,SAASpH,eAAeqI,YAAoB;IACjD,kDAAkD;IAClD,IAAI,CAACA,aAAa5G,QAAQ,CAAC,wBAAwB;QACjD,OAAO4G;IACT;IACA,MAAMO,QAAQP,aAAaG,KAAK,CAAC;IACjC,MAAMnG,QAAQuG,MAAMC,SAAS,CAAC,CAACJ,OAASA,KAAKhH,QAAQ,CAAC;IACtD,IAAIY,UAAU,CAAC,GAAG;QAChB,OAAOgG;IACT;IACA,OAAOO,MAAME,KAAK,CAACzG,QAAQ,GAAG+E,IAAI,CAAC;AACrC;AAEA,4DAA4D,GAC5D,SAASN,4BAA4BiC,IAAW;IAC9C,OACEA,KAAKvC,MAAM,KAAK,KACf,CAAA,8CAA8CyB,IAAI,CAACc,IAAI,CAAC,EAAE,KACzD,0BAA0Bd,IAAI,CAACc,IAAI,CAAC,EAAE,CAAA;AAE5C;AAEA,gEAAgE,GAChE,SAASnF,8BAA8BF,aAAoC;IACzE,MAAMC,WAAWD,CAAAA,iCAAAA,cAAeC,QAAQ,KAAI;IAC5C,IAAIA,UAAU;QACZ,MAAMqF,YAAY;YAAEC,KAAK;YAAOC,SAAS;YAAWC,KAAK;QAAM,CAAC,CAACxF,SAAS,IAAIA;QAC9E,OAAO,GAAGc,gBAAK,CAACc,IAAI,CAACyD,WAAW,CAAC,CAAC;IACpC;IAEA,OAAO;AACT;AACA,gEAAgE,GAChE,SAASvF,8BAA8BC,aAAoC;QAE7DA,uCAQVA,wCACOA;IAVT,iGAAiG;IACjG,MAAM1C,MAAM0C,CAAAA,kCAAAA,wCAAAA,cAAeK,sBAAsB,qBAArCL,sCAAuC0F,WAAW,KAAI;IAClE,IAAIpI,QAAQ,QAAQ;QAClB,OAAOyD,gBAAK,CAACc,IAAI,CAAC,OAAO;IAC3B,OAAO,IAAIvE,QAAQ,gBAAgB;QACjC,OAAOyD,gBAAK,CAACc,IAAI,CAAC,CAAC,IAAI,EAAE3B,8BAA8BF,eAAe2F,IAAI,GAAG,CAAC,CAAC,IAAI;IACrF;IAEA,IACE3F,CAAAA,kCAAAA,yCAAAA,cAAeK,sBAAsB,qBAArCL,uCAAuCM,GAAG,KAC1C,QAAON,kCAAAA,yCAAAA,cAAeK,sBAAsB,qBAArCL,uCAAuCM,GAAG,MAAK,UACtD;QACA,OAAOS,gBAAK,CAACc,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI;IAC7B;IAEA,OAAO;AACT"}