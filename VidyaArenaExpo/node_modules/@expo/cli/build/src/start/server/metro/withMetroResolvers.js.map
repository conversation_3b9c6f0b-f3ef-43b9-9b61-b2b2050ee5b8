{"version": 3, "sources": ["../../../../../src/start/server/metro/withMetroResolvers.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport type { ConfigT as MetroConfig } from '@expo/metro/metro-config';\nimport type {\n  ResolutionContext,\n  CustomResolutionContext,\n  CustomResolver,\n} from '@expo/metro/metro-resolver';\nimport { resolve as metroResolver } from '@expo/metro/metro-resolver';\n\nimport { isFailedToResolveNameError, isFailedToResolvePathError } from './metroErrors';\n\nconst debug = require('debug')('expo:metro:withMetroResolvers') as typeof console.log;\n\nexport type { CustomResolver as MetroResolver };\n\n/** Expo Metro Resolvers can return `null` to skip without throwing an error. Metro Resolvers will throw either a `FailedToResolveNameError` or `FailedToResolvePathError`. */\nexport type ExpoCustomMetroResolver = (\n  ...args: Parameters<CustomResolver>\n) => ReturnType<CustomResolver> | null;\n\n/** @returns `MetroResolver` utilizing the upstream `resolve` method. */\nexport function getDefaultMetroResolver(projectRoot: string): CustomResolver {\n  return (context: ResolutionContext, moduleName: string, platform: string | null) => {\n    return metroResolver(context, moduleName, platform);\n  };\n}\n\n/**\n * Extend the Metro config `resolver.resolveRequest` method with additional resolvers that can\n * exit early by returning a `Resolution` or skip to the next resolver by returning `null`.\n *\n * @param config Metro config.\n * @param resolvers custom MetroResolver to chain.\n * @returns a new `MetroConfig` with the `resolver.resolveRequest` method chained.\n */\nexport function withMetroResolvers(\n  config: MetroConfig,\n  inputResolvers: (ExpoCustomMetroResolver | undefined)[]\n): MetroConfig {\n  const resolvers = inputResolvers.filter((x) => x != null);\n  debug(\n    `Appending ${\n      resolvers.length\n    } custom resolvers to Metro config. (has custom resolver: ${!!config.resolver?.resolveRequest})`\n  );\n  // const hasUserDefinedResolver = !!config.resolver?.resolveRequest;\n  // const defaultResolveRequest = getDefaultMetroResolver(projectRoot);\n  const originalResolveRequest = config.resolver?.resolveRequest;\n\n  return {\n    ...config,\n    resolver: {\n      ...config.resolver,\n      resolveRequest(context, moduleName, platform) {\n        const upstreamResolveRequest = context.resolveRequest;\n\n        const universalContext = {\n          ...context,\n          resolveRequest(\n            ctx: CustomResolutionContext,\n            moduleName: string,\n            platform: string | null\n          ) {\n            for (const resolver of resolvers) {\n              try {\n                const res = resolver(ctx, moduleName, platform);\n                if (res) {\n                  return res;\n                }\n              } catch (error: any) {\n                // If the error is directly related to a resolver not being able to resolve a module, then\n                // we can ignore the error and try the next resolver. Otherwise, we should throw the error.\n                const isResolutionError =\n                  isFailedToResolveNameError(error) || isFailedToResolvePathError(error);\n                if (!isResolutionError) {\n                  throw error;\n                }\n                debug(\n                  `Custom resolver (${resolver.name || '<anonymous>'}) threw: ${error.constructor.name}. (module: ${moduleName}, platform: ${platform}, env: ${ctx.customResolverOptions?.environment}, origin: ${ctx.originModulePath})`\n                );\n              }\n            }\n            // If we haven't returned by now, use the original resolver or upstream resolver.\n            return upstreamResolveRequest(ctx, moduleName, platform);\n          },\n        };\n\n        // If the user defined a resolver, run it first and depend on the documented\n        // chaining logic: https://facebook.github.io/metro/docs/resolution/#resolution-algorithm\n        //\n        // config.resolver.resolveRequest = (context, moduleName, platform) => {\n        //\n        //  // Do work...\n        //\n        //  return context.resolveRequest(context, moduleName, platform);\n        // };\n        const firstResolver = originalResolveRequest ?? universalContext.resolveRequest;\n        return firstResolver(universalContext, moduleName, platform);\n      },\n    },\n  };\n}\n\n/**\n * Hook into the Metro resolver chain and mutate the context so users can resolve against our custom assumptions.\n * For example, this will set `preferNativePlatform` to false when bundling for web.\n * */\nexport function withMetroMutatedResolverContext(\n  config: MetroConfig,\n  getContext: (\n    ctx: CustomResolutionContext,\n    moduleName: string,\n    platform: string | null\n  ) => CustomResolutionContext\n): MetroConfig {\n  const defaultResolveRequest = getDefaultMetroResolver(config.projectRoot);\n  const originalResolveRequest = config.resolver?.resolveRequest;\n\n  return {\n    ...config,\n    resolver: {\n      ...config.resolver,\n      resolveRequest(context, moduleName, platform) {\n        const universalContext = getContext(context, moduleName, platform);\n        const firstResolver =\n          originalResolveRequest ?? universalContext.resolveRequest ?? defaultResolveRequest;\n        return firstResolver(universalContext, moduleName, platform);\n      },\n    },\n  };\n}\n"], "names": ["getDefaultMetroResolver", "withMetroMutatedResolverContext", "withMetroResolvers", "debug", "require", "projectRoot", "context", "moduleName", "platform", "metroResolver", "config", "inputResolvers", "resolvers", "filter", "x", "length", "resolver", "resolveRequest", "originalResolveRequest", "upstreamResolveRequest", "universalContext", "ctx", "res", "error", "isResolutionError", "isFailedToResolveNameError", "isFailedToResolvePathError", "name", "constructor", "customResolverOptions", "environment", "originModulePath", "firstResolver", "getContext", "defaultResolveRequest"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IAqBeA,uBAAuB;eAAvBA;;IAsFAC,+BAA+B;eAA/BA;;IAxEAC,kBAAkB;eAAlBA;;;;yBA5ByB;;;;;;6BAE8B;AAEvE,MAAMC,QAAQC,QAAQ,SAAS;AAUxB,SAASJ,wBAAwBK,WAAmB;IACzD,OAAO,CAACC,SAA4BC,YAAoBC;QACtD,OAAOC,IAAAA,wBAAa,EAACH,SAASC,YAAYC;IAC5C;AACF;AAUO,SAASN,mBACdQ,MAAmB,EACnBC,cAAuD;QAMSD,kBAIjCA;IAR/B,MAAME,YAAYD,eAAeE,MAAM,CAAC,CAACC,IAAMA,KAAK;IACpDX,MACE,CAAC,UAAU,EACTS,UAAUG,MAAM,CACjB,yDAAyD,EAAE,CAAC,GAACL,mBAAAA,OAAOM,QAAQ,qBAAfN,iBAAiBO,cAAc,EAAC,CAAC,CAAC;IAElG,oEAAoE;IACpE,sEAAsE;IACtE,MAAMC,0BAAyBR,oBAAAA,OAAOM,QAAQ,qBAAfN,kBAAiBO,cAAc;IAE9D,OAAO;QACL,GAAGP,MAAM;QACTM,UAAU;YACR,GAAGN,OAAOM,QAAQ;YAClBC,gBAAeX,OAAO,EAAEC,UAAU,EAAEC,QAAQ;gBAC1C,MAAMW,yBAAyBb,QAAQW,cAAc;gBAErD,MAAMG,mBAAmB;oBACvB,GAAGd,OAAO;oBACVW,gBACEI,GAA4B,EAC5Bd,UAAkB,EAClBC,QAAuB;wBAEvB,KAAK,MAAMQ,YAAYJ,UAAW;4BAChC,IAAI;gCACF,MAAMU,MAAMN,SAASK,KAAKd,YAAYC;gCACtC,IAAIc,KAAK;oCACP,OAAOA;gCACT;4BACF,EAAE,OAAOC,OAAY;oCAS4HF;gCAR/I,0FAA0F;gCAC1F,2FAA2F;gCAC3F,MAAMG,oBACJC,IAAAA,uCAA0B,EAACF,UAAUG,IAAAA,uCAA0B,EAACH;gCAClE,IAAI,CAACC,mBAAmB;oCACtB,MAAMD;gCACR;gCACApB,MACE,CAAC,iBAAiB,EAAEa,SAASW,IAAI,IAAI,cAAc,SAAS,EAAEJ,MAAMK,WAAW,CAACD,IAAI,CAAC,WAAW,EAAEpB,WAAW,YAAY,EAAEC,SAAS,OAAO,GAAEa,6BAAAA,IAAIQ,qBAAqB,qBAAzBR,2BAA2BS,WAAW,CAAC,UAAU,EAAET,IAAIU,gBAAgB,CAAC,CAAC,CAAC;4BAE3N;wBACF;wBACA,iFAAiF;wBACjF,OAAOZ,uBAAuBE,KAAKd,YAAYC;oBACjD;gBACF;gBAEA,4EAA4E;gBAC5E,yFAAyF;gBACzF,EAAE;gBACF,wEAAwE;gBACxE,EAAE;gBACF,iBAAiB;gBACjB,EAAE;gBACF,iEAAiE;gBACjE,KAAK;gBACL,MAAMwB,gBAAgBd,0BAA0BE,iBAAiBH,cAAc;gBAC/E,OAAOe,cAAcZ,kBAAkBb,YAAYC;YACrD;QACF;IACF;AACF;AAMO,SAASP,gCACdS,MAAmB,EACnBuB,UAI4B;QAGGvB;IAD/B,MAAMwB,wBAAwBlC,wBAAwBU,OAAOL,WAAW;IACxE,MAAMa,0BAAyBR,mBAAAA,OAAOM,QAAQ,qBAAfN,iBAAiBO,cAAc;IAE9D,OAAO;QACL,GAAGP,MAAM;QACTM,UAAU;YACR,GAAGN,OAAOM,QAAQ;YAClBC,gBAAeX,OAAO,EAAEC,UAAU,EAAEC,QAAQ;gBAC1C,MAAMY,mBAAmBa,WAAW3B,SAASC,YAAYC;gBACzD,MAAMwB,gBACJd,0BAA0BE,iBAAiBH,cAAc,IAAIiB;gBAC/D,OAAOF,cAAcZ,kBAAkBb,YAAYC;YACrD;QACF;IACF;AACF"}