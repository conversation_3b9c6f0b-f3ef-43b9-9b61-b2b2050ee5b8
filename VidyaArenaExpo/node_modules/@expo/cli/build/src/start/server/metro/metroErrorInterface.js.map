{"version": 3, "sources": ["../../../../../src/start/server/metro/metroErrorInterface.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { getMetroServerRoot } from '@expo/config/paths';\nimport chalk from 'chalk';\nimport { stripVTControlCharacters } from 'node:util';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport { parse, StackFrame } from 'stacktrace-parser';\nimport terminalLink from 'terminal-link';\n\nimport { LogBoxLog } from './log-box/LogBoxLog';\nimport type { CodeFrame, StackFrame as MetroStackFrame } from './log-box/LogBoxSymbolication';\nimport { getStackFormattedLocation } from './log-box/formatProjectFilePath';\nimport { Log } from '../../../log';\nimport { stripAnsi } from '../../../utils/ansi';\nimport { env } from '../../../utils/env';\nimport { CommandError, SilentError } from '../../../utils/errors';\nimport { createMetroEndpointAsync } from '../getStaticRenderFunctions';\n\nconst isDebug = require('debug').enabled('expo:start:server:metro');\n\nfunction fill(width: number): string {\n  return Array(width).join(' ');\n}\n\nfunction formatPaths(config: { filePath: string | null; line?: number; col?: number }) {\n  const filePath = chalk.reset(config.filePath);\n  return (\n    chalk.dim('(') +\n    filePath +\n    chalk.dim(`:${[config.line, config.col].filter(Boolean).join(':')})`)\n  );\n}\n\nexport async function logMetroErrorWithStack(\n  projectRoot: string,\n  {\n    stack,\n    codeFrame,\n    error,\n  }: {\n    stack: MetroStackFrame[];\n    codeFrame?: CodeFrame;\n    error: Error;\n  }\n) {\n  if (error instanceof SilentError) {\n    return;\n  }\n\n  // process.stdout.write('\\u001b[0m'); // Reset attributes\n  // process.stdout.write('\\u001bc'); // Reset the terminal\n\n  Log.log();\n  Log.log(chalk.red('Metro error: ') + error.message);\n  Log.log();\n\n  if (error instanceof CommandError) {\n    return;\n  }\n\n  Log.log(\n    getStackAsFormattedLog(projectRoot, { stack, codeFrame, error, showCollapsedFrames: true })\n      .stack\n  );\n}\n\nexport function getStackAsFormattedLog(\n  projectRoot: string,\n  {\n    stack,\n    codeFrame,\n    error,\n    showCollapsedFrames = env.EXPO_DEBUG,\n  }: {\n    stack: MetroStackFrame[];\n    codeFrame?: CodeFrame;\n    error?: Error;\n    showCollapsedFrames?: boolean;\n  }\n): {\n  isFallback: boolean;\n  stack: string;\n} {\n  const logs: string[] = [];\n  const containsCodeFrame = likelyContainsCodeFrame(error?.message);\n\n  if (containsCodeFrame) {\n    // Some transformation errors will have a code frame embedded in the error message\n    // from Babel and we should not duplicate it as message is already printed before this call.\n  } else if (codeFrame) {\n    const maxWarningLineLength = Math.max(800, process.stdout.columns);\n\n    const lineText = codeFrame.content;\n    const lines = codeFrame.content.split('\\n');\n\n    // ---- index.tsx ------------------------------------------------------\n    //  32 |         This is example code which will be under the title.\n    const title = path.basename(codeFrame.fileName);\n    logs.push(chalk.bold`Code: ${title}`);\n\n    const isPreviewTooLong = lines.some((line) => line.length > maxWarningLineLength);\n    const column = codeFrame.location?.column;\n    // When the preview is too long, we skip reading the file and attempting to apply\n    // code coloring, this is because it can get very slow.\n    if (isPreviewTooLong) {\n      let previewLine = '';\n      let cursorLine = '';\n\n      const formattedPath = formatPaths({\n        filePath: codeFrame.fileName,\n        line: codeFrame.location?.row,\n        col: codeFrame.location?.column,\n      });\n      // Create a curtailed preview line like:\n      // `...transition:'fade'},k._updatePropsStack=function(){clearImmediate(k._updateImmediate),k._updateImmediate...`\n      // If there is no text preview or column number, we can't do anything.\n      if (lineText && column != null) {\n        const rangeWindow = Math.round(\n          Math.max(codeFrame.fileName?.length ?? 0, Math.max(80, process.stdout.columns)) / 2\n        );\n        let minBounds = Math.max(0, column - rangeWindow);\n        const maxBounds = Math.min(minBounds + rangeWindow * 2, lineText.length);\n        previewLine = lineText.slice(minBounds, maxBounds);\n\n        // If we splice content off the start, then we should append `...`.\n        // This is unlikely to happen since we limit the activation size.\n        if (minBounds > 0) {\n          // Adjust the min bounds so the cursor is aligned after we add the \"...\"\n          minBounds -= 3;\n          previewLine = chalk.dim('...') + previewLine;\n        }\n        if (maxBounds < lineText.length) {\n          previewLine += chalk.dim('...');\n        }\n\n        // If the column property could be found, then use that to fix the cursor location which is often broken in regex.\n        cursorLine = (column == null ? '' : fill(column) + chalk.reset('^')).slice(minBounds);\n\n        logs.push(formattedPath, '', previewLine, cursorLine, chalk.dim('(error truncated)'));\n      }\n    } else {\n      logs.push(codeFrame.content);\n    }\n  }\n\n  let isFallback = false;\n  if (stack?.length) {\n    const stackProps = stack.map((frame) => {\n      return {\n        title: frame.methodName,\n        subtitle: getStackFormattedLocation(projectRoot, frame),\n        collapse: frame.collapse || isInternalBytecode(frame),\n      };\n    });\n\n    const stackLines: string[] = [];\n    const backupStackLines: string[] = [];\n\n    stackProps.forEach((frame) => {\n      const shouldShow = !frame.collapse || showCollapsedFrames;\n\n      const position = terminalLink.isSupported\n        ? terminalLink(frame.subtitle, frame.subtitle)\n        : frame.subtitle;\n      let lineItem = chalk.gray(`  ${frame.title} (${position})`);\n\n      if (frame.collapse) {\n        lineItem = chalk.dim(lineItem);\n      }\n      // Never show the internal module system.\n      const isMetroRuntime =\n        /\\/metro-runtime\\/src\\/polyfills\\/require\\.js/.test(frame.subtitle) ||\n        /\\/metro-require\\/require\\.js/.test(frame.subtitle);\n      if (!isMetroRuntime) {\n        if (shouldShow) {\n          stackLines.push(lineItem);\n        }\n        backupStackLines.push(lineItem);\n      }\n    });\n\n    logs.push(chalk.bold`Call Stack`);\n\n    if (!backupStackLines.length) {\n      logs.push(chalk.gray('  No stack trace available.'));\n    } else {\n      isFallback = stackLines.length === 0;\n      // If there are not stack lines then it means the error likely happened in the node modules, in this case we should fallback to showing all the\n      // the stacks to give the user whatever help we can.\n      const displayStack = stackLines.length ? stackLines : backupStackLines;\n      logs.push(displayStack.join('\\n'));\n    }\n  } else if (error && error.stack) {\n    logs.push(chalk.gray(`  ${error.stack}`));\n  }\n\n  return {\n    isFallback,\n    stack: logs.join('\\n'),\n  };\n}\n\nexport const IS_METRO_BUNDLE_ERROR_SYMBOL = Symbol('_isMetroBundleError');\nconst HAS_LOGGED_SYMBOL = Symbol('_hasLoggedInCLI');\n\nexport async function logMetroError(\n  projectRoot: string,\n  {\n    error,\n  }: {\n    error: Error & {\n      [HAS_LOGGED_SYMBOL]?: boolean;\n    };\n  }\n) {\n  if (error instanceof SilentError || error[HAS_LOGGED_SYMBOL]) {\n    return;\n  }\n  error[HAS_LOGGED_SYMBOL] = true;\n\n  const stack = parseErrorStack(projectRoot, error.stack);\n\n  const log = new LogBoxLog({\n    level: 'static',\n    message: {\n      content: error.message,\n      substitutions: [],\n    },\n    isComponentError: false,\n    stack,\n    category: 'static',\n    componentStack: [],\n  });\n\n  await new Promise((res) => log.symbolicate('stack', res));\n\n  logMetroErrorWithStack(projectRoot, {\n    stack: log.symbolicated?.stack?.stack ?? [],\n    codeFrame: log.codeFrame,\n    error,\n  });\n}\n\nfunction isTransformError(\n  error: any\n): error is { type: 'TransformError'; filename: string; lineNumber: number; column: number } {\n  return error.type === 'TransformError';\n}\n\n/** @returns the html required to render the static metro error as an SPA. */\nfunction logFromError({ error, projectRoot }: { error: Error; projectRoot: string }) {\n  // Remap direct Metro Node.js errors to a format that will appear more client-friendly in the logbox UI.\n  let stack: MetroStackFrame[] | undefined;\n  if (isTransformError(error) && error.filename) {\n    // Syntax errors in static rendering.\n    stack = [\n      {\n        file: path.join(projectRoot, error.filename),\n        methodName: '<unknown>',\n        arguments: [],\n        // TODO: Import stack\n        lineNumber: error.lineNumber,\n        column: error.column,\n      },\n    ];\n  } else if ('originModulePath' in error && typeof error.originModulePath === 'string') {\n    // TODO: Use import stack here when the error is resolution based.\n    stack = [\n      {\n        file: error.originModulePath,\n        methodName: '<unknown>',\n        arguments: [],\n        // TODO: Import stack\n        lineNumber: 0,\n        column: 0,\n      },\n    ];\n  } else {\n    stack = parseErrorStack(projectRoot, error.stack);\n  }\n\n  return new LogBoxLog({\n    level: 'static',\n    message: {\n      content: error.message,\n      substitutions: [],\n    },\n    isComponentError: false,\n    stack,\n    category: 'static',\n    componentStack: [],\n  });\n}\n\n/** @returns the html required to render the static metro error as an SPA. */\nexport async function logMetroErrorAsync({\n  error,\n  projectRoot,\n}: {\n  error: Error;\n  projectRoot: string;\n}) {\n  const log = logFromError({ projectRoot, error });\n\n  await new Promise<void>((res) => log.symbolicate('stack', () => res()));\n\n  logMetroErrorWithStack(projectRoot, {\n    stack: log.symbolicated?.stack?.stack ?? [],\n    codeFrame: log.codeFrame,\n    error,\n  });\n}\n\n/** @returns the html required to render the static metro error as an SPA. */\nexport async function getErrorOverlayHtmlAsync({\n  error,\n  projectRoot,\n  routerRoot,\n}: {\n  error: Error;\n  projectRoot: string;\n  routerRoot: string;\n}) {\n  const log = logFromError({ projectRoot, error });\n\n  await new Promise<void>((res) => log.symbolicate('stack', () => res()));\n\n  logMetroErrorWithStack(projectRoot, {\n    stack: log.symbolicated?.stack?.stack ?? [],\n    codeFrame: log.codeFrame,\n    error,\n  });\n\n  if ('message' in log && 'content' in log.message && typeof log.message.content === 'string') {\n    log.message.content = stripAnsi(log.message.content)!;\n  }\n\n  const logBoxContext = {\n    selectedLogIndex: 0,\n    isDisabled: false,\n    logs: [log],\n  };\n  const html = `<html><head><style>#root,body,html{height:100%}body{overflow:hidden}#root{display:flex}</style></head><body><div id=\"root\"></div><script id=\"_expo-static-error\" type=\"application/json\">${JSON.stringify(\n    logBoxContext\n  )}</script></body></html>`;\n\n  const errorOverlayEntry = await createMetroEndpointAsync(\n    projectRoot,\n    // Keep the URL relative\n    '',\n    resolveFrom(projectRoot, 'expo-router/_error'),\n    {\n      mode: 'development',\n      platform: 'web',\n      minify: false,\n      optimize: false,\n      usedExports: false,\n      baseUrl: '',\n      routerRoot,\n      isExporting: false,\n      reactCompiler: false,\n    }\n  );\n\n  const htmlWithJs = html.replace('</body>', `<script src=${errorOverlayEntry}></script></body>`);\n  return htmlWithJs;\n}\n\nfunction parseErrorStack(\n  projectRoot: string,\n  stack?: string\n): (StackFrame & { collapse?: boolean })[] {\n  if (stack == null) {\n    return [];\n  }\n  if (Array.isArray(stack)) {\n    return stack;\n  }\n\n  const serverRoot = getMetroServerRoot(projectRoot);\n\n  return parse(stack)\n    .map((frame) => {\n      // frame.file will mostly look like `http://localhost:8081/index.bundle?platform=web&dev=true&hot=false`\n\n      if (frame.file) {\n        // SSR will sometimes have absolute paths followed by `.bundle?...`, we need to try and make them relative paths and append a dev server URL.\n        if (frame.file.startsWith('/') && frame.file.includes('bundle?') && !canParse(frame.file)) {\n          // Malformed stack file from SSR. Attempt to repair.\n          frame.file = 'https://localhost:8081/' + path.relative(serverRoot, frame.file);\n        }\n      }\n\n      return {\n        ...frame,\n        column: frame.column != null ? frame.column - 1 : null,\n      };\n    })\n    .filter((frame) => frame.file && !frame.file.includes('node_modules'));\n}\n\nfunction canParse(url: string): boolean {\n  try {\n    // eslint-disable-next-line no-new\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nexport function dropStackIfContainsCodeFrame(err: unknown) {\n  if (!(err instanceof Error)) return;\n\n  if (likelyContainsCodeFrame(err.message)) {\n    // If the error message contains a code frame, we should drop the stack to avoid cluttering the output.\n    delete err.stack;\n  }\n}\n\n/**\n * Tests given string on presence of ` [num] |` at the start of any line.\n * Returns `false` for undefined or empty strings.\n */\nexport function likelyContainsCodeFrame(message: string | undefined): boolean {\n  if (!message) return false;\n\n  const clean = stripVTControlCharacters(message);\n  if (!clean) return false;\n\n  return /^\\s*\\d+\\s+\\|/m.test(clean);\n}\n\n/**\n * Walks thru the error cause chain and attaches the import stack to the root error message.\n * Removes the error stack for import and syntax errors.\n */\nexport const attachImportStackToRootMessage = (err: unknown) => {\n  if (!(err instanceof Error)) return;\n\n  // Space out build failures.\n  const nearestImportStackValue = nearestImportStack(err);\n  if (nearestImportStackValue) {\n    err.message += '\\n\\n' + nearestImportStackValue;\n\n    if (!isDebug) {\n      // When not debugging remove the stack to avoid cluttering the output and confusing users,\n      // the import stack is the guide to fixing the error.\n      delete err.stack;\n    }\n  }\n};\n\n/**\n * Walks thru the error cause chain and returns the nearest import stack.\n * If the import stack is not found, it returns `undefined`.\n */\nexport const nearestImportStack = (err: unknown, root: unknown = err): string | undefined => {\n  if (!(err instanceof Error) || !(root instanceof Error)) return undefined;\n\n  if ('_expoImportStack' in err && typeof err._expoImportStack === 'string') {\n    // Space out build failures.\n    return err._expoImportStack;\n  } else {\n    return nearestImportStack(err.cause, root);\n  }\n};\n\nfunction isInternalBytecode(frame: StackFrame): boolean {\n  return frame.file?.includes('InternalBytecode.js') ?? false;\n}\n"], "names": ["IS_METRO_BUNDLE_ERROR_SYMBOL", "attachImportStackToRootMessage", "dropStackIfContainsCodeFrame", "getErrorOverlayHtmlAsync", "getStackAsFormattedLog", "likelyContainsCodeFrame", "logMetroError", "logMetroErrorAsync", "logMetroErrorWithStack", "nearestImportStack", "isDebug", "require", "enabled", "fill", "width", "Array", "join", "formatPaths", "config", "filePath", "chalk", "reset", "dim", "line", "col", "filter", "Boolean", "projectRoot", "stack", "codeFrame", "error", "SilentError", "Log", "log", "red", "message", "CommandError", "showCollapsedFrames", "env", "EXPO_DEBUG", "logs", "containsCodeFrame", "maxWarning<PERSON>ine<PERSON><PERSON><PERSON>", "Math", "max", "process", "stdout", "columns", "lineText", "content", "lines", "split", "title", "path", "basename", "fileName", "push", "bold", "isPreviewTooLong", "some", "length", "column", "location", "previewLine", "cursorLine", "formattedPath", "row", "rangeWindow", "round", "minBounds", "maxBounds", "min", "slice", "<PERSON><PERSON><PERSON><PERSON>", "stackProps", "map", "frame", "methodName", "subtitle", "getStackFormattedLocation", "collapse", "isInternalBytecode", "stackLines", "backupStackLines", "for<PERSON>ach", "shouldShow", "position", "terminalLink", "isSupported", "lineItem", "gray", "isMetroRuntime", "test", "displayStack", "Symbol", "HAS_LOGGED_SYMBOL", "parseError<PERSON>tack", "LogBoxLog", "level", "substitutions", "isComponentError", "category", "componentStack", "Promise", "res", "symbolicate", "symbolicated", "isTransformError", "type", "logFromError", "filename", "file", "arguments", "lineNumber", "originModulePath", "routerRoot", "stripAnsi", "logBoxContext", "selectedLogIndex", "isDisabled", "html", "JSON", "stringify", "errorOverlayEntry", "createMetroEndpointAsync", "resolveFrom", "mode", "platform", "minify", "optimize", "usedExports", "baseUrl", "isExporting", "reactCompiler", "htmlWithJs", "replace", "isArray", "serverRoot", "getMetroServerRoot", "parse", "startsWith", "includes", "canParse", "relative", "url", "URL", "err", "Error", "clean", "stripVTControlCharacters", "nearestImportStackValue", "root", "undefined", "_expoImportStack", "cause"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IA0MYA,4BAA4B;eAA5BA;;IA2OAC,8BAA8B;eAA9BA;;IA1BGC,4BAA4B;eAA5BA;;IAjGMC,wBAAwB;eAAxBA;;IAxPNC,sBAAsB;eAAtBA;;IAsWAC,uBAAuB;eAAvBA;;IA3NMC,aAAa;eAAbA;;IA0FAC,kBAAkB;eAAlBA;;IAtQAC,sBAAsB;eAAtBA;;IAwaTC,kBAAkB;eAAlBA;;;;yBAxcsB;;;;;;;gEACjB;;;;;;;yBACuB;;;;;;;gEACxB;;;;;;;gEACO;;;;;;;yBACU;;;;;;;gEACT;;;;;;2BAEC;uCAEgB;qBACtB;sBACM;qBACN;wBACsB;0CACD;;;;;;AAEzC,MAAMC,UAAUC,QAAQ,SAASC,OAAO,CAAC;AAEzC,SAASC,KAAKC,KAAa;IACzB,OAAOC,MAAMD,OAAOE,IAAI,CAAC;AAC3B;AAEA,SAASC,YAAYC,MAAgE;IACnF,MAAMC,WAAWC,gBAAK,CAACC,KAAK,CAACH,OAAOC,QAAQ;IAC5C,OACEC,gBAAK,CAACE,GAAG,CAAC,OACVH,WACAC,gBAAK,CAACE,GAAG,CAAC,CAAC,CAAC,EAAE;QAACJ,OAAOK,IAAI;QAAEL,OAAOM,GAAG;KAAC,CAACC,MAAM,CAACC,SAASV,IAAI,CAAC,KAAK,CAAC,CAAC;AAExE;AAEO,eAAeR,uBACpBmB,WAAmB,EACnB,EACEC,KAAK,EACLC,SAAS,EACTC,KAAK,EAKN;IAED,IAAIA,iBAAiBC,mBAAW,EAAE;QAChC;IACF;IAEA,yDAAyD;IACzD,yDAAyD;IAEzDC,QAAG,CAACC,GAAG;IACPD,QAAG,CAACC,GAAG,CAACb,gBAAK,CAACc,GAAG,CAAC,mBAAmBJ,MAAMK,OAAO;IAClDH,QAAG,CAACC,GAAG;IAEP,IAAIH,iBAAiBM,oBAAY,EAAE;QACjC;IACF;IAEAJ,QAAG,CAACC,GAAG,CACL7B,uBAAuBuB,aAAa;QAAEC;QAAOC;QAAWC;QAAOO,qBAAqB;IAAK,GACtFT,KAAK;AAEZ;AAEO,SAASxB,uBACduB,WAAmB,EACnB,EACEC,KAAK,EACLC,SAAS,EACTC,KAAK,EACLO,sBAAsBC,QAAG,CAACC,UAAU,EAMrC;IAKD,MAAMC,OAAiB,EAAE;IACzB,MAAMC,oBAAoBpC,wBAAwByB,yBAAAA,MAAOK,OAAO;IAEhE,IAAIM,mBAAmB;IACrB,kFAAkF;IAClF,4FAA4F;IAC9F,OAAO,IAAIZ,WAAW;YAYLA;QAXf,MAAMa,uBAAuBC,KAAKC,GAAG,CAAC,KAAKC,QAAQC,MAAM,CAACC,OAAO;QAEjE,MAAMC,WAAWnB,UAAUoB,OAAO;QAClC,MAAMC,QAAQrB,UAAUoB,OAAO,CAACE,KAAK,CAAC;QAEtC,wEAAwE;QACxE,oEAAoE;QACpE,MAAMC,QAAQC,eAAI,CAACC,QAAQ,CAACzB,UAAU0B,QAAQ;QAC9Cf,KAAKgB,IAAI,CAACpC,gBAAK,CAACqC,IAAI,CAAC,MAAM,EAAEL,MAAM,CAAC;QAEpC,MAAMM,mBAAmBR,MAAMS,IAAI,CAAC,CAACpC,OAASA,KAAKqC,MAAM,GAAGlB;QAC5D,MAAMmB,UAAShC,sBAAAA,UAAUiC,QAAQ,qBAAlBjC,oBAAoBgC,MAAM;QACzC,iFAAiF;QACjF,uDAAuD;QACvD,IAAIH,kBAAkB;gBAMZ7B,sBACDA;YANP,IAAIkC,cAAc;YAClB,IAAIC,aAAa;YAEjB,MAAMC,gBAAgBhD,YAAY;gBAChCE,UAAUU,UAAU0B,QAAQ;gBAC5BhC,IAAI,GAAEM,uBAAAA,UAAUiC,QAAQ,qBAAlBjC,qBAAoBqC,GAAG;gBAC7B1C,GAAG,GAAEK,uBAAAA,UAAUiC,QAAQ,qBAAlBjC,qBAAoBgC,MAAM;YACjC;YACA,wCAAwC;YACxC,kHAAkH;YAClH,sEAAsE;YACtE,IAAIb,YAAYa,UAAU,MAAM;oBAEnBhC;gBADX,MAAMsC,cAAcxB,KAAKyB,KAAK,CAC5BzB,KAAKC,GAAG,CAACf,EAAAA,sBAAAA,UAAU0B,QAAQ,qBAAlB1B,oBAAoB+B,MAAM,KAAI,GAAGjB,KAAKC,GAAG,CAAC,IAAIC,QAAQC,MAAM,CAACC,OAAO,KAAK;gBAEpF,IAAIsB,YAAY1B,KAAKC,GAAG,CAAC,GAAGiB,SAASM;gBACrC,MAAMG,YAAY3B,KAAK4B,GAAG,CAACF,YAAYF,cAAc,GAAGnB,SAASY,MAAM;gBACvEG,cAAcf,SAASwB,KAAK,CAACH,WAAWC;gBAExC,mEAAmE;gBACnE,iEAAiE;gBACjE,IAAID,YAAY,GAAG;oBACjB,wEAAwE;oBACxEA,aAAa;oBACbN,cAAc3C,gBAAK,CAACE,GAAG,CAAC,SAASyC;gBACnC;gBACA,IAAIO,YAAYtB,SAASY,MAAM,EAAE;oBAC/BG,eAAe3C,gBAAK,CAACE,GAAG,CAAC;gBAC3B;gBAEA,kHAAkH;gBAClH0C,aAAa,AAACH,CAAAA,UAAU,OAAO,KAAKhD,KAAKgD,UAAUzC,gBAAK,CAACC,KAAK,CAAC,IAAG,EAAGmD,KAAK,CAACH;gBAE3E7B,KAAKgB,IAAI,CAACS,eAAe,IAAIF,aAAaC,YAAY5C,gBAAK,CAACE,GAAG,CAAC;YAClE;QACF,OAAO;YACLkB,KAAKgB,IAAI,CAAC3B,UAAUoB,OAAO;QAC7B;IACF;IAEA,IAAIwB,aAAa;IACjB,IAAI7C,yBAAAA,MAAOgC,MAAM,EAAE;QACjB,MAAMc,aAAa9C,MAAM+C,GAAG,CAAC,CAACC;YAC5B,OAAO;gBACLxB,OAAOwB,MAAMC,UAAU;gBACvBC,UAAUC,IAAAA,gDAAyB,EAACpD,aAAaiD;gBACjDI,UAAUJ,MAAMI,QAAQ,IAAIC,mBAAmBL;YACjD;QACF;QAEA,MAAMM,aAAuB,EAAE;QAC/B,MAAMC,mBAA6B,EAAE;QAErCT,WAAWU,OAAO,CAAC,CAACR;YAClB,MAAMS,aAAa,CAACT,MAAMI,QAAQ,IAAI3C;YAEtC,MAAMiD,WAAWC,uBAAY,CAACC,WAAW,GACrCD,IAAAA,uBAAY,EAACX,MAAME,QAAQ,EAAEF,MAAME,QAAQ,IAC3CF,MAAME,QAAQ;YAClB,IAAIW,WAAWrE,gBAAK,CAACsE,IAAI,CAAC,CAAC,EAAE,EAAEd,MAAMxB,KAAK,CAAC,EAAE,EAAEkC,SAAS,CAAC,CAAC;YAE1D,IAAIV,MAAMI,QAAQ,EAAE;gBAClBS,WAAWrE,gBAAK,CAACE,GAAG,CAACmE;YACvB;YACA,yCAAyC;YACzC,MAAME,iBACJ,+CAA+CC,IAAI,CAAChB,MAAME,QAAQ,KAClE,+BAA+Bc,IAAI,CAAChB,MAAME,QAAQ;YACpD,IAAI,CAACa,gBAAgB;gBACnB,IAAIN,YAAY;oBACdH,WAAW1B,IAAI,CAACiC;gBAClB;gBACAN,iBAAiB3B,IAAI,CAACiC;YACxB;QACF;QAEAjD,KAAKgB,IAAI,CAACpC,gBAAK,CAACqC,IAAI,CAAC,UAAU,CAAC;QAEhC,IAAI,CAAC0B,iBAAiBvB,MAAM,EAAE;YAC5BpB,KAAKgB,IAAI,CAACpC,gBAAK,CAACsE,IAAI,CAAC;QACvB,OAAO;YACLjB,aAAaS,WAAWtB,MAAM,KAAK;YACnC,+IAA+I;YAC/I,oDAAoD;YACpD,MAAMiC,eAAeX,WAAWtB,MAAM,GAAGsB,aAAaC;YACtD3C,KAAKgB,IAAI,CAACqC,aAAa7E,IAAI,CAAC;QAC9B;IACF,OAAO,IAAIc,SAASA,MAAMF,KAAK,EAAE;QAC/BY,KAAKgB,IAAI,CAACpC,gBAAK,CAACsE,IAAI,CAAC,CAAC,EAAE,EAAE5D,MAAMF,KAAK,EAAE;IACzC;IAEA,OAAO;QACL6C;QACA7C,OAAOY,KAAKxB,IAAI,CAAC;IACnB;AACF;AAEO,MAAMhB,+BAA+B8F,OAAO;AACnD,MAAMC,oBAAoBD,OAAO;AAE1B,eAAexF,cACpBqB,WAAmB,EACnB,EACEG,KAAK,EAKN;QAwBQG,yBAAAA;IAtBT,IAAIH,iBAAiBC,mBAAW,IAAID,KAAK,CAACiE,kBAAkB,EAAE;QAC5D;IACF;IACAjE,KAAK,CAACiE,kBAAkB,GAAG;IAE3B,MAAMnE,QAAQoE,gBAAgBrE,aAAaG,MAAMF,KAAK;IAEtD,MAAMK,MAAM,IAAIgE,oBAAS,CAAC;QACxBC,OAAO;QACP/D,SAAS;YACPc,SAASnB,MAAMK,OAAO;YACtBgE,eAAe,EAAE;QACnB;QACAC,kBAAkB;QAClBxE;QACAyE,UAAU;QACVC,gBAAgB,EAAE;IACpB;IAEA,MAAM,IAAIC,QAAQ,CAACC,MAAQvE,IAAIwE,WAAW,CAAC,SAASD;IAEpDhG,uBAAuBmB,aAAa;QAClCC,OAAOK,EAAAA,oBAAAA,IAAIyE,YAAY,sBAAhBzE,0BAAAA,kBAAkBL,KAAK,qBAAvBK,wBAAyBL,KAAK,KAAI,EAAE;QAC3CC,WAAWI,IAAIJ,SAAS;QACxBC;IACF;AACF;AAEA,SAAS6E,iBACP7E,KAAU;IAEV,OAAOA,MAAM8E,IAAI,KAAK;AACxB;AAEA,2EAA2E,GAC3E,SAASC,aAAa,EAAE/E,KAAK,EAAEH,WAAW,EAAyC;IACjF,wGAAwG;IACxG,IAAIC;IACJ,IAAI+E,iBAAiB7E,UAAUA,MAAMgF,QAAQ,EAAE;QAC7C,qCAAqC;QACrClF,QAAQ;YACN;gBACEmF,MAAM1D,eAAI,CAACrC,IAAI,CAACW,aAAaG,MAAMgF,QAAQ;gBAC3CjC,YAAY;gBACZmC,WAAW,EAAE;gBACb,qBAAqB;gBACrBC,YAAYnF,MAAMmF,UAAU;gBAC5BpD,QAAQ/B,MAAM+B,MAAM;YACtB;SACD;IACH,OAAO,IAAI,sBAAsB/B,SAAS,OAAOA,MAAMoF,gBAAgB,KAAK,UAAU;QACpF,kEAAkE;QAClEtF,QAAQ;YACN;gBACEmF,MAAMjF,MAAMoF,gBAAgB;gBAC5BrC,YAAY;gBACZmC,WAAW,EAAE;gBACb,qBAAqB;gBACrBC,YAAY;gBACZpD,QAAQ;YACV;SACD;IACH,OAAO;QACLjC,QAAQoE,gBAAgBrE,aAAaG,MAAMF,KAAK;IAClD;IAEA,OAAO,IAAIqE,oBAAS,CAAC;QACnBC,OAAO;QACP/D,SAAS;YACPc,SAASnB,MAAMK,OAAO;YACtBgE,eAAe,EAAE;QACnB;QACAC,kBAAkB;QAClBxE;QACAyE,UAAU;QACVC,gBAAgB,EAAE;IACpB;AACF;AAGO,eAAe/F,mBAAmB,EACvCuB,KAAK,EACLH,WAAW,EAIZ;QAMUM,yBAAAA;IALT,MAAMA,MAAM4E,aAAa;QAAElF;QAAaG;IAAM;IAE9C,MAAM,IAAIyE,QAAc,CAACC,MAAQvE,IAAIwE,WAAW,CAAC,SAAS,IAAMD;IAEhEhG,uBAAuBmB,aAAa;QAClCC,OAAOK,EAAAA,oBAAAA,IAAIyE,YAAY,sBAAhBzE,0BAAAA,kBAAkBL,KAAK,qBAAvBK,wBAAyBL,KAAK,KAAI,EAAE;QAC3CC,WAAWI,IAAIJ,SAAS;QACxBC;IACF;AACF;AAGO,eAAe3B,yBAAyB,EAC7C2B,KAAK,EACLH,WAAW,EACXwF,UAAU,EAKX;QAMUlF,yBAAAA;IALT,MAAMA,MAAM4E,aAAa;QAAElF;QAAaG;IAAM;IAE9C,MAAM,IAAIyE,QAAc,CAACC,MAAQvE,IAAIwE,WAAW,CAAC,SAAS,IAAMD;IAEhEhG,uBAAuBmB,aAAa;QAClCC,OAAOK,EAAAA,oBAAAA,IAAIyE,YAAY,sBAAhBzE,0BAAAA,kBAAkBL,KAAK,qBAAvBK,wBAAyBL,KAAK,KAAI,EAAE;QAC3CC,WAAWI,IAAIJ,SAAS;QACxBC;IACF;IAEA,IAAI,aAAaG,OAAO,aAAaA,IAAIE,OAAO,IAAI,OAAOF,IAAIE,OAAO,CAACc,OAAO,KAAK,UAAU;QAC3FhB,IAAIE,OAAO,CAACc,OAAO,GAAGmE,IAAAA,eAAS,EAACnF,IAAIE,OAAO,CAACc,OAAO;IACrD;IAEA,MAAMoE,gBAAgB;QACpBC,kBAAkB;QAClBC,YAAY;QACZ/E,MAAM;YAACP;SAAI;IACb;IACA,MAAMuF,OAAO,CAAC,yLAAyL,EAAEC,KAAKC,SAAS,CACrNL,eACA,uBAAuB,CAAC;IAE1B,MAAMM,oBAAoB,MAAMC,IAAAA,kDAAwB,EACtDjG,aACA,wBAAwB;IACxB,IACAkG,IAAAA,sBAAW,EAAClG,aAAa,uBACzB;QACEmG,MAAM;QACNC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,aAAa;QACbC,SAAS;QACThB;QACAiB,aAAa;QACbC,eAAe;IACjB;IAGF,MAAMC,aAAad,KAAKe,OAAO,CAAC,WAAW,CAAC,YAAY,EAAEZ,kBAAkB,iBAAiB,CAAC;IAC9F,OAAOW;AACT;AAEA,SAAStC,gBACPrE,WAAmB,EACnBC,KAAc;IAEd,IAAIA,SAAS,MAAM;QACjB,OAAO,EAAE;IACX;IACA,IAAIb,MAAMyH,OAAO,CAAC5G,QAAQ;QACxB,OAAOA;IACT;IAEA,MAAM6G,aAAaC,IAAAA,2BAAkB,EAAC/G;IAEtC,OAAOgH,IAAAA,yBAAK,EAAC/G,OACV+C,GAAG,CAAC,CAACC;QACJ,wGAAwG;QAExG,IAAIA,MAAMmC,IAAI,EAAE;YACd,6IAA6I;YAC7I,IAAInC,MAAMmC,IAAI,CAAC6B,UAAU,CAAC,QAAQhE,MAAMmC,IAAI,CAAC8B,QAAQ,CAAC,cAAc,CAACC,SAASlE,MAAMmC,IAAI,GAAG;gBACzF,oDAAoD;gBACpDnC,MAAMmC,IAAI,GAAG,4BAA4B1D,eAAI,CAAC0F,QAAQ,CAACN,YAAY7D,MAAMmC,IAAI;YAC/E;QACF;QAEA,OAAO;YACL,GAAGnC,KAAK;YACRf,QAAQe,MAAMf,MAAM,IAAI,OAAOe,MAAMf,MAAM,GAAG,IAAI;QACpD;IACF,GACCpC,MAAM,CAAC,CAACmD,QAAUA,MAAMmC,IAAI,IAAI,CAACnC,MAAMmC,IAAI,CAAC8B,QAAQ,CAAC;AAC1D;AAEA,SAASC,SAASE,GAAW;IAC3B,IAAI;QACF,kCAAkC;QAClC,IAAIC,IAAID;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS9I,6BAA6BgJ,GAAY;IACvD,IAAI,CAAEA,CAAAA,eAAeC,KAAI,GAAI;IAE7B,IAAI9I,wBAAwB6I,IAAI/G,OAAO,GAAG;QACxC,uGAAuG;QACvG,OAAO+G,IAAItH,KAAK;IAClB;AACF;AAMO,SAASvB,wBAAwB8B,OAA2B;IACjE,IAAI,CAACA,SAAS,OAAO;IAErB,MAAMiH,QAAQC,IAAAA,oCAAwB,EAAClH;IACvC,IAAI,CAACiH,OAAO,OAAO;IAEnB,OAAO,gBAAgBxD,IAAI,CAACwD;AAC9B;AAMO,MAAMnJ,iCAAiC,CAACiJ;IAC7C,IAAI,CAAEA,CAAAA,eAAeC,KAAI,GAAI;IAE7B,4BAA4B;IAC5B,MAAMG,0BAA0B7I,mBAAmByI;IACnD,IAAII,yBAAyB;QAC3BJ,IAAI/G,OAAO,IAAI,SAASmH;QAExB,IAAI,CAAC5I,SAAS;YACZ,0FAA0F;YAC1F,qDAAqD;YACrD,OAAOwI,IAAItH,KAAK;QAClB;IACF;AACF;AAMO,MAAMnB,qBAAqB,CAACyI,KAAcK,OAAgBL,GAAG;IAClE,IAAI,CAAEA,CAAAA,eAAeC,KAAI,KAAM,CAAEI,CAAAA,gBAAgBJ,KAAI,GAAI,OAAOK;IAEhE,IAAI,sBAAsBN,OAAO,OAAOA,IAAIO,gBAAgB,KAAK,UAAU;QACzE,4BAA4B;QAC5B,OAAOP,IAAIO,gBAAgB;IAC7B,OAAO;QACL,OAAOhJ,mBAAmByI,IAAIQ,KAAK,EAAEH;IACvC;AACF;AAEA,SAAStE,mBAAmBL,KAAiB;QACpCA;IAAP,OAAOA,EAAAA,cAAAA,MAAMmC,IAAI,qBAAVnC,YAAYiE,QAAQ,CAAC,2BAA0B;AACxD"}