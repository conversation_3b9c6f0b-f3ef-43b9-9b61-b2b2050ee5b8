{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/createDebugMiddleware.ts"], "sourcesContent": ["import { WebSocketServer } from 'ws';\n\nimport { createHandlersFactory } from './createHandlersFactory';\nimport { env } from '../../../../utils/env';\nimport { type MetroBundlerDevServer } from '../MetroBundlerDevServer';\nimport { TerminalReporter } from '../TerminalReporter';\nimport { NETWORK_RESPONSE_STORAGE } from './messageHandlers/NetworkResponse';\n\nconst debug = require('debug')('expo:metro:debugging:middleware') as typeof console.log;\n\nexport function createDebugMiddleware(\n  metroBundler: MetroBundlerDevServer,\n  reporter: TerminalReporter\n) {\n  // Load the React Native debugging tools from project\n  // TODO: check if this works with isolated modules\n  const { createDevMiddleware } =\n    require('@react-native/dev-middleware') as typeof import('@react-native/dev-middleware');\n\n  const { middleware, websocketEndpoints } = createDevMiddleware({\n    projectRoot: metroBundler.projectRoot,\n    serverBaseUrl: metroBundler\n      .getUrlCreator()\n      .constructUrl({ scheme: 'http', hostType: 'localhost' }),\n    logger: create<PERSON>ogger(reporter),\n    unstable_customInspectorMessageHandler: createHandlersFactory(),\n    // TODO: Forward all events to the shared Metro log reporter. Do this when we have opinions on how all logs should be presented.\n    // unstable_eventReporter: {\n    //   logEvent(event) {\n    //     reporter.update(event);\n    //   },\n    // },\n    unstable_experiments: {\n      // Enable the Network tab in React Native DevTools\n      enableNetworkInspector: true,\n      // Only enable opening the browser version of React Native DevTools when debugging.\n      // This is useful when debugging the React Native DevTools by going to `/open-debugger` in the browser.\n      enableOpenDebuggerRedirect: env.EXPO_DEBUG,\n    },\n  });\n\n  // NOTE(cedric): add a temporary websocket to handle Network-related CDP events\n  websocketEndpoints['/inspector/network'] = createNetworkWebsocket(\n    websocketEndpoints['/inspector/debug']\n  );\n\n  return {\n    debugMiddleware: middleware,\n    debugWebsocketEndpoints: websocketEndpoints,\n  };\n}\n\nfunction createLogger(\n  reporter: TerminalReporter\n): Parameters<typeof import('@react-native/dev-middleware').createDevMiddleware>[0]['logger'] {\n  return {\n    info: makeLogger(reporter, 'info'),\n    warn: makeLogger(reporter, 'warn'),\n    error: makeLogger(reporter, 'error'),\n  };\n}\n\nfunction makeLogger(reporter: TerminalReporter, level: 'info' | 'warn' | 'error') {\n  return (...data: any[]) =>\n    reporter.update({\n      type: 'unstable_server_log',\n      level,\n      data,\n    });\n}\n\n/**\n * This adds a dedicated websocket connection that handles Network-related CDP events.\n * It's a temporary solution until Fusebox either implements the Network CDP domain,\n * or allows external domain agents that can send messages over the CDP socket to the debugger.\n * The Network websocket rebroadcasts events on the debugger CDP connections.\n */\nfunction createNetworkWebsocket(debuggerWebsocket: WebSocketServer) {\n  const wss = new WebSocketServer({\n    noServer: true,\n    perMessageDeflate: true,\n    // Don't crash on exceptionally large messages - assume the device is\n    // well-behaved and the debugger is prepared to handle large messages.\n    maxPayload: 0,\n  });\n\n  wss.on('connection', (networkSocket) => {\n    networkSocket.on('message', (data) => {\n      try {\n        // Parse the network message, to determine how the message should be handled\n        const message = JSON.parse(data.toString());\n\n        if (message.method === 'Expo(Network.receivedResponseBody)' && message.params) {\n          // If its a response body, write it to the global storage\n          const { requestId, ...requestInfo } = message.params;\n          NETWORK_RESPONSE_STORAGE.set(requestId, requestInfo);\n        } else {\n          // Otherwise, directly re-broadcast the Network events to all connected debuggers\n          debuggerWebsocket.clients.forEach((debuggerSocket) => {\n            if (debuggerSocket.readyState === debuggerSocket.OPEN) {\n              debuggerSocket.send(data.toString());\n            }\n          });\n        }\n      } catch (error) {\n        debug('Failed to handle Network CDP event', error);\n      }\n    });\n  });\n\n  return wss;\n}\n"], "names": ["createDebugMiddleware", "debug", "require", "metroBundler", "reporter", "createDevMiddleware", "middleware", "websocketEndpoints", "projectRoot", "serverBaseUrl", "getUrlCreator", "constructUrl", "scheme", "hostType", "logger", "createLogger", "unstable_customInspectorMessageHandler", "createHandlersFactory", "unstable_experiments", "enableNetworkInspector", "enableOpenDebuggerRedirect", "env", "EXPO_DEBUG", "createNetworkWebsocket", "debugMiddleware", "debugWebsocketEndpoints", "info", "<PERSON><PERSON>ogger", "warn", "error", "level", "data", "update", "type", "debuggerWebsocket", "wss", "WebSocketServer", "noServer", "perMessageDeflate", "maxPayload", "on", "networkSocket", "message", "JSON", "parse", "toString", "method", "params", "requestId", "requestInfo", "NETWORK_RESPONSE_STORAGE", "set", "clients", "for<PERSON>ach", "debuggerSocket", "readyState", "OPEN", "send"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;;yBAVgB;;;;;;uCAEM;qBAClB;iCAGqB;AAEzC,MAAMC,QAAQC,QAAQ,SAAS;AAExB,SAASF,sBACdG,YAAmC,EACnCC,QAA0B;IAE1B,qDAAqD;IACrD,kDAAkD;IAClD,MAAM,EAAEC,mBAAmB,EAAE,GAC3BH,QAAQ;IAEV,MAAM,EAAEI,UAAU,EAAEC,kBAAkB,EAAE,GAAGF,oBAAoB;QAC7DG,aAAaL,aAAaK,WAAW;QACrCC,eAAeN,aACZO,aAAa,GACbC,YAAY,CAAC;YAAEC,QAAQ;YAAQC,UAAU;QAAY;QACxDC,QAAQC,aAAaX;QACrBY,wCAAwCC,IAAAA,4CAAqB;QAC7D,gIAAgI;QAChI,4BAA4B;QAC5B,sBAAsB;QACtB,8BAA8B;QAC9B,OAAO;QACP,KAAK;QACLC,sBAAsB;YACpB,kDAAkD;YAClDC,wBAAwB;YACxB,mFAAmF;YACnF,uGAAuG;YACvGC,4BAA4BC,QAAG,CAACC,UAAU;QAC5C;IACF;IAEA,+EAA+E;IAC/Ef,kBAAkB,CAAC,qBAAqB,GAAGgB,uBACzChB,kBAAkB,CAAC,mBAAmB;IAGxC,OAAO;QACLiB,iBAAiBlB;QACjBmB,yBAAyBlB;IAC3B;AACF;AAEA,SAASQ,aACPX,QAA0B;IAE1B,OAAO;QACLsB,MAAMC,WAAWvB,UAAU;QAC3BwB,MAAMD,WAAWvB,UAAU;QAC3ByB,OAAOF,WAAWvB,UAAU;IAC9B;AACF;AAEA,SAASuB,WAAWvB,QAA0B,EAAE0B,KAAgC;IAC9E,OAAO,CAAC,GAAGC,OACT3B,SAAS4B,MAAM,CAAC;YACdC,MAAM;YACNH;YACAC;QACF;AACJ;AAEA;;;;;CAKC,GACD,SAASR,uBAAuBW,iBAAkC;IAChE,MAAMC,MAAM,IAAIC,CAAAA,KAAc,iBAAC,CAAC;QAC9BC,UAAU;QACVC,mBAAmB;QACnB,qEAAqE;QACrE,sEAAsE;QACtEC,YAAY;IACd;IAEAJ,IAAIK,EAAE,CAAC,cAAc,CAACC;QACpBA,cAAcD,EAAE,CAAC,WAAW,CAACT;YAC3B,IAAI;gBACF,4EAA4E;gBAC5E,MAAMW,UAAUC,KAAKC,KAAK,CAACb,KAAKc,QAAQ;gBAExC,IAAIH,QAAQI,MAAM,KAAK,wCAAwCJ,QAAQK,MAAM,EAAE;oBAC7E,yDAAyD;oBACzD,MAAM,EAAEC,SAAS,EAAE,GAAGC,aAAa,GAAGP,QAAQK,MAAM;oBACpDG,yCAAwB,CAACC,GAAG,CAACH,WAAWC;gBAC1C,OAAO;oBACL,iFAAiF;oBACjFf,kBAAkBkB,OAAO,CAACC,OAAO,CAAC,CAACC;wBACjC,IAAIA,eAAeC,UAAU,KAAKD,eAAeE,IAAI,EAAE;4BACrDF,eAAeG,IAAI,CAAC1B,KAAKc,QAAQ;wBACnC;oBACF;gBACF;YACF,EAAE,OAAOhB,OAAO;gBACd5B,MAAM,sCAAsC4B;YAC9C;QACF;IACF;IAEA,OAAOM;AACT"}