{"version": 3, "sources": ["../../../../../../src/start/server/middleware/inspector/CdpClient.ts"], "sourcesContent": ["import { WebSocket } from 'ws';\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:inspector:CdpClient'\n) as typeof console.log;\n\nexport function evaluateJsFromCdpAsync(\n  webSocketDebuggerUrl: string,\n  source: string,\n  timeoutMs: number = 2000\n): Promise<string | undefined> {\n  const REQUEST_ID = 0;\n  let timeoutHandle: NodeJS.Timeout;\n\n  return new Promise((resolve, reject) => {\n    let settled = false;\n    const ws = new WebSocket(webSocketDebuggerUrl);\n\n    timeoutHandle = setTimeout(() => {\n      debug(`[evaluateJsFromCdpAsync] Request timeout from ${webSocketDebuggerUrl}`);\n      reject(new Error('Request timeout'));\n      settled = true;\n      ws.close();\n    }, timeoutMs);\n\n    ws.on('open', () => {\n      ws.send(\n        JSON.stringify({\n          id: REQUEST_ID,\n          method: 'Runtime.evaluate',\n          params: { expression: source },\n        })\n      );\n    });\n\n    ws.on('error', (e) => {\n      debug(`[evaluateJsFromCdpAsync] Failed to connect ${webSocketDebuggerUrl}`, e);\n      reject(e);\n      settled = true;\n      clearTimeout(timeoutHandle);\n      ws.close();\n    });\n\n    ws.on('close', () => {\n      if (!settled) {\n        reject(new Error('WebSocket closed before response was received.'));\n        clearTimeout(timeoutHandle);\n      }\n    });\n\n    ws.on('message', (data) => {\n      debug(\n        `[evaluateJsFromCdpAsync] message received from ${webSocketDebuggerUrl}: ${data.toString()}`\n      );\n      try {\n        const response = JSON.parse(data.toString());\n        if (response.id === REQUEST_ID) {\n          if (response.error) {\n            reject(new Error(response.error.message));\n          } else if (response.result.result.type === 'string') {\n            resolve(response.result.result.value);\n          } else {\n            resolve(undefined);\n          }\n          settled = true;\n          clearTimeout(timeoutHandle);\n          ws.close();\n        }\n      } catch (e) {\n        reject(e);\n        settled = true;\n        clearTimeout(timeoutHandle);\n        ws.close();\n      }\n    });\n  });\n}\n"], "names": ["evaluateJsFromCdpAsync", "debug", "require", "webSocketDebuggerUrl", "source", "timeoutMs", "REQUEST_ID", "timeoutH<PERSON>le", "Promise", "resolve", "reject", "settled", "ws", "WebSocket", "setTimeout", "Error", "close", "on", "send", "JSON", "stringify", "id", "method", "params", "expression", "e", "clearTimeout", "data", "toString", "response", "parse", "error", "message", "result", "type", "value", "undefined"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;;yBANU;;;;;;AAE1B,MAAMC,QAAQC,QAAQ,SACpB;AAGK,SAASF,uBACdG,oBAA4B,EAC5BC,MAAc,EACdC,YAAoB,IAAI;IAExB,MAAMC,aAAa;IACnB,IAAIC;IAEJ,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3B,IAAIC,UAAU;QACd,MAAMC,KAAK,IAAIC,CAAAA,KAAQ,WAAC,CAACV;QAEzBI,gBAAgBO,WAAW;YACzBb,MAAM,CAAC,8CAA8C,EAAEE,sBAAsB;YAC7EO,OAAO,IAAIK,MAAM;YACjBJ,UAAU;YACVC,GAAGI,KAAK;QACV,GAAGX;QAEHO,GAAGK,EAAE,CAAC,QAAQ;YACZL,GAAGM,IAAI,CACLC,KAAKC,SAAS,CAAC;gBACbC,IAAIf;gBACJgB,QAAQ;gBACRC,QAAQ;oBAAEC,YAAYpB;gBAAO;YAC/B;QAEJ;QAEAQ,GAAGK,EAAE,CAAC,SAAS,CAACQ;YACdxB,MAAM,CAAC,2CAA2C,EAAEE,sBAAsB,EAAEsB;YAC5Ef,OAAOe;YACPd,UAAU;YACVe,aAAanB;YACbK,GAAGI,KAAK;QACV;QAEAJ,GAAGK,EAAE,CAAC,SAAS;YACb,IAAI,CAACN,SAAS;gBACZD,OAAO,IAAIK,MAAM;gBACjBW,aAAanB;YACf;QACF;QAEAK,GAAGK,EAAE,CAAC,WAAW,CAACU;YAChB1B,MACE,CAAC,+CAA+C,EAAEE,qBAAqB,EAAE,EAAEwB,KAAKC,QAAQ,IAAI;YAE9F,IAAI;gBACF,MAAMC,WAAWV,KAAKW,KAAK,CAACH,KAAKC,QAAQ;gBACzC,IAAIC,SAASR,EAAE,KAAKf,YAAY;oBAC9B,IAAIuB,SAASE,KAAK,EAAE;wBAClBrB,OAAO,IAAIK,MAAMc,SAASE,KAAK,CAACC,OAAO;oBACzC,OAAO,IAAIH,SAASI,MAAM,CAACA,MAAM,CAACC,IAAI,KAAK,UAAU;wBACnDzB,QAAQoB,SAASI,MAAM,CAACA,MAAM,CAACE,KAAK;oBACtC,OAAO;wBACL1B,QAAQ2B;oBACV;oBACAzB,UAAU;oBACVe,aAAanB;oBACbK,GAAGI,KAAK;gBACV;YACF,EAAE,OAAOS,GAAG;gBACVf,OAAOe;gBACPd,UAAU;gBACVe,aAAanB;gBACbK,GAAGI,KAAK;YACV;QACF;IACF;AACF"}