{"version": 3, "sources": ["../../../../../src/start/server/metro/createExpoAutolinkingResolver.ts"], "sourcesContent": ["import type { ResolutionContext } from '@expo/metro/metro-resolver';\nimport type { ResolutionResult as AutolinkingResolutionResult } from 'expo-modules-autolinking/exports';\n\nimport type { StrictResolverFactory } from './withMetroMultiPlatform';\nimport type { ExpoCustomMetroResolver } from './withMetroResolvers';\n\nconst debug = require('debug')(\n  'expo:start:server:metro:autolinking-resolver'\n) as typeof console.log;\n\n// This is a list of known modules we want to always include in sticky resolution\n// Specifying these skips platform- and module-specific checks and always includes them in the output\nconst KNOWN_STICKY_DEPENDENCIES = [\n  // NOTE: react and react-dom aren't native modules, but must also be deduplicated in bundles\n  'react',\n  'react-dom',\n  // NOTE: react-native won't be in autolinking output, since it's special\n  // We include it here manually, since we know it should be an unduplicated direct dependency\n  'react-native',\n  // Peer dependencies from expo\n  'react-native-webview',\n  '@expo/dom-webview',\n  // Dependencies from expo\n  'expo-asset',\n  'expo-constants',\n  'expo-file-system',\n  'expo-font',\n  'expo-keep-awake',\n  'expo-modules-core',\n  // Peer dependencies from expo-router\n  'react-native-gesture-handler',\n  'react-native-reanimated',\n];\n\nconst AUTOLINKING_PLATFORMS = ['android', 'ios'] as const;\ntype AutolinkingPlatform = (typeof AUTOLINKING_PLATFORMS)[number];\n\nconst escapeDependencyName = (dependency: string) =>\n  dependency.replace(/[*.?()[\\]]/g, (x) => `\\\\${x}`);\n\n/** Converts a list of module names to a regex that may either match bare module names or sub-modules of modules */\nexport const _dependenciesToRegex = (dependencies: string[]) =>\n  new RegExp(`^(${dependencies.map(escapeDependencyName).join('|')})($|/.*)`);\n\nconst getAutolinkingExports = (): typeof import('expo/internal/unstable-autolinking-exports') =>\n  require('expo/internal/unstable-autolinking-exports');\n\ninterface PlatformModuleDescription {\n  platform: AutolinkingPlatform;\n  moduleTestRe: RegExp;\n  resolvedModulePaths: Record<string, string>;\n}\n\nconst toPlatformModuleDescription = (\n  dependencies: AutolinkingResolutionResult,\n  platform: AutolinkingPlatform\n): PlatformModuleDescription => {\n  const resolvedModulePaths: Record<string, string> = {};\n  const resolvedModuleNames: string[] = [];\n  for (const dependencyName in dependencies) {\n    const dependency = dependencies[dependencyName];\n    if (dependency) {\n      resolvedModuleNames.push(dependency.name);\n      resolvedModulePaths[dependency.name] = dependency.path;\n    }\n  }\n  debug(\n    `Sticky resolution for ${platform} registered ${resolvedModuleNames.length} resolutions:`,\n    resolvedModuleNames\n  );\n  return {\n    platform,\n    moduleTestRe: _dependenciesToRegex(resolvedModuleNames),\n    resolvedModulePaths,\n  };\n};\n\nexport type AutolinkingModuleResolverInput = {\n  [platform in AutolinkingPlatform]?: PlatformModuleDescription;\n};\n\nexport async function createAutolinkingModuleResolverInput({\n  platforms,\n  projectRoot,\n}: {\n  projectRoot: string;\n  platforms: string[];\n}): Promise<AutolinkingModuleResolverInput> {\n  const autolinking = getAutolinkingExports();\n  const linker = autolinking.makeCachedDependenciesLinker({ projectRoot });\n\n  return Object.fromEntries(\n    await Promise.all(\n      platforms\n        .filter((platform): platform is AutolinkingPlatform => {\n          return AUTOLINKING_PLATFORMS.includes(platform as any);\n        })\n        .map(async (platform) => {\n          const dependencies = await autolinking.scanDependencyResolutionsForPlatform(\n            linker,\n            platform,\n            KNOWN_STICKY_DEPENDENCIES\n          );\n          const moduleDescription = toPlatformModuleDescription(dependencies, platform);\n          return [platform, moduleDescription] satisfies [\n            AutolinkingPlatform,\n            PlatformModuleDescription,\n          ];\n        })\n    )\n  ) as AutolinkingModuleResolverInput;\n}\n\nexport function createAutolinkingModuleResolver(\n  input: AutolinkingModuleResolverInput | undefined,\n  { getStrictResolver }: { getStrictResolver: StrictResolverFactory }\n): ExpoCustomMetroResolver | undefined {\n  if (!input) {\n    return undefined;\n  }\n\n  const fileSpecifierRe = /^[\\\\/]|^\\.\\.?(?:$|[\\\\/])/i;\n  const isAutolinkingPlatform = (platform: string | null): platform is AutolinkingPlatform =>\n    !!platform && (input as any)[platform] != null;\n\n  return function requestStickyModule(immutableContext, moduleName, platform) {\n    // NOTE(@kitten): We currently don't include Web. The `expo-doctor` check already warns\n    // about duplicates, and we can try to add Web later on. We should expand expo-modules-autolinking\n    // properly to support web first however\n    if (!isAutolinkingPlatform(platform)) {\n      return null;\n    } else if (fileSpecifierRe.test(moduleName)) {\n      return null;\n    }\n\n    const moduleDescription = input[platform]!;\n    const moduleMatch = moduleDescription.moduleTestRe.exec(moduleName);\n    if (moduleMatch) {\n      const resolvedModulePath =\n        moduleDescription.resolvedModulePaths[moduleMatch[1]] || moduleName;\n      // We instead resolve as if it was a dependency from within the module (self-require/import)\n      const context: ResolutionContext = {\n        ...immutableContext,\n        nodeModulesPaths: [resolvedModulePath],\n        originModulePath: resolvedModulePath,\n      };\n      const res = getStrictResolver(context, platform)(moduleName);\n      debug(`Sticky resolution for ${platform}: ${moduleName} -> from: ${resolvedModulePath}`);\n      return res;\n    }\n\n    return null;\n  };\n}\n"], "names": ["_dependenciesToRegex", "createAutolinkingModuleResolver", "createAutolinkingModuleResolverInput", "debug", "require", "KNOWN_STICKY_DEPENDENCIES", "AUTOLINKING_PLATFORMS", "escapeDependencyName", "dependency", "replace", "x", "dependencies", "RegExp", "map", "join", "getAutolinkingExports", "toPlatformModuleDescription", "platform", "resolvedModulePaths", "resolvedModuleNames", "dependencyName", "push", "name", "path", "length", "moduleTestRe", "platforms", "projectRoot", "autolinking", "linker", "makeCachedDependenciesLinker", "Object", "fromEntries", "Promise", "all", "filter", "includes", "scanDependencyResolutionsForPlatform", "moduleDescription", "input", "getStrictResolver", "undefined", "fileSpecifierRe", "isAutolinkingPlatform", "requestStickyModule", "immutableContext", "moduleName", "test", "moduleMatch", "exec", "resolvedModulePath", "context", "nodeModulesPaths", "originModulePath", "res"], "mappings": ";;;;;;;;;;;IAyCaA,oBAAoB;eAApBA;;IAwEGC,+BAA+B;eAA/BA;;IAhCMC,oCAAoC;eAApCA;;;AA3EtB,MAAMC,QAAQC,QAAQ,SACpB;AAGF,iFAAiF;AACjF,qGAAqG;AACrG,MAAMC,4BAA4B;IAChC,4FAA4F;IAC5F;IACA;IACA,wEAAwE;IACxE,4FAA4F;IAC5F;IACA,8BAA8B;IAC9B;IACA;IACA,yBAAyB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA,qCAAqC;IACrC;IACA;CACD;AAED,MAAMC,wBAAwB;IAAC;IAAW;CAAM;AAGhD,MAAMC,uBAAuB,CAACC,aAC5BA,WAAWC,OAAO,CAAC,eAAe,CAACC,IAAM,CAAC,EAAE,EAAEA,GAAG;AAG5C,MAAMV,uBAAuB,CAACW,eACnC,IAAIC,OAAO,CAAC,EAAE,EAAED,aAAaE,GAAG,CAACN,sBAAsBO,IAAI,CAAC,KAAK,QAAQ,CAAC;AAE5E,MAAMC,wBAAwB,IAC5BX,QAAQ;AAQV,MAAMY,8BAA8B,CAClCL,cACAM;IAEA,MAAMC,sBAA8C,CAAC;IACrD,MAAMC,sBAAgC,EAAE;IACxC,IAAK,MAAMC,kBAAkBT,aAAc;QACzC,MAAMH,aAAaG,YAAY,CAACS,eAAe;QAC/C,IAAIZ,YAAY;YACdW,oBAAoBE,IAAI,CAACb,WAAWc,IAAI;YACxCJ,mBAAmB,CAACV,WAAWc,IAAI,CAAC,GAAGd,WAAWe,IAAI;QACxD;IACF;IACApB,MACE,CAAC,sBAAsB,EAAEc,SAAS,YAAY,EAAEE,oBAAoBK,MAAM,CAAC,aAAa,CAAC,EACzFL;IAEF,OAAO;QACLF;QACAQ,cAAczB,qBAAqBmB;QACnCD;IACF;AACF;AAMO,eAAehB,qCAAqC,EACzDwB,SAAS,EACTC,WAAW,EAIZ;IACC,MAAMC,cAAcb;IACpB,MAAMc,SAASD,YAAYE,4BAA4B,CAAC;QAAEH;IAAY;IAEtE,OAAOI,OAAOC,WAAW,CACvB,MAAMC,QAAQC,GAAG,CACfR,UACGS,MAAM,CAAC,CAAClB;QACP,OAAOX,sBAAsB8B,QAAQ,CAACnB;IACxC,GACCJ,GAAG,CAAC,OAAOI;QACV,MAAMN,eAAe,MAAMiB,YAAYS,oCAAoC,CACzER,QACAZ,UACAZ;QAEF,MAAMiC,oBAAoBtB,4BAA4BL,cAAcM;QACpE,OAAO;YAACA;YAAUqB;SAAkB;IAItC;AAGR;AAEO,SAASrC,gCACdsC,KAAiD,EACjD,EAAEC,iBAAiB,EAAgD;IAEnE,IAAI,CAACD,OAAO;QACV,OAAOE;IACT;IAEA,MAAMC,kBAAkB;IACxB,MAAMC,wBAAwB,CAAC1B,WAC7B,CAAC,CAACA,YAAY,AAACsB,KAAa,CAACtB,SAAS,IAAI;IAE5C,OAAO,SAAS2B,oBAAoBC,gBAAgB,EAAEC,UAAU,EAAE7B,QAAQ;QACxE,uFAAuF;QACvF,kGAAkG;QAClG,wCAAwC;QACxC,IAAI,CAAC0B,sBAAsB1B,WAAW;YACpC,OAAO;QACT,OAAO,IAAIyB,gBAAgBK,IAAI,CAACD,aAAa;YAC3C,OAAO;QACT;QAEA,MAAMR,oBAAoBC,KAAK,CAACtB,SAAS;QACzC,MAAM+B,cAAcV,kBAAkBb,YAAY,CAACwB,IAAI,CAACH;QACxD,IAAIE,aAAa;YACf,MAAME,qBACJZ,kBAAkBpB,mBAAmB,CAAC8B,WAAW,CAAC,EAAE,CAAC,IAAIF;YAC3D,4FAA4F;YAC5F,MAAMK,UAA6B;gBACjC,GAAGN,gBAAgB;gBACnBO,kBAAkB;oBAACF;iBAAmB;gBACtCG,kBAAkBH;YACpB;YACA,MAAMI,MAAMd,kBAAkBW,SAASlC,UAAU6B;YACjD3C,MAAM,CAAC,sBAAsB,EAAEc,SAAS,EAAE,EAAE6B,WAAW,UAAU,EAAEI,oBAAoB;YACvF,OAAOI;QACT;QAEA,OAAO;IACT;AACF"}