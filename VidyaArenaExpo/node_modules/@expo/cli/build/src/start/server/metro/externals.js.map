{"version": 3, "sources": ["../../../../../src/start/server/metro/externals.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport fs from 'fs';\nimport { builtinModules } from 'module';\nimport path from 'path';\n\n// A list of the Node.js standard library modules that are currently\n// available,\nexport const NODE_STDLIB_MODULES: string[] = [\n  // Add all nested imports...\n  'assert/strict',\n  'dns/promises',\n  'inspector/promises',\n  'fs/promises',\n  'stream/web',\n  'stream/promises',\n  'path/posix',\n  'path/win32',\n  'readline/promises',\n  'stream/consumers',\n  'timers/promises',\n  'util/types',\n\n  // Collect all builtin modules...\n  ...(\n    builtinModules ||\n    // @ts-expect-error\n    (process.binding ? Object.keys(process.binding('natives')) : []) ||\n    []\n  ).filter((x) => !/^(internal|v8|node-inspect)\\/|\\//.test(x) && !['sys'].includes(x)),\n].sort();\n\nconst shimsFolder = path.join(require.resolve('@expo/cli/package.json'), '../static/shims');\nconst canaryFolder = path.join(require.resolve('@expo/cli/package.json'), '../static/canary');\n\nexport function shouldCreateVirtualShim(normalName: string) {\n  const shimPath = path.join(shimsFolder, normalName);\n  if (fs.existsSync(shimPath)) {\n    return shimPath;\n  }\n  return null;\n}\nexport function shouldCreateVirtualCanary(normalName: string): string | null {\n  const canaryPath = path.join(canaryFolder, normalName);\n  if (fs.existsSync(canaryPath)) {\n    return canaryPath;\n  }\n  return null;\n}\n\nexport function isNodeExternal(moduleName: string): string | null {\n  const moduleId = moduleName.replace(/^node:/, '');\n  if (NODE_STDLIB_MODULES.includes(moduleId)) {\n    return moduleId;\n  }\n  return null;\n}\n"], "names": ["NODE_STDLIB_MODULES", "isNodeExternal", "shouldCreateVirtualCanary", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtinModules", "process", "binding", "Object", "keys", "filter", "x", "test", "includes", "sort", "shimsFolder", "path", "join", "require", "resolve", "canaryFolder", "normalName", "s<PERSON><PERSON><PERSON>", "fs", "existsSync", "canaryPath", "moduleName", "moduleId", "replace"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IAOYA,mBAAmB;eAAnBA;;IA0CGC,cAAc;eAAdA;;IARAC,yBAAyB;eAAzBA;;IAPAC,uBAAuB;eAAvBA;;;;gEAjCD;;;;;;;yBACgB;;;;;;;gEACd;;;;;;;;;;;AAIV,MAAMH,sBAAgC;IAC3C,4BAA4B;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,iCAAiC;OAC9B,AACDI,CAAAA,wBAAc,IACd,mBAAmB;IAClBC,CAAAA,QAAQC,OAAO,GAAGC,OAAOC,IAAI,CAACH,QAAQC,OAAO,CAAC,cAAc,EAAE,AAAD,KAC9D,EAAE,AAAD,EACDG,MAAM,CAAC,CAACC,IAAM,CAAC,mCAAmCC,IAAI,CAACD,MAAM,CAAC;YAAC;SAAM,CAACE,QAAQ,CAACF;CAClF,CAACG,IAAI;AAEN,MAAMC,cAAcC,eAAI,CAACC,IAAI,CAACC,QAAQC,OAAO,CAAC,2BAA2B;AACzE,MAAMC,eAAeJ,eAAI,CAACC,IAAI,CAACC,QAAQC,OAAO,CAAC,2BAA2B;AAEnE,SAASf,wBAAwBiB,UAAkB;IACxD,MAAMC,WAAWN,eAAI,CAACC,IAAI,CAACF,aAAaM;IACxC,IAAIE,aAAE,CAACC,UAAU,CAACF,WAAW;QAC3B,OAAOA;IACT;IACA,OAAO;AACT;AACO,SAASnB,0BAA0BkB,UAAkB;IAC1D,MAAMI,aAAaT,eAAI,CAACC,IAAI,CAACG,cAAcC;IAC3C,IAAIE,aAAE,CAACC,UAAU,CAACC,aAAa;QAC7B,OAAOA;IACT;IACA,OAAO;AACT;AAEO,SAASvB,eAAewB,UAAkB;IAC/C,MAAMC,WAAWD,WAAWE,OAAO,CAAC,UAAU;IAC9C,IAAI3B,oBAAoBY,QAAQ,CAACc,WAAW;QAC1C,OAAOA;IACT;IACA,OAAO;AACT"}