{"version": 3, "sources": ["../../../../../../src/start/server/metro/log-box/LogBoxLog.ts"], "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport * as LogBoxSymbolication from './LogBoxSymbolication';\nimport type { CodeFrame, StackFrame } from './LogBoxSymbolication';\n\ntype Category = string;\n\ntype Message = {\n  content: string;\n  substitutions: {\n    length: number;\n    offset: number;\n  }[];\n};\n\ntype ComponentStack = CodeFrame[];\n\ntype SymbolicationStatus = 'NONE' | 'PENDING' | 'COMPLETE' | 'FAILED';\n\nexport type LogLevel = 'warn' | 'error' | 'fatal' | 'syntax' | 'static';\n\nexport type LogBoxLogData = {\n  level: LogLevel;\n  type?: string;\n  message: Message;\n  stack: StackFrame[];\n  category: string;\n  componentStack: ComponentStack;\n  codeFrame?: CodeFrame;\n  isComponentError: boolean;\n};\n\nexport type StackType = 'stack' | 'component';\n\nfunction componentStackToStack(componentStack: ComponentStack): StackFrame[] {\n  return componentStack.map((stack) => ({\n    file: stack.fileName,\n    methodName: stack.content,\n    lineNumber: stack.location?.row ?? 0,\n    column: stack.location?.column ?? 0,\n    arguments: [],\n  }));\n}\n\ntype SymbolicationCallback = (status: SymbolicationStatus) => void;\n\ntype SymbolicationResult =\n  | { error: null; stack: null; status: 'NONE' }\n  | { error: null; stack: null; status: 'PENDING' }\n  | { error: null; stack: StackFrame[]; status: 'COMPLETE' }\n  | { error: Error; stack: null; status: 'FAILED' };\n\nexport class LogBoxLog {\n  message: Message;\n  type: string;\n  category: Category;\n  componentStack: ComponentStack;\n  stack: StackFrame[];\n  count: number;\n  level: LogLevel;\n  codeFrame?: CodeFrame;\n  isComponentError: boolean;\n  symbolicated: Record<StackType, SymbolicationResult> = {\n    stack: {\n      error: null,\n      stack: null,\n      status: 'NONE',\n    },\n    component: {\n      error: null,\n      stack: null,\n      status: 'NONE',\n    },\n  };\n\n  private callbacks: Map<StackType, Set<SymbolicationCallback>> = new Map();\n\n  constructor(\n    data: LogBoxLogData & {\n      symbolicated?: Record<StackType, SymbolicationResult>;\n    }\n  ) {\n    this.level = data.level;\n    this.type = data.type ?? 'error';\n    this.message = data.message;\n    this.stack = data.stack;\n    this.category = data.category;\n    this.componentStack = data.componentStack;\n    this.codeFrame = data.codeFrame;\n    this.isComponentError = data.isComponentError;\n    this.count = 1;\n    this.symbolicated = data.symbolicated ?? this.symbolicated;\n  }\n\n  incrementCount(): void {\n    this.count += 1;\n  }\n\n  getAvailableStack(type: StackType): StackFrame[] | null {\n    if (this.symbolicated[type].status === 'COMPLETE') {\n      return this.symbolicated[type].stack;\n    }\n    return this.getStack(type);\n  }\n\n  private flushCallbacks(type: StackType): void {\n    const callbacks = this.callbacks.get(type);\n    const status = this.symbolicated[type].status;\n    if (callbacks) {\n      for (const callback of callbacks) {\n        callback(status);\n      }\n      callbacks.clear();\n    }\n  }\n\n  private pushCallback(type: StackType, callback: SymbolicationCallback): void {\n    let callbacks = this.callbacks.get(type);\n    if (!callbacks) {\n      callbacks = new Set();\n      this.callbacks.set(type, callbacks);\n    }\n    callbacks.add(callback);\n  }\n\n  retrySymbolicate(type: StackType, callback?: (status: SymbolicationStatus) => void): void {\n    this._symbolicate(type, true, callback);\n  }\n\n  symbolicate(type: StackType, callback?: (status: SymbolicationStatus) => void): void {\n    this._symbolicate(type, false, callback);\n  }\n\n  private _symbolicate(\n    type: StackType,\n    retry: boolean,\n    callback?: (status: SymbolicationStatus) => void\n  ): void {\n    if (callback) {\n      this.pushCallback(type, callback);\n    }\n    const status = this.symbolicated[type].status;\n\n    if (status === 'COMPLETE') {\n      return this.flushCallbacks(type);\n    }\n\n    if (retry) {\n      LogBoxSymbolication.deleteStack(this.getStack(type));\n      this.handleSymbolicate(type);\n    } else {\n      if (status === 'NONE') {\n        this.handleSymbolicate(type);\n      }\n    }\n  }\n\n  private componentStackCache: StackFrame[] | null = null;\n\n  private getStack(type: StackType): StackFrame[] {\n    if (type === 'component') {\n      if (this.componentStackCache == null) {\n        this.componentStackCache = componentStackToStack(this.componentStack);\n      }\n      return this.componentStackCache;\n    }\n    return this.stack;\n  }\n\n  private handleSymbolicate(type: StackType): void {\n    if (type === 'component' && !this.componentStack?.length) {\n      return;\n    }\n\n    if (this.symbolicated[type].status !== 'PENDING') {\n      this.updateStatus(type, null, null, null);\n      LogBoxSymbolication.symbolicate(this.getStack(type)).then(\n        (data) => {\n          this.updateStatus(type, null, data?.stack, data?.codeFrame);\n        },\n        (error) => {\n          this.updateStatus(type, error, null, null);\n        }\n      );\n    }\n  }\n\n  private updateStatus(\n    type: StackType,\n    error?: Error | null,\n    stack?: StackFrame[] | null,\n    codeFrame?: CodeFrame | null\n  ): void {\n    const lastStatus = this.symbolicated[type].status;\n    if (error != null) {\n      this.symbolicated[type] = {\n        error,\n        stack: null,\n        status: 'FAILED',\n      };\n    } else if (stack != null) {\n      if (codeFrame) {\n        this.codeFrame = codeFrame;\n      }\n\n      this.symbolicated[type] = {\n        error: null,\n        stack,\n        status: 'COMPLETE',\n      };\n    } else {\n      this.symbolicated[type] = {\n        error: null,\n        stack: null,\n        status: 'PENDING',\n      };\n    }\n\n    const status = this.symbolicated[type].status;\n    if (lastStatus !== status) {\n      if (['COMPLETE', 'FAILED'].includes(status)) {\n        this.flushCallbacks(type);\n      }\n    }\n  }\n}\n"], "names": ["LogBoxLog", "componentStackToStack", "componentStack", "map", "stack", "file", "fileName", "methodName", "content", "lineNumber", "location", "row", "column", "arguments", "constructor", "data", "symbolicated", "error", "status", "component", "callbacks", "Map", "componentStackCache", "level", "type", "message", "category", "codeFrame", "isComponentError", "count", "incrementCount", "getAvailableStack", "getStack", "flushCallbacks", "get", "callback", "clear", "pushCallback", "Set", "set", "add", "retrySymbolicate", "_symbolicate", "symbolicate", "retry", "LogBoxSymbolication", "deleteStack", "handleSymbolicate", "length", "updateStatus", "then", "lastStatus", "includes"], "mappings": "AAAA;;;;;;CAMC;;;;+BAoDYA;;;eAAAA;;;6EAlDwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCrC,SAASC,sBAAsBC,cAA8B;IAC3D,OAAOA,eAAeC,GAAG,CAAC,CAACC;YAGbA,iBACJA;eAJ4B;YACpCC,MAAMD,MAAME,QAAQ;YACpBC,YAAYH,MAAMI,OAAO;YACzBC,YAAYL,EAAAA,kBAAAA,MAAMM,QAAQ,qBAAdN,gBAAgBO,GAAG,KAAI;YACnCC,QAAQR,EAAAA,mBAAAA,MAAMM,QAAQ,qBAAdN,iBAAgBQ,MAAM,KAAI;YAClCC,WAAW,EAAE;QACf;;AACF;AAUO,MAAMb;IAyBXc,YACEC,IAEC,CACD;aAnBFC,eAAuD;YACrDZ,OAAO;gBACLa,OAAO;gBACPb,OAAO;gBACPc,QAAQ;YACV;YACAC,WAAW;gBACTF,OAAO;gBACPb,OAAO;gBACPc,QAAQ;YACV;QACF;aAEQE,YAAwD,IAAIC;aAkF5DC,sBAA2C;QA3EjD,IAAI,CAACC,KAAK,GAAGR,KAAKQ,KAAK;QACvB,IAAI,CAACC,IAAI,GAAGT,KAAKS,IAAI,IAAI;QACzB,IAAI,CAACC,OAAO,GAAGV,KAAKU,OAAO;QAC3B,IAAI,CAACrB,KAAK,GAAGW,KAAKX,KAAK;QACvB,IAAI,CAACsB,QAAQ,GAAGX,KAAKW,QAAQ;QAC7B,IAAI,CAACxB,cAAc,GAAGa,KAAKb,cAAc;QACzC,IAAI,CAACyB,SAAS,GAAGZ,KAAKY,SAAS;QAC/B,IAAI,CAACC,gBAAgB,GAAGb,KAAKa,gBAAgB;QAC7C,IAAI,CAACC,KAAK,GAAG;QACb,IAAI,CAACb,YAAY,GAAGD,KAAKC,YAAY,IAAI,IAAI,CAACA,YAAY;IAC5D;IAEAc,iBAAuB;QACrB,IAAI,CAACD,KAAK,IAAI;IAChB;IAEAE,kBAAkBP,IAAe,EAAuB;QACtD,IAAI,IAAI,CAACR,YAAY,CAACQ,KAAK,CAACN,MAAM,KAAK,YAAY;YACjD,OAAO,IAAI,CAACF,YAAY,CAACQ,KAAK,CAACpB,KAAK;QACtC;QACA,OAAO,IAAI,CAAC4B,QAAQ,CAACR;IACvB;IAEQS,eAAeT,IAAe,EAAQ;QAC5C,MAAMJ,YAAY,IAAI,CAACA,SAAS,CAACc,GAAG,CAACV;QACrC,MAAMN,SAAS,IAAI,CAACF,YAAY,CAACQ,KAAK,CAACN,MAAM;QAC7C,IAAIE,WAAW;YACb,KAAK,MAAMe,YAAYf,UAAW;gBAChCe,SAASjB;YACX;YACAE,UAAUgB,KAAK;QACjB;IACF;IAEQC,aAAab,IAAe,EAAEW,QAA+B,EAAQ;QAC3E,IAAIf,YAAY,IAAI,CAACA,SAAS,CAACc,GAAG,CAACV;QACnC,IAAI,CAACJ,WAAW;YACdA,YAAY,IAAIkB;YAChB,IAAI,CAAClB,SAAS,CAACmB,GAAG,CAACf,MAAMJ;QAC3B;QACAA,UAAUoB,GAAG,CAACL;IAChB;IAEAM,iBAAiBjB,IAAe,EAAEW,QAAgD,EAAQ;QACxF,IAAI,CAACO,YAAY,CAAClB,MAAM,MAAMW;IAChC;IAEAQ,YAAYnB,IAAe,EAAEW,QAAgD,EAAQ;QACnF,IAAI,CAACO,YAAY,CAAClB,MAAM,OAAOW;IACjC;IAEQO,aACNlB,IAAe,EACfoB,KAAc,EACdT,QAAgD,EAC1C;QACN,IAAIA,UAAU;YACZ,IAAI,CAACE,YAAY,CAACb,MAAMW;QAC1B;QACA,MAAMjB,SAAS,IAAI,CAACF,YAAY,CAACQ,KAAK,CAACN,MAAM;QAE7C,IAAIA,WAAW,YAAY;YACzB,OAAO,IAAI,CAACe,cAAc,CAACT;QAC7B;QAEA,IAAIoB,OAAO;YACTC,qBAAoBC,WAAW,CAAC,IAAI,CAACd,QAAQ,CAACR;YAC9C,IAAI,CAACuB,iBAAiB,CAACvB;QACzB,OAAO;YACL,IAAIN,WAAW,QAAQ;gBACrB,IAAI,CAAC6B,iBAAiB,CAACvB;YACzB;QACF;IACF;IAIQQ,SAASR,IAAe,EAAgB;QAC9C,IAAIA,SAAS,aAAa;YACxB,IAAI,IAAI,CAACF,mBAAmB,IAAI,MAAM;gBACpC,IAAI,CAACA,mBAAmB,GAAGrB,sBAAsB,IAAI,CAACC,cAAc;YACtE;YACA,OAAO,IAAI,CAACoB,mBAAmB;QACjC;QACA,OAAO,IAAI,CAAClB,KAAK;IACnB;IAEQ2C,kBAAkBvB,IAAe,EAAQ;YAClB;QAA7B,IAAIA,SAAS,eAAe,GAAC,uBAAA,IAAI,CAACtB,cAAc,qBAAnB,qBAAqB8C,MAAM,GAAE;YACxD;QACF;QAEA,IAAI,IAAI,CAAChC,YAAY,CAACQ,KAAK,CAACN,MAAM,KAAK,WAAW;YAChD,IAAI,CAAC+B,YAAY,CAACzB,MAAM,MAAM,MAAM;YACpCqB,qBAAoBF,WAAW,CAAC,IAAI,CAACX,QAAQ,CAACR,OAAO0B,IAAI,CACvD,CAACnC;gBACC,IAAI,CAACkC,YAAY,CAACzB,MAAM,MAAMT,wBAAAA,KAAMX,KAAK,EAAEW,wBAAAA,KAAMY,SAAS;YAC5D,GACA,CAACV;gBACC,IAAI,CAACgC,YAAY,CAACzB,MAAMP,OAAO,MAAM;YACvC;QAEJ;IACF;IAEQgC,aACNzB,IAAe,EACfP,KAAoB,EACpBb,KAA2B,EAC3BuB,SAA4B,EACtB;QACN,MAAMwB,aAAa,IAAI,CAACnC,YAAY,CAACQ,KAAK,CAACN,MAAM;QACjD,IAAID,SAAS,MAAM;YACjB,IAAI,CAACD,YAAY,CAACQ,KAAK,GAAG;gBACxBP;gBACAb,OAAO;gBACPc,QAAQ;YACV;QACF,OAAO,IAAId,SAAS,MAAM;YACxB,IAAIuB,WAAW;gBACb,IAAI,CAACA,SAAS,GAAGA;YACnB;YAEA,IAAI,CAACX,YAAY,CAACQ,KAAK,GAAG;gBACxBP,OAAO;gBACPb;gBACAc,QAAQ;YACV;QACF,OAAO;YACL,IAAI,CAACF,YAAY,CAACQ,KAAK,GAAG;gBACxBP,OAAO;gBACPb,OAAO;gBACPc,QAAQ;YACV;QACF;QAEA,MAAMA,SAAS,IAAI,CAACF,YAAY,CAACQ,KAAK,CAACN,MAAM;QAC7C,IAAIiC,eAAejC,QAAQ;YACzB,IAAI;gBAAC;gBAAY;aAAS,CAACkC,QAAQ,CAAClC,SAAS;gBAC3C,IAAI,CAACe,cAAc,CAACT;YACtB;QACF;IACF;AACF"}