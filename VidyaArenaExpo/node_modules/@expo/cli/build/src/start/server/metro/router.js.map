{"version": 3, "sources": ["../../../../../src/start/server/metro/router.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport type { MiddlewareMatcher } from '@expo/server/build/types';\nimport chalk from 'chalk';\nimport { sync as globSync } from 'glob';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { Log } from '../../../log';\nimport { directoryExistsSync } from '../../../utils/dir';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { learnMore } from '../../../utils/link';\n\nconst debug = require('debug')('expo:start:server:metro:router') as typeof console.log;\n\n/**\n * Get the relative path for requiring the `/app` folder relative to the `expo-router/entry` file.\n * This mechanism does require the server to restart after the `expo-router` package is installed.\n */\nexport function getAppRouterRelativeEntryPath(\n  projectRoot: string,\n  routerDirectory: string = getRouterDirectory(projectRoot)\n): string | undefined {\n  // Auto pick App entry\n  const routerEntry =\n    resolveFrom.silent(projectRoot, 'expo-router/entry') ?? getFallbackEntryRoot(projectRoot);\n  if (!routerEntry) {\n    return undefined;\n  }\n  // It doesn't matter if the app folder exists.\n  const appFolder = path.join(projectRoot, routerDirectory);\n  const appRoot = path.relative(path.dirname(routerEntry), appFolder);\n  debug('expo-router entry', routerEntry, appFolder, appRoot);\n  return appRoot;\n}\n\n/** If the `expo-router` package is not installed, then use the `expo` package to determine where the node modules are relative to the project. */\nfunction getFallbackEntryRoot(projectRoot: string): string {\n  const expoRoot = resolveFrom.silent(projectRoot, 'expo/package.json');\n  if (expoRoot) {\n    return path.join(path.dirname(path.dirname(expoRoot)), 'expo-router/entry');\n  }\n  return path.join(projectRoot, 'node_modules/expo-router/entry');\n}\n\nexport function getRouterDirectoryModuleIdWithManifest(\n  projectRoot: string,\n  exp: ExpoConfig\n): string {\n  return toPosixPath(exp.extra?.router?.root ?? getRouterDirectory(projectRoot));\n}\n\nlet hasWarnedAboutSrcDir = false;\nconst logSrcDir = () => {\n  if (hasWarnedAboutSrcDir) return;\n  hasWarnedAboutSrcDir = true;\n  Log.log(chalk.gray('Using src/app as the root directory for Expo Router.'));\n};\n\nexport function getRouterDirectory(projectRoot: string): string {\n  // more specific directories first\n  if (directoryExistsSync(path.join(projectRoot, 'src', 'app'))) {\n    logSrcDir();\n    return path.join('src', 'app');\n  }\n\n  debug('Using app as the root directory for Expo Router.');\n  return 'app';\n}\n\nexport function isApiRouteConvention(name: string): boolean {\n  return /\\+api\\.[tj]sx?$/.test(name);\n}\n\nexport function getApiRoutesForDirectory(cwd: string) {\n  return globSync('**/*+api.@(ts|tsx|js|jsx)', {\n    cwd,\n    absolute: true,\n    dot: true,\n  });\n}\n\n/**\n * Gets the +middleware file for a given directory. In\n * @param cwd\n */\nexport function getMiddlewareForDirectory(cwd: string): string | null {\n  const files = globSync('+middleware.@(ts|tsx|js|jsx)', {\n    cwd,\n    absolute: true,\n    dot: true,\n  });\n\n  if (files.length === 0) return null;\n\n  if (files.length > 1) {\n    // In development, throw an error if there are multiple root-level middleware files\n    if (process.env.NODE_ENV !== 'production') {\n      const relativePaths = files.map((f) => './' + path.relative(cwd, f)).sort();\n      throw new Error(\n        `Only one middleware file is allowed. Keep one of the conflicting files: ${relativePaths.map((p) => `\"${p}\"`).join(' or ')}`\n      );\n    }\n  }\n\n  return files[0];\n}\n\n// Used to emulate a context module, but way faster. TODO: May need to adjust the extensions to stay in sync with Metro.\nexport function getRoutePaths(cwd: string) {\n  return globSync('**/*.@(ts|tsx|js|jsx)', {\n    cwd,\n    dot: true,\n  }).map((p) => './' + normalizePaths(p));\n}\n\nfunction normalizePaths(p: string) {\n  return p.replace(/\\\\/g, '/');\n}\n\nlet hasWarnedAboutApiRouteOutput = false;\nlet hasWarnedAboutMiddlewareOutput = false;\n\nexport function hasWarnedAboutApiRoutes() {\n  return hasWarnedAboutApiRouteOutput;\n}\n\nexport function hasWarnedAboutMiddleware() {\n  return hasWarnedAboutMiddlewareOutput;\n}\n\nexport function warnInvalidWebOutput() {\n  if (!hasWarnedAboutApiRouteOutput) {\n    Log.warn(\n      chalk.yellow`Using API routes requires the {bold web.output} to be set to {bold \"server\"} in the project {bold app.json}. ${learnMore(\n        'https://docs.expo.dev/router/reference/api-routes/'\n      )}`\n    );\n  }\n\n  hasWarnedAboutApiRouteOutput = true;\n}\n\nexport function warnInvalidMiddlewareOutput() {\n  if (!hasWarnedAboutMiddlewareOutput) {\n    Log.warn(\n      chalk.yellow`Using middleware requires the {bold web.output} to be set to {bold \"server\"} in the project {bold app.json}. ${learnMore(\n        'https://docs.expo.dev/router/reference/api-routes/'\n      )}`\n    );\n  }\n\n  hasWarnedAboutMiddlewareOutput = true;\n}\n\nexport function warnInvalidMiddlewareMatcherSettings(matcher: MiddlewareMatcher) {\n  const validMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'];\n\n  // Ensure methods are valid HTTP methods\n  if (matcher.methods) {\n    if (!Array.isArray(matcher.methods)) {\n      Log.error(\n        chalk.red`Middleware matcher methods must be an array of valid HTTP methods. Supported methods are: ${validMethods.join(', ')}`\n      );\n    } else {\n      for (const method of matcher.methods) {\n        if (!validMethods.includes(method)) {\n          Log.error(\n            chalk.red`Invalid middleware HTTP method: ${method}. Supported methods are: ${validMethods.join(', ')}`\n          );\n        }\n      }\n    }\n  }\n\n  // Ensure patterns are either a string or RegExp\n  if (matcher.patterns) {\n    const patterns = Array.isArray(matcher.patterns) ? matcher.patterns : [matcher.patterns];\n    for (const pattern of patterns) {\n      if (typeof pattern !== 'string' && !(pattern instanceof RegExp)) {\n        Log.error(\n          chalk.red`Middleware matcher patterns must be strings or regular expressions. Received: ${String(\n            pattern\n          )}`\n        );\n      }\n\n      if (typeof pattern === 'string' && !pattern.startsWith('/')) {\n        Log.error(\n          chalk.red`String patterns in middleware matcher must start with '/'. Received: ${pattern}`\n        );\n      }\n    }\n  }\n}\n"], "names": ["getApiRoutesForDirectory", "getAppRouterRelativeEntryPath", "getMiddlewareForDirectory", "getRoutePaths", "getRouterDirectory", "getRouterDirectoryModuleIdWithManifest", "hasWarnedAboutApiRoutes", "hasWarnedAboutMiddleware", "isApiRouteConvention", "warnInvalidMiddlewareMatcherSettings", "warnInvalidMiddlewareOutput", "warnInvalidWebOutput", "debug", "require", "projectRoot", "routerDirectory", "routerEntry", "resolveFrom", "silent", "getFallbackEntryRoot", "undefined", "appFolder", "path", "join", "appRoot", "relative", "dirname", "expoRoot", "exp", "toPosixPath", "extra", "router", "root", "hasWarnedAboutSrcDir", "logSrcDir", "Log", "log", "chalk", "gray", "directoryExistsSync", "name", "test", "cwd", "globSync", "absolute", "dot", "files", "length", "process", "env", "NODE_ENV", "relativePaths", "map", "f", "sort", "Error", "p", "normalizePaths", "replace", "hasWarnedAboutApiRouteOutput", "hasWarnedAboutMiddlewareOutput", "warn", "yellow", "learnMore", "matcher", "validMethods", "methods", "Array", "isArray", "error", "red", "method", "includes", "patterns", "pattern", "RegExp", "String", "startsWith"], "mappings": ";;;;;;;;;;;IAyEgBA,wBAAwB;eAAxBA;;IAvDAC,6BAA6B;eAA7BA;;IAmEAC,yBAAyB;eAAzBA;;IAuBAC,aAAa;eAAbA;;IAlDAC,kBAAkB;eAAlBA;;IAdAC,sCAAsC;eAAtCA;;IA8EAC,uBAAuB;eAAvBA;;IAIAC,wBAAwB;eAAxBA;;IAzDAC,oBAAoB;eAApBA;;IAqFAC,oCAAoC;eAApCA;;IAZAC,2BAA2B;eAA3BA;;IAZAC,oBAAoB;eAApBA;;;;gEAhIE;;;;;;;yBACe;;;;;;;gEAChB;;;;;;;gEACO;;;;;;qBAEJ;qBACgB;0BACR;sBACF;;;;;;AAE1B,MAAMC,QAAQC,QAAQ,SAAS;AAMxB,SAASZ,8BACda,WAAmB,EACnBC,kBAA0BX,mBAAmBU,YAAY;IAEzD,sBAAsB;IACtB,MAAME,cACJC,sBAAW,CAACC,MAAM,CAACJ,aAAa,wBAAwBK,qBAAqBL;IAC/E,IAAI,CAACE,aAAa;QAChB,OAAOI;IACT;IACA,8CAA8C;IAC9C,MAAMC,YAAYC,eAAI,CAACC,IAAI,CAACT,aAAaC;IACzC,MAAMS,UAAUF,eAAI,CAACG,QAAQ,CAACH,eAAI,CAACI,OAAO,CAACV,cAAcK;IACzDT,MAAM,qBAAqBI,aAAaK,WAAWG;IACnD,OAAOA;AACT;AAEA,gJAAgJ,GAChJ,SAASL,qBAAqBL,WAAmB;IAC/C,MAAMa,WAAWV,sBAAW,CAACC,MAAM,CAACJ,aAAa;IACjD,IAAIa,UAAU;QACZ,OAAOL,eAAI,CAACC,IAAI,CAACD,eAAI,CAACI,OAAO,CAACJ,eAAI,CAACI,OAAO,CAACC,YAAY;IACzD;IACA,OAAOL,eAAI,CAACC,IAAI,CAACT,aAAa;AAChC;AAEO,SAAST,uCACdS,WAAmB,EACnBc,GAAe;QAEIA,mBAAAA;IAAnB,OAAOC,IAAAA,qBAAW,EAACD,EAAAA,aAAAA,IAAIE,KAAK,sBAATF,oBAAAA,WAAWG,MAAM,qBAAjBH,kBAAmBI,IAAI,KAAI5B,mBAAmBU;AACnE;AAEA,IAAImB,uBAAuB;AAC3B,MAAMC,YAAY;IAChB,IAAID,sBAAsB;IAC1BA,uBAAuB;IACvBE,QAAG,CAACC,GAAG,CAACC,gBAAK,CAACC,IAAI,CAAC;AACrB;AAEO,SAASlC,mBAAmBU,WAAmB;IACpD,kCAAkC;IAClC,IAAIyB,IAAAA,wBAAmB,EAACjB,eAAI,CAACC,IAAI,CAACT,aAAa,OAAO,SAAS;QAC7DoB;QACA,OAAOZ,eAAI,CAACC,IAAI,CAAC,OAAO;IAC1B;IAEAX,MAAM;IACN,OAAO;AACT;AAEO,SAASJ,qBAAqBgC,IAAY;IAC/C,OAAO,kBAAkBC,IAAI,CAACD;AAChC;AAEO,SAASxC,yBAAyB0C,GAAW;IAClD,OAAOC,IAAAA,YAAQ,EAAC,6BAA6B;QAC3CD;QACAE,UAAU;QACVC,KAAK;IACP;AACF;AAMO,SAAS3C,0BAA0BwC,GAAW;IACnD,MAAMI,QAAQH,IAAAA,YAAQ,EAAC,gCAAgC;QACrDD;QACAE,UAAU;QACVC,KAAK;IACP;IAEA,IAAIC,MAAMC,MAAM,KAAK,GAAG,OAAO;IAE/B,IAAID,MAAMC,MAAM,GAAG,GAAG;QACpB,mFAAmF;QACnF,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,MAAMC,gBAAgBL,MAAMM,GAAG,CAAC,CAACC,IAAM,OAAO/B,eAAI,CAACG,QAAQ,CAACiB,KAAKW,IAAIC,IAAI;YACzE,MAAM,IAAIC,MACR,CAAC,wEAAwE,EAAEJ,cAAcC,GAAG,CAAC,CAACI,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEjC,IAAI,CAAC,SAAS;QAEhI;IACF;IAEA,OAAOuB,KAAK,CAAC,EAAE;AACjB;AAGO,SAAS3C,cAAcuC,GAAW;IACvC,OAAOC,IAAAA,YAAQ,EAAC,yBAAyB;QACvCD;QACAG,KAAK;IACP,GAAGO,GAAG,CAAC,CAACI,IAAM,OAAOC,eAAeD;AACtC;AAEA,SAASC,eAAeD,CAAS;IAC/B,OAAOA,EAAEE,OAAO,CAAC,OAAO;AAC1B;AAEA,IAAIC,+BAA+B;AACnC,IAAIC,iCAAiC;AAE9B,SAAStD;IACd,OAAOqD;AACT;AAEO,SAASpD;IACd,OAAOqD;AACT;AAEO,SAASjD;IACd,IAAI,CAACgD,8BAA8B;QACjCxB,QAAG,CAAC0B,IAAI,CACNxB,gBAAK,CAACyB,MAAM,CAAC,6GAA6G,EAAEC,IAAAA,eAAS,EACnI,sDACA,CAAC;IAEP;IAEAJ,+BAA+B;AACjC;AAEO,SAASjD;IACd,IAAI,CAACkD,gCAAgC;QACnCzB,QAAG,CAAC0B,IAAI,CACNxB,gBAAK,CAACyB,MAAM,CAAC,6GAA6G,EAAEC,IAAAA,eAAS,EACnI,sDACA,CAAC;IAEP;IAEAH,iCAAiC;AACnC;AAEO,SAASnD,qCAAqCuD,OAA0B;IAC7E,MAAMC,eAAe;QAAC;QAAO;QAAQ;QAAO;QAAS;QAAU;QAAW;KAAO;IAEjF,wCAAwC;IACxC,IAAID,QAAQE,OAAO,EAAE;QACnB,IAAI,CAACC,MAAMC,OAAO,CAACJ,QAAQE,OAAO,GAAG;YACnC/B,QAAG,CAACkC,KAAK,CACPhC,gBAAK,CAACiC,GAAG,CAAC,0FAA0F,EAAEL,aAAa1C,IAAI,CAAC,MAAM,CAAC;QAEnI,OAAO;YACL,KAAK,MAAMgD,UAAUP,QAAQE,OAAO,CAAE;gBACpC,IAAI,CAACD,aAAaO,QAAQ,CAACD,SAAS;oBAClCpC,QAAG,CAACkC,KAAK,CACPhC,gBAAK,CAACiC,GAAG,CAAC,gCAAgC,EAAEC,OAAO,yBAAyB,EAAEN,aAAa1C,IAAI,CAAC,MAAM,CAAC;gBAE3G;YACF;QACF;IACF;IAEA,gDAAgD;IAChD,IAAIyC,QAAQS,QAAQ,EAAE;QACpB,MAAMA,WAAWN,MAAMC,OAAO,CAACJ,QAAQS,QAAQ,IAAIT,QAAQS,QAAQ,GAAG;YAACT,QAAQS,QAAQ;SAAC;QACxF,KAAK,MAAMC,WAAWD,SAAU;YAC9B,IAAI,OAAOC,YAAY,YAAY,CAAEA,CAAAA,mBAAmBC,MAAK,GAAI;gBAC/DxC,QAAG,CAACkC,KAAK,CACPhC,gBAAK,CAACiC,GAAG,CAAC,8EAA8E,EAAEM,OACxFF,SACA,CAAC;YAEP;YAEA,IAAI,OAAOA,YAAY,YAAY,CAACA,QAAQG,UAAU,CAAC,MAAM;gBAC3D1C,QAAG,CAACkC,KAAK,CACPhC,gBAAK,CAACiC,GAAG,CAAC,qEAAqE,EAAEI,QAAQ,CAAC;YAE9F;QACF;IACF;AACF"}