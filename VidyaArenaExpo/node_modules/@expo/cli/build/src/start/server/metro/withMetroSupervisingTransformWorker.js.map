{"version": 3, "sources": ["../../../../../src/start/server/metro/withMetroSupervisingTransformWorker.ts"], "sourcesContent": ["import type { ConfigT as MetroConfig } from '@expo/metro/metro-config';\nimport { unstable_transformerPath, internal_supervisingTransformerPath } from '@expo/metro-config';\n\nconst debug = require('debug')(\n  'expo:metro:withMetroSupervisingTransformWorker'\n) as typeof console.log;\n\ndeclare module '@expo/metro/metro-transform-worker' {\n  export interface JsTransformerConfig {\n    expo_customTransformerPath?: string | false;\n  }\n}\n\n// The default babel transformer is either `@expo/metro-config/babel-transformer` set by the user\n// or @expo/metro-config/build/babel-transformer\nconst defaultBabelTransformerPaths = [\n  require.resolve('@expo/metro-config/babel-transformer'),\n  require.resolve('@expo/metro-config/build/babel-transformer'),\n];\n\n/** Adds a wrapper around a user's transformerPath or babelTransformerPath\n * @remarks\n * It's relatively common still for people to set custom transformerPath\n * or babelTransformerPaths. These are used to customize how files are transformed\n * and wrap around Expo's transformer/babel-transformer.\n *\n * This config override customizes them.\n * If a user has a custom transformer or babelTransformerPath set, we\n * first load the \"supervising transformer\"\n *\n * This is a transformer entrypoint that's always supposed to load first and\n * loads the user's custom transformer from `config.transformer.expo_customTransformerPath`\n * instead of `config.transformerPath`\n *\n * This supervisor has two tasks:\n * - It adds a try-catch. When the user's transformer fails to load, we output a better error message\n * - It forces the same versions of Metro and @expo/metro-config to load that we depend on\n *   (unless threading is disabled, which makes this override unsafe)\n *\n * We do this because transformers and babel transformers *must not* use different\n * versions of Metro. This is unsupported and undefined behavior and will lead to\n * bugs and errors.\n */\nexport function withMetroSupervisingTransformWorker(config: MetroConfig): MetroConfig {\n  // NOTE: This is usually a required property, but we don't always set it in mocks\n  const originalBabelTransformerPath = config.transformer?.babelTransformerPath;\n  const originalTransformerPath = config.transformerPath;\n\n  const hasDefaultTransformerPath = originalTransformerPath === unstable_transformerPath;\n  const hasDefaultBabelTransformerPath =\n    !originalBabelTransformerPath ||\n    defaultBabelTransformerPaths.includes(originalBabelTransformerPath);\n  if (hasDefaultTransformerPath && hasDefaultBabelTransformerPath) {\n    return config;\n  }\n\n  // DEBUGGING: When set to false the supervisor is disabled for debugging\n  if (config.transformer?.expo_customTransformerPath === false) {\n    debug('Skipping transform worker supervisor: transformer.expo_customTransformerPath is false');\n    return config;\n  }\n\n  // We modify the config if the user either has a custom transformerPath or\n  // a custom transformer.babelTransformerPath\n  // NOTE: It's not a bad thing if we load the superivising transformer even if\n  // we don't need to. It will do nothing to our transformer\n  if (!hasDefaultTransformerPath) {\n    debug('Detected customized \"transformerPath\"');\n  }\n  if (!hasDefaultBabelTransformerPath) {\n    debug('Detected customized \"transformer.babelTransformerPath\"');\n  }\n\n  debug('Applying transform worker supervisor to \"transformerPath\"');\n  return {\n    ...config,\n    transformerPath: internal_supervisingTransformerPath,\n    transformer: {\n      ...config.transformer,\n      // Only pass the custom transformer path, if the user has set one, otherwise we're only applying\n      // the supervisor for the Babel transformer\n      expo_customTransformerPath: !hasDefaultTransformerPath ? originalTransformerPath : undefined,\n    },\n  };\n}\n"], "names": ["withMetroSupervisingTransformWorker", "debug", "require", "defaultBabelTransformerPaths", "resolve", "config", "originalBabelTransformerPath", "transformer", "babelTransformerPath", "originalTransformerPath", "transformerPath", "hasDefaultTransformerPath", "unstable_transformerPath", "hasDefaultBabelTransformerPath", "includes", "expo_customTransformerPath", "internal_supervisingTransformerPath", "undefined"], "mappings": ";;;;+BA2CgBA;;;eAAAA;;;;yBA1C8D;;;;;;AAE9E,MAAMC,QAAQC,QAAQ,SACpB;AASF,iGAAiG;AACjG,gDAAgD;AAChD,MAAMC,+BAA+B;IACnCD,QAAQE,OAAO,CAAC;IAChBF,QAAQE,OAAO,CAAC;CACjB;AAyBM,SAASJ,oCAAoCK,MAAmB;QAEhCA,qBAYjCA;IAbJ,iFAAiF;IACjF,MAAMC,gCAA+BD,sBAAAA,OAAOE,WAAW,qBAAlBF,oBAAoBG,oBAAoB;IAC7E,MAAMC,0BAA0BJ,OAAOK,eAAe;IAEtD,MAAMC,4BAA4BF,4BAA4BG,uCAAwB;IACtF,MAAMC,iCACJ,CAACP,gCACDH,6BAA6BW,QAAQ,CAACR;IACxC,IAAIK,6BAA6BE,gCAAgC;QAC/D,OAAOR;IACT;IAEA,wEAAwE;IACxE,IAAIA,EAAAA,uBAAAA,OAAOE,WAAW,qBAAlBF,qBAAoBU,0BAA0B,MAAK,OAAO;QAC5Dd,MAAM;QACN,OAAOI;IACT;IAEA,0EAA0E;IAC1E,4CAA4C;IAC5C,6EAA6E;IAC7E,0DAA0D;IAC1D,IAAI,CAACM,2BAA2B;QAC9BV,MAAM;IACR;IACA,IAAI,CAACY,gCAAgC;QACnCZ,MAAM;IACR;IAEAA,MAAM;IACN,OAAO;QACL,GAAGI,MAAM;QACTK,iBAAiBM,kDAAmC;QACpDT,aAAa;YACX,GAAGF,OAAOE,WAAW;YACrB,gGAAgG;YAChG,2CAA2C;YAC3CQ,4BAA4B,CAACJ,4BAA4BF,0BAA0BQ;QACrF;IACF;AACF"}