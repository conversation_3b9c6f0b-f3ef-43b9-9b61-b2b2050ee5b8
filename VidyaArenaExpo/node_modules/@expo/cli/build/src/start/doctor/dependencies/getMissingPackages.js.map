{"version": 3, "sources": ["../../../../../src/start/doctor/dependencies/getMissingPackages.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\n\nimport { getCombinedKnownVersionsAsync } from './getVersionedPackages';\n\nconst debug = require('debug')('expo:doctor:dependencies:getMissingPackages') as typeof console.log;\n\nexport type ResolvedPackage = {\n  /** Module ID pointing to the library `package.json`. */\n  file: string;\n  /** NPM package name. */\n  pkg: string;\n  /** Required version range. */\n  version?: string;\n  /** If the dependency should be installed as a `devDependency` */\n  dev?: boolean;\n};\n\n/** Given a set of required packages, this method returns a list of missing packages. */\nexport function collectMissingPackages(\n  projectRoot: string,\n  requiredPackages: ResolvedPackage[]\n): {\n  missing: ResolvedPackage[];\n  resolutions: Record<string, string>;\n} {\n  const resolutions: Record<string, string> = {};\n\n  const missingPackages = requiredPackages.filter((p) => {\n    const resolved = resolveFrom.silent(projectRoot, p.file);\n    if (!resolved || !versionSatisfiesRequiredPackage(resolved, p)) {\n      return true;\n    }\n    resolutions[p.pkg] = resolved;\n    return false;\n  });\n\n  return { missing: missingPackages, resolutions };\n}\n\nexport function versionSatisfiesRequiredPackage(\n  packageJsonFilePath: string,\n  resolvedPackage: Pick<ResolvedPackage, 'version' | 'pkg'>\n): boolean {\n  // If the version is specified, check that it satisfies the installed version.\n  if (!resolvedPackage.version) {\n    debug(`Required package \"${resolvedPackage.pkg}\" found (no version constraint specified).`);\n    return true;\n  }\n\n  const pkgJson = JsonFile.read(packageJsonFilePath);\n  if (\n    // package.json has version.\n    typeof pkgJson.version === 'string' &&\n    // semver satisfaction.\n    semver.satisfies(pkgJson.version, resolvedPackage.version)\n  ) {\n    return true;\n  }\n  debug(\n    `Installed package \"${resolvedPackage.pkg}\" does not satisfy version constraint \"${resolvedPackage.version}\" (version: \"${pkgJson.version}\")`\n  );\n  return false;\n}\n\n/**\n * Collect missing packages given a list of required packages.\n * Any missing packages will be versioned to the known versions for the current SDK.\n *\n * @param projectRoot\n * @param props.requiredPackages list of required packages to check for\n * @returns list of missing packages and resolutions to existing packages.\n */\nexport async function getMissingPackagesAsync(\n  projectRoot: string,\n  {\n    sdkVersion,\n    requiredPackages,\n  }: {\n    sdkVersion?: string;\n    requiredPackages: ResolvedPackage[];\n  }\n): Promise<{\n  missing: ResolvedPackage[];\n  resolutions: Record<string, string>;\n}> {\n  const results = collectMissingPackages(projectRoot, requiredPackages);\n  if (!results.missing.length) {\n    return results;\n  }\n\n  // Ensure the versions are right for the SDK that the project is currently using.\n  await mutatePackagesWithKnownVersionsAsync(projectRoot, sdkVersion, results.missing);\n\n  return results;\n}\n\nexport async function mutatePackagesWithKnownVersionsAsync(\n  projectRoot: string,\n  sdkVersion: string | undefined,\n  packages: ResolvedPackage[]\n) {\n  // Ensure the versions are right for the SDK that the project is currently using.\n  const relatedPackages = await getCombinedKnownVersionsAsync({ projectRoot, sdkVersion });\n  for (const pkg of packages) {\n    if (\n      // Only use the SDK versions if the package does not already have a hardcoded version.\n      // We do this because some packages have API coded into the CLI which expects an exact version.\n      !pkg.version &&\n      pkg.pkg in relatedPackages\n    ) {\n      pkg.version = relatedPackages[pkg.pkg];\n    }\n  }\n  return packages;\n}\n"], "names": ["collectMissingPackages", "getMissingPackagesAsync", "mutatePackagesWithKnownVersionsAsync", "versionSatisfiesRequiredPackage", "debug", "require", "projectRoot", "requiredPackages", "resolutions", "missingPackages", "filter", "p", "resolved", "resolveFrom", "silent", "file", "pkg", "missing", "packageJsonFilePath", "resolvedPackage", "version", "pkgJson", "JsonFile", "read", "semver", "satisfies", "sdkVersion", "results", "length", "packages", "relatedPackages", "getCombinedKnownVersionsAsync"], "mappings": ";;;;;;;;;;;IAoBgBA,sBAAsB;eAAtBA;;IAsDMC,uBAAuB;eAAvBA;;IAwBAC,oCAAoC;eAApCA;;IAzDNC,+BAA+B;eAA/BA;;;;gEAzCK;;;;;;;gEACG;;;;;;;gEACL;;;;;;sCAE2B;;;;;;AAE9C,MAAMC,QAAQC,QAAQ,SAAS;AAcxB,SAASL,uBACdM,WAAmB,EACnBC,gBAAmC;IAKnC,MAAMC,cAAsC,CAAC;IAE7C,MAAMC,kBAAkBF,iBAAiBG,MAAM,CAAC,CAACC;QAC/C,MAAMC,WAAWC,sBAAW,CAACC,MAAM,CAACR,aAAaK,EAAEI,IAAI;QACvD,IAAI,CAACH,YAAY,CAACT,gCAAgCS,UAAUD,IAAI;YAC9D,OAAO;QACT;QACAH,WAAW,CAACG,EAAEK,GAAG,CAAC,GAAGJ;QACrB,OAAO;IACT;IAEA,OAAO;QAAEK,SAASR;QAAiBD;IAAY;AACjD;AAEO,SAASL,gCACde,mBAA2B,EAC3BC,eAAyD;IAEzD,8EAA8E;IAC9E,IAAI,CAACA,gBAAgBC,OAAO,EAAE;QAC5BhB,MAAM,CAAC,kBAAkB,EAAEe,gBAAgBH,GAAG,CAAC,0CAA0C,CAAC;QAC1F,OAAO;IACT;IAEA,MAAMK,UAAUC,mBAAQ,CAACC,IAAI,CAACL;IAC9B,IACE,4BAA4B;IAC5B,OAAOG,QAAQD,OAAO,KAAK,YAC3B,uBAAuB;IACvBI,iBAAM,CAACC,SAAS,CAACJ,QAAQD,OAAO,EAAED,gBAAgBC,OAAO,GACzD;QACA,OAAO;IACT;IACAhB,MACE,CAAC,mBAAmB,EAAEe,gBAAgBH,GAAG,CAAC,uCAAuC,EAAEG,gBAAgBC,OAAO,CAAC,aAAa,EAAEC,QAAQD,OAAO,CAAC,EAAE,CAAC;IAE/I,OAAO;AACT;AAUO,eAAenB,wBACpBK,WAAmB,EACnB,EACEoB,UAAU,EACVnB,gBAAgB,EAIjB;IAKD,MAAMoB,UAAU3B,uBAAuBM,aAAaC;IACpD,IAAI,CAACoB,QAAQV,OAAO,CAACW,MAAM,EAAE;QAC3B,OAAOD;IACT;IAEA,iFAAiF;IACjF,MAAMzB,qCAAqCI,aAAaoB,YAAYC,QAAQV,OAAO;IAEnF,OAAOU;AACT;AAEO,eAAezB,qCACpBI,WAAmB,EACnBoB,UAA8B,EAC9BG,QAA2B;IAE3B,iFAAiF;IACjF,MAAMC,kBAAkB,MAAMC,IAAAA,mDAA6B,EAAC;QAAEzB;QAAaoB;IAAW;IACtF,KAAK,MAAMV,OAAOa,SAAU;QAC1B,IACE,sFAAsF;QACtF,+FAA+F;QAC/F,CAACb,IAAII,OAAO,IACZJ,IAAIA,GAAG,IAAIc,iBACX;YACAd,IAAII,OAAO,GAAGU,eAAe,CAACd,IAAIA,GAAG,CAAC;QACxC;IACF;IACA,OAAOa;AACT"}