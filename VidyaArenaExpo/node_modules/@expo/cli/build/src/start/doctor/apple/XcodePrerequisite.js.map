{"version": 3, "sources": ["../../../../../src/start/doctor/apple/XcodePrerequisite.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\nimport { execSync } from 'child_process';\nimport semver from 'semver';\n\nimport * as Log from '../../../log';\nimport { AbortCommandError } from '../../../utils/errors';\nimport { profile } from '../../../utils/profile';\nimport { confirmAsync } from '../../../utils/prompts';\nimport { Prerequisite } from '../Prerequisite';\n\nconst debug = require('debug')('expo:doctor:apple:xcode') as typeof console.log;\n\n// Based on the Apple announcement (last updated: Aug 2023).\n// https://developer.apple.com/news/upcoming-requirements/?id=04252023a\nconst MIN_XCODE_VERSION = '14.1';\nconst APP_STORE_ID = '497799835';\n\nconst SUGGESTED_XCODE_VERSION = `${MIN_XCODE_VERSION}.0`;\n\nconst promptToOpenAppStoreAsync = async (message: string) => {\n  // This prompt serves no purpose accept informing the user what to do next, we could just open the App Store but it could be confusing if they don't know what's going on.\n  const confirm = await confirmAsync({ initial: true, message });\n  if (confirm) {\n    Log.log(`Going to the App Store, re-run Expo CLI when Xcode has finished installing.`);\n    openAppStore(APP_STORE_ID);\n  }\n};\n\nlet _xcodeVersionPromise: Promise<{ value: string | null | false; error?: string }> | null = null;\n\nexport const getXcodeVersionAsync = async ({\n  silent,\n  force,\n}: { silent?: boolean; force?: boolean } = {}): Promise<string | null | false> => {\n  const logError = silent ? debug : Log.warn;\n  const getVersion = async (): Promise<{ value: string | null | false; error?: string }> => {\n    try {\n      const { stdout } = await spawnAsync('xcodebuild', ['-version']);\n      const last = stdout.match(/^Xcode (\\d+\\.\\d+)/)?.[1];\n      // Convert to a semver string\n      if (last) {\n        const version = `${last}.0`;\n\n        if (!semver.valid(version)) {\n          // Not sure why this would happen, if it does we should add a more confident error message.\n          return { error: `Xcode version is in an unknown format: ${version}`, value: false };\n        }\n        return { value: version };\n      }\n\n      // not sure what's going on\n      return {\n        error:\n          'Unable to check Xcode version. Command ran successfully but no version number was found.',\n        value: null,\n      };\n    } catch {\n      // not installed\n    }\n    return { value: null };\n  };\n\n  if (force) {\n    _xcodeVersionPromise = null;\n  }\n\n  _xcodeVersionPromise = _xcodeVersionPromise ?? getVersion();\n\n  const result = await _xcodeVersionPromise;\n\n  if (result.error) {\n    logError(result.error);\n  }\n\n  return result.value;\n};\n\n/**\n * Open a link to the App Store. Just link in mobile apps, **never** redirect without prompting first.\n *\n * @param appId\n */\nfunction openAppStore(appId: string) {\n  const link = getAppStoreLink(appId);\n  execSync(`open ${link}`, { stdio: 'ignore' });\n}\n\nfunction getAppStoreLink(appId: string): string {\n  if (process.platform === 'darwin') {\n    // TODO: Is there ever a case where the macappstore isn't available on mac?\n    return `macappstore://itunes.apple.com/app/id${appId}`;\n  }\n  return `https://apps.apple.com/us/app/id${appId}`;\n}\n\nfunction spawnForString(cmd: string): string | null {\n  try {\n    return execSync(cmd, { stdio: 'pipe' }).toString().trim();\n  } catch {}\n  return null;\n}\n\n/** @returns a string like `/Applications/Xcode.app/Contents/Developer` when Xcode has a correctly selected path. */\nfunction getXcodeSelectPathAsync() {\n  return spawnForString('/usr/bin/xcode-select --print-path');\n}\nfunction getXcodeInstalled() {\n  return spawnForString('ls /Applications/Xcode.app/Contents/Developer');\n}\n\nexport class XcodePrerequisite extends Prerequisite {\n  static instance = new XcodePrerequisite();\n\n  /**\n   * Ensure Xcode is installed and recent enough to be used with Expo.\n   */\n  async assertImplementation(): Promise<void> {\n    const version = await profile(getXcodeVersionAsync)({ force: process.env.NODE_ENV === 'test' });\n    debug(`Xcode version: ${version}`);\n    if (!version) {\n      // A couple different issues could have occurred, let's check them after we're past the point of no return\n      // since we no longer need to be fast about validation.\n\n      // Ensure Xcode.app can be found before we prompt to sudo select it.\n      if (getXcodeInstalled()) {\n        const selectPath = profile(getXcodeSelectPathAsync)();\n        debug(`Xcode select path: ${selectPath}`);\n        if (!selectPath) {\n          Log.error(\n            [\n              '',\n              chalk.bold('Xcode has not been fully setup for Apple development yet.'),\n              'Download at: https://developer.apple.com/xcode/',\n              'or in the App Store.',\n              '',\n              'After downloading Xcode, run the following two commands in your terminal:',\n              chalk.cyan('  sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer'),\n              chalk.cyan('  sudo xcodebuild -runFirstLaunch'),\n              '',\n              'Then you can re-run Expo CLI. Alternatively, you can build apps in the cloud with EAS CLI, or preview using the Expo Go app on a physical device.',\n              '',\n            ].join('\\n')\n          );\n          throw new AbortCommandError();\n        } else {\n          debug(`Unexpected Xcode setup (version: ${version}, select: ${selectPath})`);\n        }\n      }\n\n      // Almost certainly Xcode isn't installed.\n      await promptToOpenAppStoreAsync(\n        `Xcode must be fully installed before you can continue. If this message is still occurring after installing Xcode, you may need to finish the installation of the developer tools by running: \\`sudo xcode-select -s /Applications/Xcode.app/Contents/Developer\\`. Continue to the App Store?`\n      );\n      throw new AbortCommandError();\n    }\n\n    if (semver.lt(version, SUGGESTED_XCODE_VERSION)) {\n      // Xcode version is too old.\n      await promptToOpenAppStoreAsync(\n        `Xcode (${version}) needs to be updated to at least version ${MIN_XCODE_VERSION}. Continue to the App Store?`\n      );\n      throw new AbortCommandError();\n    }\n  }\n}\n"], "names": ["XcodePrerequisite", "getXcodeVersionAsync", "debug", "require", "MIN_XCODE_VERSION", "APP_STORE_ID", "SUGGESTED_XCODE_VERSION", "promptToOpenAppStoreAsync", "message", "confirm", "<PERSON><PERSON><PERSON>", "initial", "Log", "log", "openAppStore", "_xcodeVersionPromise", "silent", "force", "logError", "warn", "getVersion", "stdout", "spawnAsync", "last", "match", "version", "semver", "valid", "error", "value", "result", "appId", "link", "getAppStoreLink", "execSync", "stdio", "process", "platform", "spawnForString", "cmd", "toString", "trim", "getXcodeSelectPathAsync", "getXcodeInstalled", "Prerequisite", "instance", "assertImplementation", "profile", "env", "NODE_ENV", "selectPath", "chalk", "bold", "cyan", "join", "AbortCommandError", "lt"], "mappings": ";;;;;;;;;;;IA+GaA,iBAAiB;eAAjBA;;IAhFAC,oBAAoB;eAApBA;;;;gEA/BU;;;;;;;gEACL;;;;;;;yBACO;;;;;;;gEACN;;;;;;6DAEE;wBACa;yBACV;yBACK;8BACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,4DAA4D;AAC5D,uEAAuE;AACvE,MAAMC,oBAAoB;AAC1B,MAAMC,eAAe;AAErB,MAAMC,0BAA0B,GAAGF,kBAAkB,EAAE,CAAC;AAExD,MAAMG,4BAA4B,OAAOC;IACvC,0KAA0K;IAC1K,MAAMC,UAAU,MAAMC,IAAAA,qBAAY,EAAC;QAAEC,SAAS;QAAMH;IAAQ;IAC5D,IAAIC,SAAS;QACXG,KAAIC,GAAG,CAAC,CAAC,2EAA2E,CAAC;QACrFC,aAAaT;IACf;AACF;AAEA,IAAIU,uBAAyF;AAEtF,MAAMd,uBAAuB,OAAO,EACzCe,MAAM,EACNC,KAAK,EACiC,GAAG,CAAC,CAAC;IAC3C,MAAMC,WAAWF,SAASd,QAAQU,KAAIO,IAAI;IAC1C,MAAMC,aAAa;QACjB,IAAI;gBAEWC;YADb,MAAM,EAAEA,MAAM,EAAE,GAAG,MAAMC,IAAAA,qBAAU,EAAC,cAAc;gBAAC;aAAW;YAC9D,MAAMC,QAAOF,gBAAAA,OAAOG,KAAK,CAAC,yCAAbH,aAAmC,CAAC,EAAE;YACnD,6BAA6B;YAC7B,IAAIE,MAAM;gBACR,MAAME,UAAU,GAAGF,KAAK,EAAE,CAAC;gBAE3B,IAAI,CAACG,iBAAM,CAACC,KAAK,CAACF,UAAU;oBAC1B,2FAA2F;oBAC3F,OAAO;wBAAEG,OAAO,CAAC,uCAAuC,EAAEH,SAAS;wBAAEI,OAAO;oBAAM;gBACpF;gBACA,OAAO;oBAAEA,OAAOJ;gBAAQ;YAC1B;YAEA,2BAA2B;YAC3B,OAAO;gBACLG,OACE;gBACFC,OAAO;YACT;QACF,EAAE,OAAM;QACN,gBAAgB;QAClB;QACA,OAAO;YAAEA,OAAO;QAAK;IACvB;IAEA,IAAIZ,OAAO;QACTF,uBAAuB;IACzB;IAEAA,uBAAuBA,wBAAwBK;IAE/C,MAAMU,SAAS,MAAMf;IAErB,IAAIe,OAAOF,KAAK,EAAE;QAChBV,SAASY,OAAOF,KAAK;IACvB;IAEA,OAAOE,OAAOD,KAAK;AACrB;AAEA;;;;CAIC,GACD,SAASf,aAAaiB,KAAa;IACjC,MAAMC,OAAOC,gBAAgBF;IAC7BG,IAAAA,yBAAQ,EAAC,CAAC,KAAK,EAAEF,MAAM,EAAE;QAAEG,OAAO;IAAS;AAC7C;AAEA,SAASF,gBAAgBF,KAAa;IACpC,IAAIK,QAAQC,QAAQ,KAAK,UAAU;QACjC,2EAA2E;QAC3E,OAAO,CAAC,qCAAqC,EAAEN,OAAO;IACxD;IACA,OAAO,CAAC,gCAAgC,EAAEA,OAAO;AACnD;AAEA,SAASO,eAAeC,GAAW;IACjC,IAAI;QACF,OAAOL,IAAAA,yBAAQ,EAACK,KAAK;YAAEJ,OAAO;QAAO,GAAGK,QAAQ,GAAGC,IAAI;IACzD,EAAE,OAAM,CAAC;IACT,OAAO;AACT;AAEA,kHAAkH,GAClH,SAASC;IACP,OAAOJ,eAAe;AACxB;AACA,SAASK;IACP,OAAOL,eAAe;AACxB;AAEO,MAAMtC,0BAA0B4C,0BAAY;qBAC1CC,WAAW,IAAI7C;IAEtB;;GAEC,GACD,MAAM8C,uBAAsC;QAC1C,MAAMrB,UAAU,MAAMsB,IAAAA,gBAAO,EAAC9C,sBAAsB;YAAEgB,OAAOmB,QAAQY,GAAG,CAACC,QAAQ,KAAK;QAAO;QAC7F/C,MAAM,CAAC,eAAe,EAAEuB,SAAS;QACjC,IAAI,CAACA,SAAS;YACZ,0GAA0G;YAC1G,uDAAuD;YAEvD,oEAAoE;YACpE,IAAIkB,qBAAqB;gBACvB,MAAMO,aAAaH,IAAAA,gBAAO,EAACL;gBAC3BxC,MAAM,CAAC,mBAAmB,EAAEgD,YAAY;gBACxC,IAAI,CAACA,YAAY;oBACftC,KAAIgB,KAAK,CACP;wBACE;wBACAuB,gBAAK,CAACC,IAAI,CAAC;wBACX;wBACA;wBACA;wBACA;wBACAD,gBAAK,CAACE,IAAI,CAAC;wBACXF,gBAAK,CAACE,IAAI,CAAC;wBACX;wBACA;wBACA;qBACD,CAACC,IAAI,CAAC;oBAET,MAAM,IAAIC,yBAAiB;gBAC7B,OAAO;oBACLrD,MAAM,CAAC,iCAAiC,EAAEuB,QAAQ,UAAU,EAAEyB,WAAW,CAAC,CAAC;gBAC7E;YACF;YAEA,0CAA0C;YAC1C,MAAM3C,0BACJ,CAAC,4RAA4R,CAAC;YAEhS,MAAM,IAAIgD,yBAAiB;QAC7B;QAEA,IAAI7B,iBAAM,CAAC8B,EAAE,CAAC/B,SAASnB,0BAA0B;YAC/C,4BAA4B;YAC5B,MAAMC,0BACJ,CAAC,OAAO,EAAEkB,QAAQ,0CAA0C,EAAErB,kBAAkB,4BAA4B,CAAC;YAE/G,MAAM,IAAImD,yBAAiB;QAC7B;IACF;AACF"}