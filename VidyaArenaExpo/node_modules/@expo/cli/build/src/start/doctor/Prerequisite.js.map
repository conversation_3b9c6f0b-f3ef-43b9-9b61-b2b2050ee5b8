{"version": 3, "sources": ["../../../../src/start/doctor/Prerequisite.ts"], "sourcesContent": ["import { CommandError, UnimplementedError } from '../../utils/errors';\nimport { memoize } from '../../utils/fn';\n\n/** An error that is memoized and asserted whenever a Prerequisite.assertAsync is subsequently called. */\nexport class PrerequisiteCommandError extends CommandError {\n  constructor(code: string, message: string = '') {\n    super(message ? 'VALIDATE_' + code : code, message);\n  }\n}\n\nexport class Prerequisite<T = void, TProps = void> {\n  /** Memoized results of `assertImplementation` */\n  private _assertAsync: (props: TProps) => Promise<T>;\n\n  constructor() {\n    this._assertAsync = memoize(this.assertImplementation.bind(this));\n  }\n\n  /** An optional warning to call before running the memoized assertion.  */\n  protected cachedError?: PrerequisiteCommandError;\n\n  /** Reset the assertion memo and warning message. */\n  public resetAssertion(props: TProps) {\n    this.cachedError = undefined;\n    this._assertAsync = memoize(this.assertImplementation.bind(this));\n  }\n\n  async assertAsync(props: TProps): Promise<T> {\n    if (this.cachedError) {\n      throw this.cachedError;\n    }\n    try {\n      return await this._assertAsync(props);\n    } catch (error) {\n      if (error instanceof PrerequisiteCommandError) {\n        this.cachedError = error;\n      }\n      throw error;\n    }\n  }\n\n  /** Exposed for testing. */\n  async assertImplementation(props: TProps): Promise<T> {\n    throw new UnimplementedError();\n  }\n}\n\n/** A prerequisite that is project specific. */\nexport class ProjectPrerequisite<T = void, TProps = void> extends Prerequisite<T, TProps> {\n  constructor(protected projectRoot: string) {\n    super();\n  }\n}\n"], "names": ["Prerequisite", "PrerequisiteCommandError", "ProjectPrerequisite", "CommandError", "constructor", "code", "message", "_assertAsync", "memoize", "assertImplementation", "bind", "resetAssertion", "props", "cachedError", "undefined", "assertAsync", "error", "UnimplementedError", "projectRoot"], "mappings": ";;;;;;;;;;;IAUaA,YAAY;eAAZA;;IANAC,wBAAwB;eAAxBA;;IA4CAC,mBAAmB;eAAnBA;;;wBAhDoC;oBACzB;AAGjB,MAAMD,iCAAiCE,oBAAY;IACxDC,YAAYC,IAAY,EAAEC,UAAkB,EAAE,CAAE;QAC9C,KAAK,CAACA,UAAU,cAAcD,OAAOA,MAAMC;IAC7C;AACF;AAEO,MAAMN;IAIXI,aAAc;QACZ,IAAI,CAACG,YAAY,GAAGC,IAAAA,WAAO,EAAC,IAAI,CAACC,oBAAoB,CAACC,IAAI,CAAC,IAAI;IACjE;IAKA,kDAAkD,GAClD,AAAOC,eAAeC,KAAa,EAAE;QACnC,IAAI,CAACC,WAAW,GAAGC;QACnB,IAAI,CAACP,YAAY,GAAGC,IAAAA,WAAO,EAAC,IAAI,CAACC,oBAAoB,CAACC,IAAI,CAAC,IAAI;IACjE;IAEA,MAAMK,YAAYH,KAAa,EAAc;QAC3C,IAAI,IAAI,CAACC,WAAW,EAAE;YACpB,MAAM,IAAI,CAACA,WAAW;QACxB;QACA,IAAI;YACF,OAAO,MAAM,IAAI,CAACN,YAAY,CAACK;QACjC,EAAE,OAAOI,OAAO;YACd,IAAIA,iBAAiBf,0BAA0B;gBAC7C,IAAI,CAACY,WAAW,GAAGG;YACrB;YACA,MAAMA;QACR;IACF;IAEA,yBAAyB,GACzB,MAAMP,qBAAqBG,KAAa,EAAc;QACpD,MAAM,IAAIK,0BAAkB;IAC9B;AACF;AAGO,MAAMf,4BAAqDF;IAChEI,YAAY,AAAUc,WAAmB,CAAE;QACzC,KAAK,SADeA,cAAAA;IAEtB;AACF"}