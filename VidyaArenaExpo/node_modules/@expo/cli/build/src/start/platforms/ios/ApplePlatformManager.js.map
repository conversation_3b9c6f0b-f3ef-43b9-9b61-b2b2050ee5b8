{"version": 3, "sources": ["../../../../../src/start/platforms/ios/ApplePlatformManager.ts"], "sourcesContent": ["import { AppleAppIdResolver } from './AppleAppIdResolver';\nimport { AppleDeviceManager } from './AppleDeviceManager';\nimport { Device } from './simctl';\nimport { AppIdResolver } from '../AppIdResolver';\nimport { BaseOpenInCustomProps, PlatformManager } from '../PlatformManager';\n\n/** Manages launching apps on Apple simulators. */\nexport class ApplePlatformManager extends PlatformManager<Device> {\n  constructor(\n    protected projectRoot: string,\n    protected port: number,\n    options: {\n      /** Get the base URL for the dev server hosting this platform manager. */\n      getDevServerUrl: () => string | null;\n      /** Expo Go URL. */\n      getExpoGoUrl: () => string;\n      /** Get redirect URL for native disambiguation. */\n      getRedirectUrl: () => string | null;\n      /** Dev Client */\n      getCustomRuntimeUrl: (props?: { scheme?: string }) => string | null;\n    }\n  ) {\n    super(projectRoot, {\n      platform: 'ios',\n      ...options,\n      resolveDeviceAsync: AppleDeviceManager.resolveAsync,\n    });\n  }\n\n  async openAsync(\n    options:\n      | { runtime: 'expo' | 'web' }\n      | { runtime: 'custom'; props?: Partial<BaseOpenInCustomProps> },\n    resolveSettings?: Partial<{ shouldPrompt?: boolean; device?: Device }>\n  ): Promise<{ url: string }> {\n    await AppleDeviceManager.assertSystemRequirementsAsync();\n    return super.openAsync(options, resolveSettings);\n  }\n\n  _getAppIdResolver(): AppIdResolver {\n    return new AppleAppIdResolver(this.projectRoot);\n  }\n\n  _resolveAlternativeLaunchUrl(\n    applicationId: string,\n    props?: Partial<BaseOpenInCustomProps>\n  ): string {\n    return applicationId;\n  }\n}\n"], "names": ["ApplePlatformManager", "PlatformManager", "constructor", "projectRoot", "port", "options", "platform", "resolveDeviceAsync", "AppleDeviceManager", "resolveAsync", "openAsync", "resolveSettings", "assertSystemRequirementsAsync", "_getAppIdResolver", "AppleAppIdResolver", "_resolveAlternativeLaunchUrl", "applicationId", "props"], "mappings": ";;;;+BAOaA;;;eAAAA;;;oCAPsB;oCACA;iCAGoB;AAGhD,MAAMA,6BAA6BC,gCAAe;IACvDC,YACE,AAAUC,WAAmB,EAC7B,AAAUC,IAAY,EACtBC,OASC,CACD;QACA,KAAK,CAACF,aAAa;YACjBG,UAAU;YACV,GAAGD,OAAO;YACVE,oBAAoBC,sCAAkB,CAACC,YAAY;QACrD,SAjBUN,cAAAA,kBACAC,OAAAA;IAiBZ;IAEA,MAAMM,UACJL,OAEiE,EACjEM,eAAsE,EAC5C;QAC1B,MAAMH,sCAAkB,CAACI,6BAA6B;QACtD,OAAO,KAAK,CAACF,UAAUL,SAASM;IAClC;IAEAE,oBAAmC;QACjC,OAAO,IAAIC,sCAAkB,CAAC,IAAI,CAACX,WAAW;IAChD;IAEAY,6BACEC,aAAqB,EACrBC,KAAsC,EAC9B;QACR,OAAOD;IACT;AACF"}