{"version": 3, "sources": ["../../../../../src/start/platforms/ios/xcrun.ts"], "sourcesContent": ["import spawnAsync, { SpawnOptions, SpawnResult } from '@expo/spawn-async';\nimport chalk from 'chalk';\n\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')('expo:start:platforms:ios:xcrun') as typeof console.log;\n\nexport function isSpawnResultError(obj: any): obj is Error & SpawnResult {\n  return (\n    obj &&\n    'message' in obj &&\n    obj.status !== undefined &&\n    obj.stdout !== undefined &&\n    obj.stderr !== undefined\n  );\n}\n\nexport async function xcrunAsync(args: (string | undefined)[], options?: SpawnOptions) {\n  debug('Running: xcrun ' + args.join(' '));\n  try {\n    return await spawnAsync('xcrun', args.filter(Boolean) as string[], options);\n  } catch (e) {\n    throwXcrunError(e);\n  }\n}\n\nfunction throwXcrunError(e: any): never {\n  if (isLicenseOutOfDate(e.stdout) || isLicenseOutOfDate(e.stderr)) {\n    throw new CommandError(\n      'XCODE_LICENSE_NOT_ACCEPTED',\n      'Xcode license is not accepted. Run `sudo xcodebuild -license`.'\n    );\n  } else if (e.stderr?.includes('not a developer tool or in PATH')) {\n    throw new CommandError(\n      'SIMCTL_NOT_AVAILABLE',\n      `You may need to run ${chalk.bold(\n        'sudo xcode-select -s /Applications/Xcode.app'\n      )} and try again.`\n    );\n  }\n  // Attempt to craft a better error message...\n  if (Array.isArray(e.output)) {\n    e.message += '\\n' + e.output.join('\\n').trim();\n  } else if (e.stderr) {\n    e.message += '\\n' + e.stderr;\n  }\n  throw e;\n}\n\nfunction isLicenseOutOfDate(text: string) {\n  if (!text) {\n    return false;\n  }\n\n  const lower = text.toLowerCase();\n  return lower.includes('xcode') && lower.includes('license');\n}\n"], "names": ["isSpawnResultError", "xcrunAsync", "debug", "require", "obj", "status", "undefined", "stdout", "stderr", "args", "options", "join", "spawnAsync", "filter", "Boolean", "e", "throwXcrunError", "isLicenseOutOfDate", "CommandError", "includes", "chalk", "bold", "Array", "isArray", "output", "message", "trim", "text", "lower", "toLowerCase"], "mappings": ";;;;;;;;;;;IAOgBA,kBAAkB;eAAlBA;;IAUMC,UAAU;eAAVA;;;;gEAjBgC;;;;;;;gEACpC;;;;;;wBAEW;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SAAS;AAExB,SAASH,mBAAmBI,GAAQ;IACzC,OACEA,OACA,aAAaA,OACbA,IAAIC,MAAM,KAAKC,aACfF,IAAIG,MAAM,KAAKD,aACfF,IAAII,MAAM,KAAKF;AAEnB;AAEO,eAAeL,WAAWQ,IAA4B,EAAEC,OAAsB;IACnFR,MAAM,oBAAoBO,KAAKE,IAAI,CAAC;IACpC,IAAI;QACF,OAAO,MAAMC,IAAAA,qBAAU,EAAC,SAASH,KAAKI,MAAM,CAACC,UAAsBJ;IACrE,EAAE,OAAOK,GAAG;QACVC,gBAAgBD;IAClB;AACF;AAEA,SAASC,gBAAgBD,CAAM;QAMlBA;IALX,IAAIE,mBAAmBF,EAAER,MAAM,KAAKU,mBAAmBF,EAAEP,MAAM,GAAG;QAChE,MAAM,IAAIU,oBAAY,CACpB,8BACA;IAEJ,OAAO,KAAIH,YAAAA,EAAEP,MAAM,qBAARO,UAAUI,QAAQ,CAAC,oCAAoC;QAChE,MAAM,IAAID,oBAAY,CACpB,wBACA,CAAC,oBAAoB,EAAEE,gBAAK,CAACC,IAAI,CAC/B,gDACA,eAAe,CAAC;IAEtB;IACA,6CAA6C;IAC7C,IAAIC,MAAMC,OAAO,CAACR,EAAES,MAAM,GAAG;QAC3BT,EAAEU,OAAO,IAAI,OAAOV,EAAES,MAAM,CAACb,IAAI,CAAC,MAAMe,IAAI;IAC9C,OAAO,IAAIX,EAAEP,MAAM,EAAE;QACnBO,EAAEU,OAAO,IAAI,OAAOV,EAAEP,MAAM;IAC9B;IACA,MAAMO;AACR;AAEA,SAASE,mBAAmBU,IAAY;IACtC,IAAI,CAACA,MAAM;QACT,OAAO;IACT;IAEA,MAAMC,QAAQD,KAAKE,WAAW;IAC9B,OAAOD,MAAMT,QAAQ,CAAC,YAAYS,MAAMT,QAAQ,CAAC;AACnD"}