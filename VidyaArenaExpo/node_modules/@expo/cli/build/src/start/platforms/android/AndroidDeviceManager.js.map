{"version": 3, "sources": ["../../../../../src/start/platforms/android/AndroidDeviceManager.ts"], "sourcesContent": ["import assert from 'assert';\nimport chalk from 'chalk';\n\nimport { activateWindowAsync } from './activateWindow';\nimport * as AndroidDebugBridge from './adb';\nimport { startDeviceAsync } from './emulator';\nimport { getDevicesAsync } from './getDevices';\nimport { promptForDeviceAsync } from './promptAndroidDevice';\nimport * as Log from '../../../log';\nimport { AbortCommandError, CommandError } from '../../../utils/errors';\nimport { validateUrl } from '../../../utils/url';\nimport { DeviceManager } from '../DeviceManager';\nimport { ExpoGoInstaller } from '../ExpoGoInstaller';\nimport { BaseResolveDeviceProps } from '../PlatformManager';\n\nconst EXPO_GO_APPLICATION_IDENTIFIER = 'host.exp.exponent';\n\nexport class AndroidDeviceManager extends DeviceManager<AndroidDebugBridge.Device> {\n  static async resolveFromNameAsync(name: string): Promise<AndroidDeviceManager> {\n    const devices = await getDevicesAsync();\n    const device = devices.find((device) => device.name === name);\n\n    if (!device) {\n      throw new CommandError('Could not find device with name: ' + name);\n    }\n    return AndroidDeviceManager.resolveAsync({ device, shouldPrompt: false });\n  }\n\n  static async resolveAsync({\n    device,\n    shouldPrompt,\n  }: BaseResolveDeviceProps<AndroidDebugBridge.Device> = {}): Promise<AndroidDeviceManager> {\n    if (device) {\n      const manager = new AndroidDeviceManager(device);\n      if (!(await manager.attemptToStartAsync())) {\n        throw new AbortCommandError();\n      }\n      return manager;\n    }\n\n    const devices = await getDevicesAsync();\n    const _device = shouldPrompt ? await promptForDeviceAsync(devices) : devices[0];\n    return AndroidDeviceManager.resolveAsync({ device: _device, shouldPrompt: false });\n  }\n\n  get name() {\n    // TODO: Maybe strip `_` from the device name?\n    return this.device.name;\n  }\n\n  get identifier(): string {\n    return this.device.pid ?? 'unknown';\n  }\n\n  async getAppVersionAsync(applicationId: string): Promise<string | null> {\n    const info = await AndroidDebugBridge.getPackageInfoAsync(this.device, {\n      appId: applicationId,\n    });\n\n    const regex = /versionName=([0-9.]+)/;\n    return regex.exec(info)?.[1] ?? null;\n  }\n\n  protected async attemptToStartAsync(): Promise<AndroidDebugBridge.Device | null> {\n    // TODO: Add a light-weight method for checking since a device could disconnect.\n    if (!(await AndroidDebugBridge.isDeviceBootedAsync(this.device))) {\n      this.device = await startDeviceAsync(this.device);\n    }\n\n    if (this.device.isAuthorized === false) {\n      AndroidDebugBridge.logUnauthorized(this.device);\n      return null;\n    }\n\n    return this.device;\n  }\n\n  async startAsync(): Promise<AndroidDebugBridge.Device> {\n    const device = await this.attemptToStartAsync();\n    assert(device, `Failed to boot emulator.`);\n    return this.device;\n  }\n\n  async installAppAsync(binaryPath: string) {\n    await AndroidDebugBridge.installAsync(this.device, {\n      filePath: binaryPath,\n    });\n  }\n\n  async uninstallAppAsync(appId: string) {\n    // we need to check if the app is installed, else we might bump into \"Failure [DELETE_FAILED_INTERNAL_ERROR]\"\n    const isInstalled = await this.isAppInstalledAndIfSoReturnContainerPathForIOSAsync(appId);\n    if (!isInstalled) {\n      return;\n    }\n\n    try {\n      await AndroidDebugBridge.uninstallAsync(this.device, {\n        appId,\n      });\n    } catch (e) {\n      Log.error(\n        `Could not uninstall app \"${appId}\" from your device, please uninstall it manually and try again.`\n      );\n      throw e;\n    }\n  }\n\n  /**\n   * @param launchActivity Activity to launch `[application identifier]/.[main activity name]`, ex: `com.bacon.app/.MainActivity`\n   */\n  async launchActivityAsync(launchActivity: string, url?: string): Promise<string> {\n    try {\n      return await AndroidDebugBridge.launchActivityAsync(this.device, {\n        launchActivity,\n        url,\n      });\n    } catch (error: any) {\n      let errorMessage = `Couldn't open Android app with activity \"${launchActivity}\" on device \"${this.name}\".`;\n      if (error instanceof CommandError && error.code === 'APP_NOT_INSTALLED') {\n        errorMessage += `\\nThe app might not be installed, try installing it with: ${chalk.bold(\n          `npx expo run:android -d ${this.name}`\n        )}`;\n      }\n      errorMessage += chalk.gray(`\\n${error.message}`);\n      error.message = errorMessage;\n      throw error;\n    }\n  }\n\n  async isAppInstalledAndIfSoReturnContainerPathForIOSAsync(applicationId: string) {\n    return await AndroidDebugBridge.isPackageInstalledAsync(this.device, applicationId);\n  }\n\n  async openUrlAsync(url: string) {\n    // Non-compliant URLs will be treated as application identifiers.\n    if (!validateUrl(url, { requireProtocol: true })) {\n      await this.launchActivityAsync(url);\n      return;\n    }\n\n    const parsed = new URL(url);\n\n    if (parsed.protocol === 'exp:') {\n      // NOTE(brentvatne): temporary workaround! launch Expo Go first, then\n      // launch the project!\n      // https://github.com/expo/expo/issues/7772\n      // adb shell monkey -p host.exp.exponent -c android.intent.category.LAUNCHER 1\n      // Note: this is not needed in Expo Development Client, it only applies to Expo Go\n      await AndroidDebugBridge.openAppIdAsync(\n        { pid: this.device.pid },\n        { applicationId: EXPO_GO_APPLICATION_IDENTIFIER }\n      );\n    }\n\n    await AndroidDebugBridge.openUrlAsync({ pid: this.device.pid }, { url });\n  }\n\n  async activateWindowAsync() {\n    // Bring the emulator window to the front on macos devices.\n    await activateWindowAsync(this.device);\n  }\n\n  getExpoGoAppId(): string {\n    return EXPO_GO_APPLICATION_IDENTIFIER;\n  }\n\n  async ensureExpoGoAsync(sdkVersion: string): Promise<boolean> {\n    const installer = new ExpoGoInstaller('android', EXPO_GO_APPLICATION_IDENTIFIER, sdkVersion);\n    return installer.ensureAsync(this);\n  }\n}\n"], "names": ["AndroidDeviceManager", "EXPO_GO_APPLICATION_IDENTIFIER", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resolveFromNameAsync", "name", "devices", "getDevicesAsync", "device", "find", "CommandError", "resolveAsync", "should<PERSON>rompt", "manager", "attemptToStartAsync", "AbortCommandError", "_device", "promptForDeviceAsync", "identifier", "pid", "getAppVersionAsync", "applicationId", "regex", "info", "AndroidDebugBridge", "getPackageInfoAsync", "appId", "exec", "isDeviceBootedAsync", "startDeviceAsync", "isAuthorized", "logUnauthorized", "startAsync", "assert", "installAppAsync", "binaryPath", "installAsync", "filePath", "uninstallAppAsync", "isInstalled", "isAppInstalledAndIfSoReturnContainerPathForIOSAsync", "uninstallAsync", "e", "Log", "error", "launchActivityAsync", "launchActivity", "url", "errorMessage", "code", "chalk", "bold", "gray", "message", "isPackageInstalledAsync", "openUrlAsync", "validateUrl", "requireProtocol", "parsed", "URL", "protocol", "openAppIdAsync", "activateWindowAsync", "getExpoGoAppId", "ensureExpoGoAsync", "sdkVersion", "installer", "ExpoGoInstaller", "ensureAsync"], "mappings": ";;;;+BAiBaA;;;eAAAA;;;;gEAjBM;;;;;;;gEACD;;;;;;gCAEkB;6DACA;0BACH;4BACD;qCACK;6DAChB;wBAC2B;qBACpB;+BACE;iCACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGhC,MAAMC,iCAAiC;AAEhC,MAAMD,6BAA6BE,4BAAa;IACrD,aAAaC,qBAAqBC,IAAY,EAAiC;QAC7E,MAAMC,UAAU,MAAMC,IAAAA,2BAAe;QACrC,MAAMC,SAASF,QAAQG,IAAI,CAAC,CAACD,SAAWA,OAAOH,IAAI,KAAKA;QAExD,IAAI,CAACG,QAAQ;YACX,MAAM,IAAIE,oBAAY,CAAC,sCAAsCL;QAC/D;QACA,OAAOJ,qBAAqBU,YAAY,CAAC;YAAEH;YAAQI,cAAc;QAAM;IACzE;IAEA,aAAaD,aAAa,EACxBH,MAAM,EACNI,YAAY,EACsC,GAAG,CAAC,CAAC,EAAiC;QACxF,IAAIJ,QAAQ;YACV,MAAMK,UAAU,IAAIZ,qBAAqBO;YACzC,IAAI,CAAE,MAAMK,QAAQC,mBAAmB,IAAK;gBAC1C,MAAM,IAAIC,yBAAiB;YAC7B;YACA,OAAOF;QACT;QAEA,MAAMP,UAAU,MAAMC,IAAAA,2BAAe;QACrC,MAAMS,UAAUJ,eAAe,MAAMK,IAAAA,yCAAoB,EAACX,WAAWA,OAAO,CAAC,EAAE;QAC/E,OAAOL,qBAAqBU,YAAY,CAAC;YAAEH,QAAQQ;YAASJ,cAAc;QAAM;IAClF;IAEA,IAAIP,OAAO;QACT,8CAA8C;QAC9C,OAAO,IAAI,CAACG,MAAM,CAACH,IAAI;IACzB;IAEA,IAAIa,aAAqB;QACvB,OAAO,IAAI,CAACV,MAAM,CAACW,GAAG,IAAI;IAC5B;IAEA,MAAMC,mBAAmBC,aAAqB,EAA0B;YAM/DC;QALP,MAAMC,OAAO,MAAMC,KAAmBC,mBAAmB,CAAC,IAAI,CAACjB,MAAM,EAAE;YACrEkB,OAAOL;QACT;QAEA,MAAMC,QAAQ;QACd,OAAOA,EAAAA,cAAAA,MAAMK,IAAI,CAACJ,0BAAXD,WAAkB,CAAC,EAAE,KAAI;IAClC;IAEA,MAAgBR,sBAAiE;QAC/E,gFAAgF;QAChF,IAAI,CAAE,MAAMU,KAAmBI,mBAAmB,CAAC,IAAI,CAACpB,MAAM,GAAI;YAChE,IAAI,CAACA,MAAM,GAAG,MAAMqB,IAAAA,0BAAgB,EAAC,IAAI,CAACrB,MAAM;QAClD;QAEA,IAAI,IAAI,CAACA,MAAM,CAACsB,YAAY,KAAK,OAAO;YACtCN,KAAmBO,eAAe,CAAC,IAAI,CAACvB,MAAM;YAC9C,OAAO;QACT;QAEA,OAAO,IAAI,CAACA,MAAM;IACpB;IAEA,MAAMwB,aAAiD;QACrD,MAAMxB,SAAS,MAAM,IAAI,CAACM,mBAAmB;QAC7CmB,IAAAA,iBAAM,EAACzB,QAAQ,CAAC,wBAAwB,CAAC;QACzC,OAAO,IAAI,CAACA,MAAM;IACpB;IAEA,MAAM0B,gBAAgBC,UAAkB,EAAE;QACxC,MAAMX,KAAmBY,YAAY,CAAC,IAAI,CAAC5B,MAAM,EAAE;YACjD6B,UAAUF;QACZ;IACF;IAEA,MAAMG,kBAAkBZ,KAAa,EAAE;QACrC,6GAA6G;QAC7G,MAAMa,cAAc,MAAM,IAAI,CAACC,mDAAmD,CAACd;QACnF,IAAI,CAACa,aAAa;YAChB;QACF;QAEA,IAAI;YACF,MAAMf,KAAmBiB,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;gBACnDkB;YACF;QACF,EAAE,OAAOgB,GAAG;YACVC,KAAIC,KAAK,CACP,CAAC,yBAAyB,EAAElB,MAAM,+DAA+D,CAAC;YAEpG,MAAMgB;QACR;IACF;IAEA;;GAEC,GACD,MAAMG,oBAAoBC,cAAsB,EAAEC,GAAY,EAAmB;QAC/E,IAAI;YACF,OAAO,MAAMvB,KAAmBqB,mBAAmB,CAAC,IAAI,CAACrC,MAAM,EAAE;gBAC/DsC;gBACAC;YACF;QACF,EAAE,OAAOH,OAAY;YACnB,IAAII,eAAe,CAAC,yCAAyC,EAAEF,eAAe,aAAa,EAAE,IAAI,CAACzC,IAAI,CAAC,EAAE,CAAC;YAC1G,IAAIuC,iBAAiBlC,oBAAY,IAAIkC,MAAMK,IAAI,KAAK,qBAAqB;gBACvED,gBAAgB,CAAC,0DAA0D,EAAEE,gBAAK,CAACC,IAAI,CACrF,CAAC,wBAAwB,EAAE,IAAI,CAAC9C,IAAI,EAAE,GACrC;YACL;YACA2C,gBAAgBE,gBAAK,CAACE,IAAI,CAAC,CAAC,EAAE,EAAER,MAAMS,OAAO,EAAE;YAC/CT,MAAMS,OAAO,GAAGL;YAChB,MAAMJ;QACR;IACF;IAEA,MAAMJ,oDAAoDnB,aAAqB,EAAE;QAC/E,OAAO,MAAMG,KAAmB8B,uBAAuB,CAAC,IAAI,CAAC9C,MAAM,EAAEa;IACvE;IAEA,MAAMkC,aAAaR,GAAW,EAAE;QAC9B,iEAAiE;QACjE,IAAI,CAACS,IAAAA,gBAAW,EAACT,KAAK;YAAEU,iBAAiB;QAAK,IAAI;YAChD,MAAM,IAAI,CAACZ,mBAAmB,CAACE;YAC/B;QACF;QAEA,MAAMW,SAAS,IAAIC,IAAIZ;QAEvB,IAAIW,OAAOE,QAAQ,KAAK,QAAQ;YAC9B,qEAAqE;YACrE,sBAAsB;YACtB,2CAA2C;YAC3C,8EAA8E;YAC9E,kFAAkF;YAClF,MAAMpC,KAAmBqC,cAAc,CACrC;gBAAE1C,KAAK,IAAI,CAACX,MAAM,CAACW,GAAG;YAAC,GACvB;gBAAEE,eAAenB;YAA+B;QAEpD;QAEA,MAAMsB,KAAmB+B,YAAY,CAAC;YAAEpC,KAAK,IAAI,CAACX,MAAM,CAACW,GAAG;QAAC,GAAG;YAAE4B;QAAI;IACxE;IAEA,MAAMe,sBAAsB;QAC1B,2DAA2D;QAC3D,MAAMA,IAAAA,mCAAmB,EAAC,IAAI,CAACtD,MAAM;IACvC;IAEAuD,iBAAyB;QACvB,OAAO7D;IACT;IAEA,MAAM8D,kBAAkBC,UAAkB,EAAoB;QAC5D,MAAMC,YAAY,IAAIC,gCAAe,CAAC,WAAWjE,gCAAgC+D;QACjF,OAAOC,UAAUE,WAAW,CAAC,IAAI;IACnC;AACF"}