{"version": 3, "sources": ["../../../../src/start/platforms/PlatformManager.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport assert from 'assert';\nimport chalk from 'chalk';\n\nimport { AppIdResolver } from './AppIdResolver';\nimport { DeviceManager } from './DeviceManager';\nimport { Log } from '../../log';\nimport { CommandError, UnimplementedError } from '../../utils/errors';\nimport { learnMore } from '../../utils/link';\n\nconst debug = require('debug')('expo:start:platforms:platformManager') as typeof console.log;\n\nexport interface BaseOpenInCustomProps {\n  scheme?: string;\n  applicationId?: string | null;\n}\n\nexport interface BaseResolveDeviceProps<IDevice> {\n  /** Should prompt the user to select a device. */\n  shouldPrompt?: boolean;\n  /** The target device to use. */\n  device?: IDevice;\n}\n\n/** An abstract class for launching a URL on a device. */\nexport class PlatformManager<\n  IDevice,\n  IOpenInCustomProps extends BaseOpenInCustomProps = BaseOpenInCustomProps,\n  IResolveDeviceProps extends BaseResolveDeviceProps<IDevice> = BaseResolveDeviceProps<IDevice>,\n> {\n  constructor(\n    protected projectRoot: string,\n    protected props: {\n      platform: 'ios' | 'android';\n      /** Get the base URL for the dev server hosting this platform manager. */\n      getDevServerUrl: () => string | null;\n      /** Expo Go URL. */\n      getExpoGoUrl: () => string;\n      /**\n       * Get redirect URL for native disambiguation.\n       * @returns a URL like `http://localhost:8081/_expo/loading`\n       */\n      getRedirectUrl: () => string | null;\n      /** Dev Client */\n      getCustomRuntimeUrl: (props?: { scheme?: string }) => string | null;\n      /** Resolve a device, this function should automatically handle opening the device and asserting any system validations. */\n      resolveDeviceAsync: (\n        resolver?: Partial<IResolveDeviceProps>\n      ) => Promise<DeviceManager<IDevice>>;\n    }\n  ) {}\n\n  /** Returns the project application identifier or asserts that one is not defined. Exposed for testing. */\n  _getAppIdResolver(): AppIdResolver {\n    throw new UnimplementedError();\n  }\n\n  /**\n   * Get the URL for users intending to launch the project in Expo Go.\n   * The CLI will check if the project has a custom dev client and if the redirect page feature is enabled.\n   * If both are true, the CLI will return the redirect page URL.\n   */\n  protected async getExpoGoOrCustomRuntimeUrlAsync(\n    deviceManager: DeviceManager<IDevice>\n  ): Promise<string> {\n    // Determine if the redirect page feature is enabled first since it's the cheapest to check.\n    const redirectUrl = this.props.getRedirectUrl();\n    if (redirectUrl) {\n      // If the redirect page feature is enabled, check if the project has a resolvable native identifier.\n      let applicationId;\n      try {\n        applicationId = await this._getAppIdResolver().getAppIdAsync();\n      } catch {\n        Log.warn(\n          chalk`\\u203A Launching in Expo Go. If you want to use a ` +\n            `development build, you need to create and install one first, or, if you already ` +\n            chalk`have a build, add {bold ios.bundleIdentifier} and {bold android.package} to ` +\n            `this project's app config.\\n${learnMore('https://docs.expo.dev/development/build/')}`\n        );\n      }\n      if (applicationId) {\n        debug(`Resolving launch URL: (appId: ${applicationId}, redirect URL: ${redirectUrl})`);\n        // NOTE(EvanBacon): This adds considerable amount of time to the command, we should consider removing or memoizing it.\n        // Finally determine if the target device has a custom dev client installed.\n        if (\n          await deviceManager.isAppInstalledAndIfSoReturnContainerPathForIOSAsync(applicationId)\n        ) {\n          return redirectUrl;\n        } else {\n          // Log a warning if no development build is available on the device, but the\n          // interstitial page would otherwise be opened.\n          Log.warn(\n            chalk`\\u203A The {bold expo-dev-client} package is installed, but a development build is not ` +\n              chalk`installed on {bold ${deviceManager.name}}.\\nLaunching in Expo Go. If you want to use a ` +\n              `development build, you need to create and install one first.\\n${learnMore(\n                'https://docs.expo.dev/development/build/'\n              )}`\n          );\n        }\n      }\n    }\n\n    return this.props.getExpoGoUrl();\n  }\n\n  protected async openProjectInExpoGoAsync(\n    resolveSettings: Partial<IResolveDeviceProps> = {}\n  ): Promise<{ url: string }> {\n    const deviceManager = await this.props.resolveDeviceAsync(resolveSettings);\n    const url = await this.getExpoGoOrCustomRuntimeUrlAsync(deviceManager);\n\n    deviceManager.logOpeningUrl(url);\n\n    // TODO: Expensive, we should only do this once.\n    const { exp } = getConfig(this.projectRoot);\n    const sdkVersion = exp.sdkVersion;\n    assert(sdkVersion, 'sdkVersion should be resolved by getConfig');\n    await deviceManager.ensureExpoGoAsync(sdkVersion);\n\n    deviceManager.activateWindowAsync();\n    await deviceManager.openUrlAsync(url, { appId: deviceManager.getExpoGoAppId() });\n\n    return { url };\n  }\n\n  protected async openProjectInCustomRuntimeAsync(\n    resolveSettings: Partial<IResolveDeviceProps> = {},\n    props: Partial<IOpenInCustomProps> = {}\n  ): Promise<{ url: string }> {\n    debug(\n      `open custom (${Object.entries(props)\n        .map(([k, v]) => `${k}: ${v}`)\n        .join(', ')})`\n    );\n\n    let url = this.props.getCustomRuntimeUrl({ scheme: props.scheme });\n    debug(`Opening project in custom runtime: ${url} -- %O`, props);\n    // TODO: It's unclear why we do application id validation when opening with a URL\n    // NOTE: But having it enables us to allow the deep link to directly open on iOS simulators without the modal.\n    const applicationId = props.applicationId ?? (await this._getAppIdResolver().getAppIdAsync());\n\n    const deviceManager = await this.props.resolveDeviceAsync(resolveSettings);\n\n    if (!(await deviceManager.isAppInstalledAndIfSoReturnContainerPathForIOSAsync(applicationId))) {\n      throw new CommandError(\n        `No development build (${applicationId}) for this project is installed. ` +\n          `Install a development build on the target device and try again.\\n${learnMore(\n            'https://docs.expo.dev/development/build/'\n          )}`\n      );\n    }\n\n    if (!url) {\n      url = this._resolveAlternativeLaunchUrl(applicationId, props);\n    }\n\n    deviceManager.logOpeningUrl(url);\n    await deviceManager.activateWindowAsync();\n\n    await deviceManager.openUrlAsync(url, {\n      appId: applicationId,\n    });\n\n    return {\n      url,\n    };\n  }\n\n  /** Launch the project on a device given the input runtime. */\n  async openAsync(\n    options:\n      | {\n          runtime: 'expo' | 'web';\n        }\n      | {\n          runtime: 'custom';\n          props?: Partial<IOpenInCustomProps>;\n        },\n    resolveSettings: Partial<IResolveDeviceProps> = {}\n  ): Promise<{ url: string }> {\n    debug(\n      `open (runtime: ${options.runtime}, platform: ${this.props.platform}, device: %O, shouldPrompt: ${resolveSettings.shouldPrompt})`,\n      resolveSettings.device\n    );\n    if (options.runtime === 'expo') {\n      return this.openProjectInExpoGoAsync(resolveSettings);\n    } else if (options.runtime === 'web') {\n      return this.openWebProjectAsync(resolveSettings);\n    } else if (options.runtime === 'custom') {\n      return this.openProjectInCustomRuntimeAsync(resolveSettings, options.props);\n    } else {\n      throw new CommandError(`Invalid runtime target: ${options.runtime}`);\n    }\n  }\n\n  /** Open the current web project (Webpack) in a device . */\n  private async openWebProjectAsync(resolveSettings: Partial<IResolveDeviceProps> = {}): Promise<{\n    url: string;\n  }> {\n    const url = this.props.getDevServerUrl();\n    assert(url, 'Dev server is not running.');\n\n    const deviceManager = await this.props.resolveDeviceAsync(resolveSettings);\n    deviceManager.logOpeningUrl(url);\n    await deviceManager.activateWindowAsync();\n    await deviceManager.openUrlAsync(url);\n\n    return { url };\n  }\n\n  /** If the launch URL cannot be determined (`custom` runtimes) then an alternative string can be provided to open the device. Often a device ID or activity to launch. Exposed for testing. */\n  _resolveAlternativeLaunchUrl(\n    applicationId: string,\n    props: Partial<IOpenInCustomProps> = {}\n  ): string {\n    throw new UnimplementedError();\n  }\n}\n"], "names": ["PlatformManager", "debug", "require", "constructor", "projectRoot", "props", "_getAppIdResolver", "UnimplementedError", "getExpoGoOrCustomRuntimeUrlAsync", "deviceManager", "redirectUrl", "getRedirectUrl", "applicationId", "getAppIdAsync", "Log", "warn", "chalk", "learnMore", "isAppInstalledAndIfSoReturnContainerPathForIOSAsync", "name", "getExpoGoUrl", "openProjectInExpoGoAsync", "resolveSettings", "resolveDeviceAsync", "url", "logOpeningUrl", "exp", "getConfig", "sdkVersion", "assert", "ensureExpoGoAsync", "activateWindowAsync", "openUrlAsync", "appId", "getExpoGoAppId", "openProjectInCustomRuntimeAsync", "Object", "entries", "map", "k", "v", "join", "getCustomRuntimeUrl", "scheme", "CommandError", "_resolveAlternativeLaunchUrl", "openAsync", "options", "runtime", "platform", "should<PERSON>rompt", "device", "openWebProjectAsync", "getDevServerUrl"], "mappings": ";;;;+BAyBaA;;;eAAAA;;;;yBAzBa;;;;;;;gEACP;;;;;;;gEACD;;;;;;qBAIE;wBAC6B;sBACvB;;;;;;AAE1B,MAAMC,QAAQC,QAAQ,SAAS;AAexB,MAAMF;IAKXG,YACE,AAAUC,WAAmB,EAC7B,AAAUC,KAiBT,CACD;aAnBUD,cAAAA;aACAC,QAAAA;IAkBT;IAEH,wGAAwG,GACxGC,oBAAmC;QACjC,MAAM,IAAIC,0BAAkB;IAC9B;IAEA;;;;GAIC,GACD,MAAgBC,iCACdC,aAAqC,EACpB;QACjB,4FAA4F;QAC5F,MAAMC,cAAc,IAAI,CAACL,KAAK,CAACM,cAAc;QAC7C,IAAID,aAAa;YACf,oGAAoG;YACpG,IAAIE;YACJ,IAAI;gBACFA,gBAAgB,MAAM,IAAI,CAACN,iBAAiB,GAAGO,aAAa;YAC9D,EAAE,OAAM;gBACNC,QAAG,CAACC,IAAI,CACNC,IAAAA,gBAAK,CAAA,CAAC,kDAAkD,CAAC,GACvD,CAAC,gFAAgF,CAAC,GAClFA,IAAAA,gBAAK,CAAA,CAAC,4EAA4E,CAAC,GACnF,CAAC,4BAA4B,EAAEC,IAAAA,eAAS,EAAC,6CAA6C;YAE5F;YACA,IAAIL,eAAe;gBACjBX,MAAM,CAAC,8BAA8B,EAAEW,cAAc,gBAAgB,EAAEF,YAAY,CAAC,CAAC;gBACrF,sHAAsH;gBACtH,4EAA4E;gBAC5E,IACE,MAAMD,cAAcS,mDAAmD,CAACN,gBACxE;oBACA,OAAOF;gBACT,OAAO;oBACL,4EAA4E;oBAC5E,+CAA+C;oBAC/CI,QAAG,CAACC,IAAI,CACNC,IAAAA,gBAAK,CAAA,CAAC,uFAAuF,CAAC,GAC5FA,IAAAA,gBAAK,CAAA,CAAC,mBAAmB,EAAEP,cAAcU,IAAI,CAAC,+CAA+C,CAAC,GAC9F,CAAC,8DAA8D,EAAEF,IAAAA,eAAS,EACxE,6CACC;gBAET;YACF;QACF;QAEA,OAAO,IAAI,CAACZ,KAAK,CAACe,YAAY;IAChC;IAEA,MAAgBC,yBACdC,kBAAgD,CAAC,CAAC,EACxB;QAC1B,MAAMb,gBAAgB,MAAM,IAAI,CAACJ,KAAK,CAACkB,kBAAkB,CAACD;QAC1D,MAAME,MAAM,MAAM,IAAI,CAAChB,gCAAgC,CAACC;QAExDA,cAAcgB,aAAa,CAACD;QAE5B,gDAAgD;QAChD,MAAM,EAAEE,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAAC,IAAI,CAACvB,WAAW;QAC1C,MAAMwB,aAAaF,IAAIE,UAAU;QACjCC,IAAAA,iBAAM,EAACD,YAAY;QACnB,MAAMnB,cAAcqB,iBAAiB,CAACF;QAEtCnB,cAAcsB,mBAAmB;QACjC,MAAMtB,cAAcuB,YAAY,CAACR,KAAK;YAAES,OAAOxB,cAAcyB,cAAc;QAAG;QAE9E,OAAO;YAAEV;QAAI;IACf;IAEA,MAAgBW,gCACdb,kBAAgD,CAAC,CAAC,EAClDjB,QAAqC,CAAC,CAAC,EACb;QAC1BJ,MACE,CAAC,aAAa,EAAEmC,OAAOC,OAAO,CAAChC,OAC5BiC,GAAG,CAAC,CAAC,CAACC,GAAGC,EAAE,GAAK,GAAGD,EAAE,EAAE,EAAEC,GAAG,EAC5BC,IAAI,CAAC,MAAM,CAAC,CAAC;QAGlB,IAAIjB,MAAM,IAAI,CAACnB,KAAK,CAACqC,mBAAmB,CAAC;YAAEC,QAAQtC,MAAMsC,MAAM;QAAC;QAChE1C,MAAM,CAAC,mCAAmC,EAAEuB,IAAI,MAAM,CAAC,EAAEnB;QACzD,iFAAiF;QACjF,8GAA8G;QAC9G,MAAMO,gBAAgBP,MAAMO,aAAa,IAAK,MAAM,IAAI,CAACN,iBAAiB,GAAGO,aAAa;QAE1F,MAAMJ,gBAAgB,MAAM,IAAI,CAACJ,KAAK,CAACkB,kBAAkB,CAACD;QAE1D,IAAI,CAAE,MAAMb,cAAcS,mDAAmD,CAACN,gBAAiB;YAC7F,MAAM,IAAIgC,oBAAY,CACpB,CAAC,sBAAsB,EAAEhC,cAAc,iCAAiC,CAAC,GACvE,CAAC,iEAAiE,EAAEK,IAAAA,eAAS,EAC3E,6CACC;QAET;QAEA,IAAI,CAACO,KAAK;YACRA,MAAM,IAAI,CAACqB,4BAA4B,CAACjC,eAAeP;QACzD;QAEAI,cAAcgB,aAAa,CAACD;QAC5B,MAAMf,cAAcsB,mBAAmB;QAEvC,MAAMtB,cAAcuB,YAAY,CAACR,KAAK;YACpCS,OAAOrB;QACT;QAEA,OAAO;YACLY;QACF;IACF;IAEA,4DAA4D,GAC5D,MAAMsB,UACJC,OAOK,EACLzB,kBAAgD,CAAC,CAAC,EACxB;QAC1BrB,MACE,CAAC,eAAe,EAAE8C,QAAQC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC3C,KAAK,CAAC4C,QAAQ,CAAC,4BAA4B,EAAE3B,gBAAgB4B,YAAY,CAAC,CAAC,CAAC,EACjI5B,gBAAgB6B,MAAM;QAExB,IAAIJ,QAAQC,OAAO,KAAK,QAAQ;YAC9B,OAAO,IAAI,CAAC3B,wBAAwB,CAACC;QACvC,OAAO,IAAIyB,QAAQC,OAAO,KAAK,OAAO;YACpC,OAAO,IAAI,CAACI,mBAAmB,CAAC9B;QAClC,OAAO,IAAIyB,QAAQC,OAAO,KAAK,UAAU;YACvC,OAAO,IAAI,CAACb,+BAA+B,CAACb,iBAAiByB,QAAQ1C,KAAK;QAC5E,OAAO;YACL,MAAM,IAAIuC,oBAAY,CAAC,CAAC,wBAAwB,EAAEG,QAAQC,OAAO,EAAE;QACrE;IACF;IAEA,yDAAyD,GACzD,MAAcI,oBAAoB9B,kBAAgD,CAAC,CAAC,EAEjF;QACD,MAAME,MAAM,IAAI,CAACnB,KAAK,CAACgD,eAAe;QACtCxB,IAAAA,iBAAM,EAACL,KAAK;QAEZ,MAAMf,gBAAgB,MAAM,IAAI,CAACJ,KAAK,CAACkB,kBAAkB,CAACD;QAC1Db,cAAcgB,aAAa,CAACD;QAC5B,MAAMf,cAAcsB,mBAAmB;QACvC,MAAMtB,cAAcuB,YAAY,CAACR;QAEjC,OAAO;YAAEA;QAAI;IACf;IAEA,4LAA4L,GAC5LqB,6BACEjC,aAAqB,EACrBP,QAAqC,CAAC,CAAC,EAC/B;QACR,MAAM,IAAIE,0BAAkB;IAC9B;AACF"}