{"version": 3, "sources": ["../../../../../src/api/graphql/types/App.ts"], "sourcesContent": ["import { TypedDocumentNode, gql } from '@urql/core';\n\nexport const AppFragmentNode: TypedDocumentNode = gql`\n  fragment AppFragment on App {\n    id\n    scopeKey\n    ownerAccount {\n      id\n      name\n    }\n  }\n`;\n"], "names": ["AppFragmentNode", "gql"], "mappings": ";;;;+BAEaA;;;eAAAA;;;;yBAF0B;;;;;;AAEhC,MAAMA,kBAAqCC,IAAAA,WAAG,CAAA,CAAC;;;;;;;;;AAStD,CAAC"}