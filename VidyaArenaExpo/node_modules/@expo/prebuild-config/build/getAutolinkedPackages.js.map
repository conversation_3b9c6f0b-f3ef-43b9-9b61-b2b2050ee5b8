{"version": 3, "file": "getAutolinkedPackages.js", "names": ["_unstableAutolinkingExports", "data", "require", "getAutolinkedPackagesAsync", "projectRoot", "platforms", "linker", "makeCachedDependenciesLinker", "dependenciesPerPlatform", "Promise", "all", "map", "platform", "scanExpoModuleResolutionsForPlatform", "resolvePackagesList", "platformPaths", "allPlatformPaths", "paths", "Object", "keys", "flat", "uniquePaths", "Set", "sort", "shouldSkipAutoPlugin", "config", "plugin", "Array", "isArray", "_internal", "autolinkedModules", "pluginId", "isIncluded", "includes"], "sources": ["../src/getAutolinkedPackages.ts"], "sourcesContent": ["import { ModPlatform, StaticPlugin } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\nimport {\n  makeCachedDependenciesLinker,\n  scanExpoModuleResolutionsForPlatform,\n} from 'expo/internal/unstable-autolinking-exports';\n\n/**\n * Returns a list of packages that are autolinked to a project.\n *\n * @param projectRoot\n * @param platforms platforms to check for\n * @returns list of packages ex: `['expo-camera', 'react-native-screens']`\n */\nexport async function getAutolinkedPackagesAsync(\n  projectRoot: string,\n  platforms: ModPlatform[] = ['ios', 'android']\n) {\n  const linker = makeCachedDependenciesLinker({ projectRoot });\n  const dependenciesPerPlatform = await Promise.all(\n    platforms.map((platform) => {\n      return scanExpoModuleResolutionsForPlatform(linker, platform);\n    })\n  );\n  return resolvePackagesList(dependenciesPerPlatform);\n}\n\nexport function resolvePackagesList(platformPaths: Record<string, any>[]) {\n  const allPlatformPaths = platformPaths.map((paths) => Object.keys(paths)).flat();\n\n  const uniquePaths = [...new Set(allPlatformPaths)];\n\n  return uniquePaths.sort();\n}\n\nexport function shouldSkipAutoPlugin(\n  config: Pick<ExpoConfig, '_internal'>,\n  plugin: StaticPlugin | string\n) {\n  // Hack workaround because expo-dev-client doesn't use expo modules.\n  if (plugin === 'expo-dev-client') {\n    return false;\n  }\n\n  // Only perform the check if `autolinkedModules` is defined, otherwise we assume\n  // this is a legacy runner which doesn't support autolinking.\n  if (Array.isArray(config._internal?.autolinkedModules)) {\n    // Resolve the pluginId as a string.\n    const pluginId = Array.isArray(plugin) ? plugin[0] : plugin;\n    if (typeof pluginId === 'string') {\n      // Determine if the autolinked modules list includes our moduleId\n      const isIncluded = config._internal!.autolinkedModules.includes(pluginId);\n      if (!isIncluded) {\n        // If it doesn't then we know that any potential plugin shouldn't be applied automatically.\n        return true;\n      }\n    }\n  }\n  return false;\n}\n"], "mappings": ";;;;;;;;AAEA,SAAAA,4BAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,2BAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,eAAeE,0BAA0BA,CAC9CC,WAAmB,EACnBC,SAAwB,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,EAC7C;EACA,MAAMC,MAAM,GAAG,IAAAC,0DAA4B,EAAC;IAAEH;EAAY,CAAC,CAAC;EAC5D,MAAMI,uBAAuB,GAAG,MAAMC,OAAO,CAACC,GAAG,CAC/CL,SAAS,CAACM,GAAG,CAAEC,QAAQ,IAAK;IAC1B,OAAO,IAAAC,kEAAoC,EAACP,MAAM,EAAEM,QAAQ,CAAC;EAC/D,CAAC,CACH,CAAC;EACD,OAAOE,mBAAmB,CAACN,uBAAuB,CAAC;AACrD;AAEO,SAASM,mBAAmBA,CAACC,aAAoC,EAAE;EACxE,MAAMC,gBAAgB,GAAGD,aAAa,CAACJ,GAAG,CAAEM,KAAK,IAAKC,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;EAEhF,MAAMC,WAAW,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACN,gBAAgB,CAAC,CAAC;EAElD,OAAOK,WAAW,CAACE,IAAI,CAAC,CAAC;AAC3B;AAEO,SAASC,oBAAoBA,CAClCC,MAAqC,EACrCC,MAA6B,EAC7B;EACA;EACA,IAAIA,MAAM,KAAK,iBAAiB,EAAE;IAChC,OAAO,KAAK;EACd;;EAEA;EACA;EACA,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAACI,SAAS,EAAEC,iBAAiB,CAAC,EAAE;IACtD;IACA,MAAMC,QAAQ,GAAGJ,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM;IAC3D,IAAI,OAAOK,QAAQ,KAAK,QAAQ,EAAE;MAChC;MACA,MAAMC,UAAU,GAAGP,MAAM,CAACI,SAAS,CAAEC,iBAAiB,CAACG,QAAQ,CAACF,QAAQ,CAAC;MACzE,IAAI,CAACC,UAAU,EAAE;QACf;QACA,OAAO,IAAI;MACb;IACF;EACF;EACA,OAAO,KAAK;AACd", "ignoreList": []}