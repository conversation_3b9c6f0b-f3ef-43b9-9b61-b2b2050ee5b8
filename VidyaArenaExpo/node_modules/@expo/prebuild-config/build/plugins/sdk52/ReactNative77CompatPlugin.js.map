{"version": 3, "file": "ReactNative77CompatPlugin.js", "names": ["_configPlugins", "data", "require", "_jsonFile", "_interopRequireDefault", "_resolveFrom", "_semver", "e", "__esModule", "default", "withSdk52ReactNative77CompatAndroid", "config", "withProjectBuildGradle", "sdkVersion", "reactNativeVersion", "queryReactNativeVersionAsync", "modRequest", "projectRoot", "semver", "lt", "modResults", "language", "contents", "setProjectBuildGradle", "WarningAggregator", "addWarningAndroid", "exports", "kotlinVersion", "newContents", "replace", "ndkVersion", "packageJsonPath", "resolveFrom", "silent", "packageJson", "JsonFile", "readAsync", "version", "parse"], "sources": ["../../../src/plugins/sdk52/ReactNative77CompatPlugin.ts"], "sourcesContent": ["import { withProjectBuildGradle, WarningAggregator, type ConfigPlugin } from '@expo/config-plugins';\nimport JsonFile from '@expo/json-file';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\n\n// TODO(kudo,20241112): Remove this plugin when we drop support for SDK 52.\nexport const withSdk52ReactNative77CompatAndroid: ConfigPlugin = (config) => {\n  return withProjectBuildGradle(config, async (config) => {\n    if (config.sdkVersion !== '52.0.0') {\n      return config;\n    }\n    const reactNativeVersion = await queryReactNativeVersionAsync(config.modRequest.projectRoot);\n    if (!reactNativeVersion || semver.lt(reactNativeVersion, '0.77.0')) {\n      return config;\n    }\n\n    if (config.modResults.language === 'groovy') {\n      config.modResults.contents = setProjectBuildGradle(config.modResults.contents);\n    } else {\n      WarningAggregator.addWarningAndroid(\n        'ReactNative77CompatPlugin',\n        `Cannot automatically configure project build.gradle if it's not groovy`\n      );\n    }\n    return config;\n  });\n};\n\nfunction setProjectBuildGradle(contents: string): string {\n  // Update kotlinVersion\n  const kotlinVersion = '2.0.21';\n  let newContents = contents.replace(\n    /\\b(kotlinVersion\\s*=\\s*findProperty\\('android.kotlinVersion'\\)\\s*\\?: ['\"])(1\\.9\\.\\d+)(['\"])/g,\n    `$1${kotlinVersion}$3`\n  );\n\n  // Update ndkVersion\n  const ndkVersion = '27.1.12297006';\n  newContents = newContents.replace(\n    /\\b(ndkVersion\\s*=\\s*['\"])(26.1.10909125)(['\"])/g,\n    `$1${ndkVersion}$3`\n  );\n\n  return newContents;\n}\n\nasync function queryReactNativeVersionAsync(projectRoot: string): Promise<semver.SemVer | null> {\n  const packageJsonPath = resolveFrom.silent(projectRoot, 'react-native/package.json');\n  if (packageJsonPath) {\n    const packageJson = await JsonFile.readAsync(packageJsonPath);\n    const version = packageJson.version;\n    if (typeof version === 'string') {\n      return semver.parse(version);\n    }\n  }\n  return null;\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,aAAA;EAAA,MAAAJ,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAG,YAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4B,SAAAG,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE5B;AACO,MAAMG,mCAAiD,GAAIC,MAAM,IAAK;EAC3E,OAAO,IAAAC,uCAAsB,EAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IACtD,IAAIA,MAAM,CAACE,UAAU,KAAK,QAAQ,EAAE;MAClC,OAAOF,MAAM;IACf;IACA,MAAMG,kBAAkB,GAAG,MAAMC,4BAA4B,CAACJ,MAAM,CAACK,UAAU,CAACC,WAAW,CAAC;IAC5F,IAAI,CAACH,kBAAkB,IAAII,iBAAM,CAACC,EAAE,CAACL,kBAAkB,EAAE,QAAQ,CAAC,EAAE;MAClE,OAAOH,MAAM;IACf;IAEA,IAAIA,MAAM,CAACS,UAAU,CAACC,QAAQ,KAAK,QAAQ,EAAE;MAC3CV,MAAM,CAACS,UAAU,CAACE,QAAQ,GAAGC,qBAAqB,CAACZ,MAAM,CAACS,UAAU,CAACE,QAAQ,CAAC;IAChF,CAAC,MAAM;MACLE,kCAAiB,CAACC,iBAAiB,CACjC,2BAA2B,EAC3B,wEACF,CAAC;IACH;IACA,OAAOd,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACe,OAAA,CAAAhB,mCAAA,GAAAA,mCAAA;AAEF,SAASa,qBAAqBA,CAACD,QAAgB,EAAU;EACvD;EACA,MAAMK,aAAa,GAAG,QAAQ;EAC9B,IAAIC,WAAW,GAAGN,QAAQ,CAACO,OAAO,CAChC,8FAA8F,EAC9F,KAAKF,aAAa,IACpB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAG,eAAe;EAClCF,WAAW,GAAGA,WAAW,CAACC,OAAO,CAC/B,iDAAiD,EACjD,KAAKC,UAAU,IACjB,CAAC;EAED,OAAOF,WAAW;AACpB;AAEA,eAAeb,4BAA4BA,CAACE,WAAmB,EAAiC;EAC9F,MAAMc,eAAe,GAAGC,sBAAW,CAACC,MAAM,CAAChB,WAAW,EAAE,2BAA2B,CAAC;EACpF,IAAIc,eAAe,EAAE;IACnB,MAAMG,WAAW,GAAG,MAAMC,mBAAQ,CAACC,SAAS,CAACL,eAAe,CAAC;IAC7D,MAAMM,OAAO,GAAGH,WAAW,CAACG,OAAO;IACnC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,OAAOnB,iBAAM,CAACoB,KAAK,CAACD,OAAO,CAAC;IAC9B;EACF;EACA,OAAO,IAAI;AACb", "ignoreList": []}