{"version": 3, "file": "withAndroidSplashStrings.js", "names": ["_configPlugins", "data", "require", "_getAndroidSplashConfig", "RESIZE_MODE_KEY", "STATUS_BAR_TRANSLUCENT_KEY", "withAndroidSplashStrings", "config", "props", "withStringsXml", "splashConfig", "getAndroidSplashConfig", "resizeMode", "statusBarTranslucent", "androidStatusBar", "translucent", "modResults", "setSplashStrings", "exports", "strings", "AndroidConfig", "Strings", "setStringItem", "Resources", "buildResourceItem", "name", "value", "translatable", "String"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withAndroidSplashStrings.ts"], "sourcesContent": ["import { AndroidConfig, ConfigPlugin, withStringsXml } from '@expo/config-plugins';\n\nimport { AndroidSplashConfig, getAndroidSplashConfig } from './getAndroidSplashConfig';\n\nconst RESIZE_MODE_KEY = 'expo_splash_screen_resize_mode';\nconst STATUS_BAR_TRANSLUCENT_KEY = 'expo_splash_screen_status_bar_translucent';\n\nexport const withAndroidSplashStrings: ConfigPlugin<AndroidSplashConfig> = (config, props) => {\n  return withStringsXml(config, (config) => {\n    const splashConfig = getAndroidSplashConfig(config, props);\n    if (splashConfig) {\n      const { resizeMode } = splashConfig;\n      const statusBarTranslucent = !!config.androidStatusBar?.translucent;\n      config.modResults = setSplashStrings(config.modResults, resizeMode, statusBarTranslucent);\n    }\n    return config;\n  });\n};\n\nexport function setSplashStrings(\n  strings: AndroidConfig.Resources.ResourceXML,\n  resizeMode: string,\n  statusBarTranslucent: boolean\n): AndroidConfig.Resources.ResourceXML {\n  return AndroidConfig.Strings.setStringItem(\n    [\n      AndroidConfig.Resources.buildResourceItem({\n        name: RESIZE_MODE_KEY,\n        value: resizeMode,\n        translatable: false,\n      }),\n      AndroidConfig.Resources.buildResourceItem({\n        name: STATUS_BAR_TRANSLUCENT_KEY,\n        value: String(statusBarTranslucent),\n        translatable: false,\n      }),\n    ],\n    strings\n  );\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,wBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,uBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAMG,eAAe,GAAG,gCAAgC;AACxD,MAAMC,0BAA0B,GAAG,2CAA2C;AAEvE,MAAMC,wBAA2D,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC5F,OAAO,IAAAC,+BAAc,EAACF,MAAM,EAAGA,MAAM,IAAK;IACxC,MAAMG,YAAY,GAAG,IAAAC,gDAAsB,EAACJ,MAAM,EAAEC,KAAK,CAAC;IAC1D,IAAIE,YAAY,EAAE;MAChB,MAAM;QAAEE;MAAW,CAAC,GAAGF,YAAY;MACnC,MAAMG,oBAAoB,GAAG,CAAC,CAACN,MAAM,CAACO,gBAAgB,EAAEC,WAAW;MACnER,MAAM,CAACS,UAAU,GAAGC,gBAAgB,CAACV,MAAM,CAACS,UAAU,EAAEJ,UAAU,EAAEC,oBAAoB,CAAC;IAC3F;IACA,OAAON,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACW,OAAA,CAAAZ,wBAAA,GAAAA,wBAAA;AAEK,SAASW,gBAAgBA,CAC9BE,OAA4C,EAC5CP,UAAkB,EAClBC,oBAA6B,EACQ;EACrC,OAAOO,8BAAa,CAACC,OAAO,CAACC,aAAa,CACxC,CACEF,8BAAa,CAACG,SAAS,CAACC,iBAAiB,CAAC;IACxCC,IAAI,EAAErB,eAAe;IACrBsB,KAAK,EAAEd,UAAU;IACjBe,YAAY,EAAE;EAChB,CAAC,CAAC,EACFP,8BAAa,CAACG,SAAS,CAACC,iBAAiB,CAAC;IACxCC,IAAI,EAAEpB,0BAA0B;IAChCqB,KAAK,EAAEE,MAAM,CAACf,oBAAoB,CAAC;IACnCc,YAAY,EAAE;EAChB,CAAC,CAAC,CACH,EACDR,OACF,CAAC;AACH", "ignoreList": []}