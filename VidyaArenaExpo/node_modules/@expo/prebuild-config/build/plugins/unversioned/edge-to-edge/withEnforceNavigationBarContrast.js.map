{"version": 3, "file": "withEnforceNavigationBarContrast.js", "names": ["_configPlugins", "data", "require", "withEnforceNavigationBarContrast", "config", "enforceNavigationBarContrast", "withAndroidStyles", "applyEnforceNavigationBarContrast", "exports", "enforceNavigationBarContrastItem", "_", "$", "name", "style", "modResults", "resources", "mainThemeIndex", "findIndex", "mainTheme", "enforceIndex", "item", "filter"], "sources": ["../../../../src/plugins/unversioned/edge-to-edge/withEnforceNavigationBarContrast.ts"], "sourcesContent": ["import { ConfigPlugin, withAndroidStyles } from '@expo/config-plugins';\n\nimport { ResourceXMLConfig } from './withEdgeToEdge';\n\nexport const withEnforceNavigationBarContrast: ConfigPlugin<boolean> = (\n  config,\n  enforceNavigationBarContrast: boolean\n) => {\n  return withAndroidStyles(config, (config) => {\n    return applyEnforceNavigationBarContrast(config, enforceNavigationBarContrast);\n  });\n};\n\nexport function applyEnforceNavigationBarContrast(\n  config: ResourceXMLConfig,\n  enforceNavigationBarContrast: boolean\n): ResourceXMLConfig {\n  const enforceNavigationBarContrastItem = {\n    _: enforceNavigationBarContrast ? 'true' : 'false',\n    $: {\n      name: 'android:enforceNavigationBarContrast',\n      'tools:targetApi': '29',\n    },\n  };\n  const { style = [] } = config.modResults.resources;\n  const mainThemeIndex = style.findIndex(({ $ }) => $.name === 'AppTheme');\n  if (mainThemeIndex === -1) {\n    return config;\n  }\n  const mainTheme = style[mainThemeIndex];\n  const enforceIndex = mainTheme.item.findIndex(\n    ({ $ }) => $.name === 'android:enforceNavigationBarContrast'\n  );\n  if (enforceIndex !== -1) {\n    style[mainThemeIndex].item[enforceIndex] = enforceNavigationBarContrastItem;\n    return config;\n  }\n\n  config.modResults.resources.style = [\n    {\n      $: style[mainThemeIndex].$,\n      item: [enforceNavigationBarContrastItem, ...mainTheme.item],\n    },\n    ...style.filter(({ $ }) => $.name !== 'AppTheme'),\n  ];\n\n  return config;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIO,MAAME,gCAAuD,GAAGA,CACrEC,MAAM,EACNC,4BAAqC,KAClC;EACH,OAAO,IAAAC,kCAAiB,EAACF,MAAM,EAAGA,MAAM,IAAK;IAC3C,OAAOG,iCAAiC,CAACH,MAAM,EAAEC,4BAA4B,CAAC;EAChF,CAAC,CAAC;AACJ,CAAC;AAACG,OAAA,CAAAL,gCAAA,GAAAA,gCAAA;AAEK,SAASI,iCAAiCA,CAC/CH,MAAyB,EACzBC,4BAAqC,EAClB;EACnB,MAAMI,gCAAgC,GAAG;IACvCC,CAAC,EAAEL,4BAA4B,GAAG,MAAM,GAAG,OAAO;IAClDM,CAAC,EAAE;MACDC,IAAI,EAAE,sCAAsC;MAC5C,iBAAiB,EAAE;IACrB;EACF,CAAC;EACD,MAAM;IAAEC,KAAK,GAAG;EAAG,CAAC,GAAGT,MAAM,CAACU,UAAU,CAACC,SAAS;EAClD,MAAMC,cAAc,GAAGH,KAAK,CAACI,SAAS,CAAC,CAAC;IAAEN;EAAE,CAAC,KAAKA,CAAC,CAACC,IAAI,KAAK,UAAU,CAAC;EACxE,IAAII,cAAc,KAAK,CAAC,CAAC,EAAE;IACzB,OAAOZ,MAAM;EACf;EACA,MAAMc,SAAS,GAAGL,KAAK,CAACG,cAAc,CAAC;EACvC,MAAMG,YAAY,GAAGD,SAAS,CAACE,IAAI,CAACH,SAAS,CAC3C,CAAC;IAAEN;EAAE,CAAC,KAAKA,CAAC,CAACC,IAAI,KAAK,sCACxB,CAAC;EACD,IAAIO,YAAY,KAAK,CAAC,CAAC,EAAE;IACvBN,KAAK,CAACG,cAAc,CAAC,CAACI,IAAI,CAACD,YAAY,CAAC,GAAGV,gCAAgC;IAC3E,OAAOL,MAAM;EACf;EAEAA,MAAM,CAACU,UAAU,CAACC,SAAS,CAACF,KAAK,GAAG,CAClC;IACEF,CAAC,EAAEE,KAAK,CAACG,cAAc,CAAC,CAACL,CAAC;IAC1BS,IAAI,EAAE,CAACX,gCAAgC,EAAE,GAAGS,SAAS,CAACE,IAAI;EAC5D,CAAC,EACD,GAAGP,KAAK,CAACQ,MAAM,CAAC,CAAC;IAAEV;EAAE,CAAC,KAAKA,CAAC,CAACC,IAAI,KAAK,UAAU,CAAC,CAClD;EAED,OAAOR,MAAM;AACf", "ignoreList": []}