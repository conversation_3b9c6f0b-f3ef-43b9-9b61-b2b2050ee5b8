{"version": 3, "file": "withEdgeToEdgeEnabledGradleProperties.js", "names": ["_configPlugins", "data", "require", "OLD_EXPO_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT", "EXPO_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_KEY", "EXPO_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT", "REACT_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_KEY", "REACT_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT", "withEdgeToEdgeEnabledGradleProperties", "config", "props", "withGradleProperties", "configureEdgeToEdgeEnabledGradleProperties", "edgeToEdgeEnabled", "removeOldExpoEdgeToEdgeEnabledComment", "configureGradleProperty", "commentIndex", "modResults", "findIndex", "item", "type", "value", "splice", "key", "comment", "addNewLine", "propertyIndex", "push"], "sources": ["../../../../src/plugins/unversioned/edge-to-edge/withEdgeToEdgeEnabledGradleProperties.ts"], "sourcesContent": ["import { withGradleProperties } from '@expo/config-plugins';\nimport type { ExpoConfig } from '@expo/config-types';\n\nimport { GradlePropertiesConfig } from './withEdgeToEdge';\n\nconst OLD_EXPO_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT =\n  'Whether the app is configured to use edge-to-edge via the app config or `react-native-edge-to-edge` plugin';\nconst EXPO_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_KEY = 'expo.edgeToEdgeEnabled';\nconst EXPO_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT =\n  'Specifies whether the app is configured to use edge-to-edge via the app config or plugin\\n' +\n  '# WARNING: This property has been deprecated and will be removed in Expo SDK 55. Use `edgeToEdgeEnabled` or `react.edgeToEdgeEnabled` to determine whether the project is using edge-to-edge.';\n\nconst REACT_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_KEY = 'edgeToEdgeEnabled';\nconst REACT_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT =\n  'Use this property to enable edge-to-edge display support.\\n' +\n  '# This allows your app to draw behind system bars for an immersive UI.\\n' +\n  '# Note: Only works with ReactActivity and should not be used with custom Activity.';\n\nexport function withEdgeToEdgeEnabledGradleProperties(\n  config: ExpoConfig,\n  props: {\n    edgeToEdgeEnabled: boolean;\n  }\n) {\n  return withGradleProperties(config, (config) => {\n    return configureEdgeToEdgeEnabledGradleProperties(config, props.edgeToEdgeEnabled);\n  });\n}\n\nexport function configureEdgeToEdgeEnabledGradleProperties(\n  config: GradlePropertiesConfig,\n  edgeToEdgeEnabled: boolean\n): GradlePropertiesConfig {\n  // TODO: Remove for SDK 55\n  config = removeOldExpoEdgeToEdgeEnabledComment(config);\n\n  // TODO: Remove for SDK 55\n  config = configureGradleProperty(\n    config,\n    EXPO_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_KEY,\n    edgeToEdgeEnabled ? 'true' : 'false',\n    EXPO_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT\n  );\n\n  return configureGradleProperty(\n    config,\n    REACT_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_KEY,\n    edgeToEdgeEnabled ? 'true' : 'false',\n    REACT_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT\n  );\n}\n\n// TODO: Remove for SDK 55\nfunction removeOldExpoEdgeToEdgeEnabledComment(\n  config: GradlePropertiesConfig\n): GradlePropertiesConfig {\n  const commentIndex = config.modResults.findIndex(\n    (item) =>\n      item.type === 'comment' &&\n      item.value === OLD_EXPO_EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_COMMENT\n  );\n  if (commentIndex !== -1) {\n    config.modResults.splice(commentIndex, 1);\n  }\n  return config;\n}\n\nfunction configureGradleProperty(\n  config: GradlePropertiesConfig,\n  key: string,\n  value: string,\n  comment: string,\n  addNewLine: boolean = true\n): GradlePropertiesConfig {\n  const propertyIndex = config.modResults.findIndex(\n    (item) => item.type === 'property' && item.key === key\n  );\n\n  if (propertyIndex !== -1 && config.modResults[propertyIndex].type === 'property') {\n    config.modResults[propertyIndex].value = value;\n  } else {\n    config.modResults.push({\n      type: 'comment',\n      value: comment,\n    });\n\n    config.modResults.push({\n      type: 'property',\n      key,\n      value: addNewLine ? value + '\\n' : value,\n    });\n  }\n\n  return config;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKA,MAAME,qDAAqD,GACzD,4GAA4G;AAC9G,MAAMC,6CAA6C,GAAG,wBAAwB;AAC9E,MAAMC,iDAAiD,GACrD,4FAA4F,GAC5F,+LAA+L;AAEjM,MAAMC,8CAA8C,GAAG,mBAAmB;AAC1E,MAAMC,kDAAkD,GACtD,6DAA6D,GAC7D,0EAA0E,GAC1E,oFAAoF;AAE/E,SAASC,qCAAqCA,CACnDC,MAAkB,EAClBC,KAEC,EACD;EACA,OAAO,IAAAC,qCAAoB,EAACF,MAAM,EAAGA,MAAM,IAAK;IAC9C,OAAOG,0CAA0C,CAACH,MAAM,EAAEC,KAAK,CAACG,iBAAiB,CAAC;EACpF,CAAC,CAAC;AACJ;AAEO,SAASD,0CAA0CA,CACxDH,MAA8B,EAC9BI,iBAA0B,EACF;EACxB;EACAJ,MAAM,GAAGK,qCAAqC,CAACL,MAAM,CAAC;;EAEtD;EACAA,MAAM,GAAGM,uBAAuB,CAC9BN,MAAM,EACNL,6CAA6C,EAC7CS,iBAAiB,GAAG,MAAM,GAAG,OAAO,EACpCR,iDACF,CAAC;EAED,OAAOU,uBAAuB,CAC5BN,MAAM,EACNH,8CAA8C,EAC9CO,iBAAiB,GAAG,MAAM,GAAG,OAAO,EACpCN,kDACF,CAAC;AACH;;AAEA;AACA,SAASO,qCAAqCA,CAC5CL,MAA8B,EACN;EACxB,MAAMO,YAAY,GAAGP,MAAM,CAACQ,UAAU,CAACC,SAAS,CAC7CC,IAAI,IACHA,IAAI,CAACC,IAAI,KAAK,SAAS,IACvBD,IAAI,CAACE,KAAK,KAAKlB,qDACnB,CAAC;EACD,IAAIa,YAAY,KAAK,CAAC,CAAC,EAAE;IACvBP,MAAM,CAACQ,UAAU,CAACK,MAAM,CAACN,YAAY,EAAE,CAAC,CAAC;EAC3C;EACA,OAAOP,MAAM;AACf;AAEA,SAASM,uBAAuBA,CAC9BN,MAA8B,EAC9Bc,GAAW,EACXF,KAAa,EACbG,OAAe,EACfC,UAAmB,GAAG,IAAI,EACF;EACxB,MAAMC,aAAa,GAAGjB,MAAM,CAACQ,UAAU,CAACC,SAAS,CAC9CC,IAAI,IAAKA,IAAI,CAACC,IAAI,KAAK,UAAU,IAAID,IAAI,CAACI,GAAG,KAAKA,GACrD,CAAC;EAED,IAAIG,aAAa,KAAK,CAAC,CAAC,IAAIjB,MAAM,CAACQ,UAAU,CAACS,aAAa,CAAC,CAACN,IAAI,KAAK,UAAU,EAAE;IAChFX,MAAM,CAACQ,UAAU,CAACS,aAAa,CAAC,CAACL,KAAK,GAAGA,KAAK;EAChD,CAAC,MAAM;IACLZ,MAAM,CAACQ,UAAU,CAACU,IAAI,CAAC;MACrBP,IAAI,EAAE,SAAS;MACfC,KAAK,EAAEG;IACT,CAAC,CAAC;IAEFf,MAAM,CAACQ,UAAU,CAACU,IAAI,CAAC;MACrBP,IAAI,EAAE,UAAU;MAChBG,GAAG;MACHF,KAAK,EAAEI,UAAU,GAAGJ,KAAK,GAAG,IAAI,GAAGA;IACrC,CAAC,CAAC;EACJ;EAEA,OAAOZ,MAAM;AACf", "ignoreList": []}