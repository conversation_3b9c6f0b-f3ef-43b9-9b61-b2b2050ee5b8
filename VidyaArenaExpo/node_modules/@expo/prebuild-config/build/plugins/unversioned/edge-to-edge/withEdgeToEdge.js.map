{"version": 3, "file": "withEdgeToEdge.js", "names": ["_configPlugins", "data", "require", "_withConfigureEdgeToEdgeEnforcement", "_withEdgeToEdgeEnabledGradleProperties", "_withEnforceNavigationBarContrast", "_withRestoreDefaultTheme", "TAG", "withEdgeToEdge", "config", "projectRoot", "applyEdgeToEdge", "exports", "android", "edgeToEdgeEnabled", "WarningAggregator", "addWarningAndroid", "withEdgeToEdgeEnabledGradleProperties", "withConfigureEdgeToEdgeEnforcement", "disableEdgeToEdgeEnforcement", "withEnforceNavigationBarContrast", "androidNavigationBar", "enforceContrast", "withRestoreDefaultTheme", "_default", "default"], "sources": ["../../../../src/plugins/unversioned/edge-to-edge/withEdgeToEdge.ts"], "sourcesContent": ["import {\n  ConfigPlugin,\n  ExportedConfigWithProps,\n  WarningAggregator,\n  AndroidConfig,\n} from '@expo/config-plugins';\nimport { type ExpoConfig } from '@expo/config-types';\n\nimport { withConfigureEdgeToEdgeEnforcement } from './withConfigureEdgeToEdgeEnforcement';\nimport { withEdgeToEdgeEnabledGradleProperties } from './withEdgeToEdgeEnabledGradleProperties';\nimport { withEnforceNavigationBarContrast } from './withEnforceNavigationBarContrast';\nimport { withRestoreDefaultTheme } from './withRestoreDefaultTheme';\n\nconst TAG = 'EDGE_TO_EDGE_PLUGIN';\n\nexport type ResourceXMLConfig = ExportedConfigWithProps<AndroidConfig.Resources.ResourceXML>;\nexport type GradlePropertiesConfig = ExportedConfigWithProps<\n  AndroidConfig.Properties.PropertiesItem[]\n>;\n\nexport const withEdgeToEdge: ConfigPlugin<{ projectRoot: string }> = (config, { projectRoot }) => {\n  return applyEdgeToEdge(config, projectRoot);\n};\n\nexport function applyEdgeToEdge(config: ExpoConfig, projectRoot: string): ExpoConfig {\n  if (config.android?.edgeToEdgeEnabled === false) {\n    WarningAggregator.addWarningAndroid(\n      TAG,\n      '`edgeToEdgeEnabled` field is explicitly set to false in the project app config. In Android 16+ (targetSdkVersion 36) it is no longer be possible to disable edge-to-edge. Learn more:',\n      'https://expo.fyi/edge-to-edge-rollout'\n    );\n  }\n\n  const edgeToEdgeEnabled = config.android?.edgeToEdgeEnabled !== false;\n\n  config = withEdgeToEdgeEnabledGradleProperties(config, { edgeToEdgeEnabled });\n  // Enable/disable edge-to-edge enforcement\n  config = withConfigureEdgeToEdgeEnforcement(config, {\n    disableEdgeToEdgeEnforcement: !edgeToEdgeEnabled,\n  });\n\n  config = withEnforceNavigationBarContrast(\n    config,\n    config.androidNavigationBar?.enforceContrast !== false\n  );\n\n  // We always restore the default theme in case the project has a leftover react-native-edge-to-edge theme from SDK 53.\n  // If they are using react-native-edge-to-edge config plugin it'll be reapplied later.\n  return withRestoreDefaultTheme(config);\n}\n\nexport default withEdgeToEdge;\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA,SAAAE,oCAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,mCAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,uCAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,sCAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,kCAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,iCAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,yBAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,wBAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAMM,GAAG,GAAG,qBAAqB;AAO1B,MAAMC,cAAqD,GAAGA,CAACC,MAAM,EAAE;EAAEC;AAAY,CAAC,KAAK;EAChG,OAAOC,eAAe,CAACF,MAAM,EAAEC,WAAW,CAAC;AAC7C,CAAC;AAACE,OAAA,CAAAJ,cAAA,GAAAA,cAAA;AAEK,SAASG,eAAeA,CAACF,MAAkB,EAAEC,WAAmB,EAAc;EACnF,IAAID,MAAM,CAACI,OAAO,EAAEC,iBAAiB,KAAK,KAAK,EAAE;IAC/CC,kCAAiB,CAACC,iBAAiB,CACjCT,GAAG,EACH,uLAAuL,EACvL,uCACF,CAAC;EACH;EAEA,MAAMO,iBAAiB,GAAGL,MAAM,CAACI,OAAO,EAAEC,iBAAiB,KAAK,KAAK;EAErEL,MAAM,GAAG,IAAAQ,8EAAqC,EAACR,MAAM,EAAE;IAAEK;EAAkB,CAAC,CAAC;EAC7E;EACAL,MAAM,GAAG,IAAAS,wEAAkC,EAACT,MAAM,EAAE;IAClDU,4BAA4B,EAAE,CAACL;EACjC,CAAC,CAAC;EAEFL,MAAM,GAAG,IAAAW,oEAAgC,EACvCX,MAAM,EACNA,MAAM,CAACY,oBAAoB,EAAEC,eAAe,KAAK,KACnD,CAAC;;EAED;EACA;EACA,OAAO,IAAAC,kDAAuB,EAACd,MAAM,CAAC;AACxC;AAAC,IAAAe,QAAA,GAAAZ,OAAA,CAAAa,OAAA,GAEcjB,cAAc", "ignoreList": []}