{"version": 3, "file": "expo-apple-authentication.js", "names": ["_configPlugins", "data", "require", "_createLegacyPlugin", "withAppleSignInWarning", "config", "withEntitlementsPlist", "ios", "usesAppleSignIn", "WarningAggregator", "addWarningIOS", "_default", "exports", "default", "createLegacyPlugin", "packageName", "fallback"], "sources": ["../../../src/plugins/unversioned/expo-apple-authentication.ts"], "sourcesContent": ["import { ConfigPlugin, WarningAggregator, withEntitlementsPlist } from '@expo/config-plugins';\n\nimport { createLegacyPlugin } from './createLegacyPlugin';\n\nconst withAppleSignInWarning: ConfigPlugin = (config) => {\n  return withEntitlementsPlist(config, (config) => {\n    if (config.ios?.usesAppleSignIn) {\n      WarningAggregator.addWarningIOS(\n        'ios.usesAppleSignIn',\n        'Install expo-apple-authentication to enable this feature',\n        'https://docs.expo.dev/versions/latest/sdk/apple-authentication/#eas-build'\n      );\n    }\n\n    return config;\n  });\n};\n\nexport default createLegacyPlugin({\n  packageName: 'expo-apple-authentication',\n  fallback: withAppleSignInWarning,\n});\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,oBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,mBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAMG,sBAAoC,GAAIC,MAAM,IAAK;EACvD,OAAO,IAAAC,sCAAqB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAC/C,IAAIA,MAAM,CAACE,GAAG,EAAEC,eAAe,EAAE;MAC/BC,kCAAiB,CAACC,aAAa,CAC7B,qBAAqB,EACrB,0DAA0D,EAC1D,2EACF,CAAC;IACH;IAEA,OAAOL,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAAC,IAAAM,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEa,IAAAC,wCAAkB,EAAC;EAChCC,WAAW,EAAE,2BAA2B;EACxCC,QAAQ,EAAEZ;AACZ,CAAC,CAAC", "ignoreList": []}