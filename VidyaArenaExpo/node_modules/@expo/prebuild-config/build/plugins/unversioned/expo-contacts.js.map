{"version": 3, "file": "expo-contacts.js", "names": ["_configPlugins", "data", "require", "_createLegacyPlugin", "withAccessesContactNotes", "config", "withEntitlementsPlist", "modResults", "setAccessesContactNotes", "entitlementsPlist", "ios", "accessesContactNotes", "_default", "exports", "default", "createLegacyPlugin", "packageName", "fallback"], "sources": ["../../../src/plugins/unversioned/expo-contacts.ts"], "sourcesContent": ["import { ConfigPlugin, withEntitlementsPlist } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\nimport { JSONObject } from '@expo/json-file';\n\nimport { createLegacyPlugin } from './createLegacyPlugin';\n\nconst withAccessesContactNotes: ConfigPlugin = (config) => {\n  return withEntitlementsPlist(config, (config) => {\n    config.modResults = setAccessesContactNotes(config, config.modResults);\n    return config;\n  });\n};\n\nfunction setAccessesContactNotes(config: ExpoConfig, entitlementsPlist: JSONObject): JSONObject {\n  if (config.ios?.accessesContactNotes) {\n    return {\n      ...entitlementsPlist,\n      'com.apple.developer.contacts.notes': true,\n    };\n  }\n\n  return entitlementsPlist;\n}\n\nexport default createLegacyPlugin({\n  packageName: 'expo-contacts',\n  fallback: withAccessesContactNotes,\n});\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA,SAAAE,oBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,mBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAMG,wBAAsC,GAAIC,MAAM,IAAK;EACzD,OAAO,IAAAC,sCAAqB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAC/CA,MAAM,CAACE,UAAU,GAAGC,uBAAuB,CAACH,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IACtE,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAED,SAASG,uBAAuBA,CAACH,MAAkB,EAAEI,iBAA6B,EAAc;EAC9F,IAAIJ,MAAM,CAACK,GAAG,EAAEC,oBAAoB,EAAE;IACpC,OAAO;MACL,GAAGF,iBAAiB;MACpB,oCAAoC,EAAE;IACxC,CAAC;EACH;EAEA,OAAOA,iBAAiB;AAC1B;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc,IAAAC,wCAAkB,EAAC;EAChCC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAEb;AACZ,CAAC,CAAC", "ignoreList": []}