{"version": 3, "file": "new-arch-plist-hotfix.js", "names": ["_configPlugins", "data", "require", "withNewArchPlistHotfix", "config", "withInfoPlist", "modResults", "setNewArchPlistHotfixConfig", "getNewArchEnabled", "newArchEnabled", "ios", "toString", "infoPlist", "RCTNewArchEnabled", "_default", "exports", "default"], "sources": ["../../../../src/plugins/unversioned/new-arch-plist-hotfix/new-arch-plist-hotfix.ts"], "sourcesContent": ["import { ConfigPlugin, InfoPlist, withInfoPlist } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\n\n// This is a temporary plugin to fix the plist hotfix for the new arch.\n// Fixes: https://github.com/expo/expo/issues/39597\nconst withNewArchPlistHotfix: ConfigPlugin = (config) => {\n  return withInfoPlist(config, (config) => {\n    config.modResults = setNewArchPlistHotfixConfig(config, config.modResults);\n    return config;\n  });\n};\n\nfunction getNewArchEnabled(config: ExpoConfig) {\n  const newArchEnabled = (config.ios?.newArchEnabled ?? config.newArchEnabled)?.toString();\n  return newArchEnabled !== 'false';\n}\n\nfunction setNewArchPlistHotfixConfig(config: ExpoConfig, infoPlist: InfoPlist): InfoPlist {\n  const newArchEnabled = getNewArchEnabled(config);\n\n  return {\n    ...infoPlist,\n    RCTNewArchEnabled: newArchEnabled,\n  };\n}\n\nexport default withNewArchPlistHotfix;\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA;AACA;AACA,MAAME,sBAAoC,GAAIC,MAAM,IAAK;EACvD,OAAO,IAAAC,8BAAa,EAACD,MAAM,EAAGA,MAAM,IAAK;IACvCA,MAAM,CAACE,UAAU,GAAGC,2BAA2B,CAACH,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC1E,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAED,SAASI,iBAAiBA,CAACJ,MAAkB,EAAE;EAC7C,MAAMK,cAAc,GAAG,CAACL,MAAM,CAACM,GAAG,EAAED,cAAc,IAAIL,MAAM,CAACK,cAAc,GAAGE,QAAQ,CAAC,CAAC;EACxF,OAAOF,cAAc,KAAK,OAAO;AACnC;AAEA,SAASF,2BAA2BA,CAACH,MAAkB,EAAEQ,SAAoB,EAAa;EACxF,MAAMH,cAAc,GAAGD,iBAAiB,CAACJ,MAAM,CAAC;EAEhD,OAAO;IACL,GAAGQ,SAAS;IACZC,iBAAiB,EAAEJ;EACrB,CAAC;AACH;AAAC,IAAAK,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcb,sBAAsB", "ignoreList": []}