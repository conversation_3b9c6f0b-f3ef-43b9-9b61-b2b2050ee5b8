{"version": 3, "file": "expo-ads-admob.js", "names": ["_withAndroidAdMob", "data", "require", "_withIosAdMob", "_createLegacyPlugin", "_default", "exports", "default", "createLegacyPlugin", "packageName", "fallback", "withAndroidAdMob", "withIosAdMob"], "sources": ["../../../../src/plugins/unversioned/expo-ads-admob/expo-ads-admob.ts"], "sourcesContent": ["import { withAndroidAdMob } from './withAndroidAdMob';\nimport { withIosAdMob } from './withIosAdMob';\nimport { createLegacyPlugin } from '../createLegacyPlugin';\n\nexport default createLegacyPlugin({\n  packageName: 'expo-ads-admob',\n  fallback: [withAndroidAdMob, withIosAdMob],\n});\n"], "mappings": ";;;;;;AAAA,SAAAA,kBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,iBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,cAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,aAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,oBAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,mBAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2D,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE5C,IAAAC,wCAAkB,EAAC;EAChCC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAACC,oCAAgB,EAAEC,4BAAY;AAC3C,CAAC,CAAC", "ignoreList": []}