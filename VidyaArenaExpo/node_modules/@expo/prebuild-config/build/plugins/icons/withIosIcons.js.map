{"version": 3, "file": "withIosIcons.js", "names": ["_configPlugins", "data", "require", "_imageUtils", "_fs", "_interopRequireDefault", "_path", "_AssetContents", "e", "__esModule", "default", "getProjectName", "IOSConfig", "XcodeUtils", "IMAGE_CACHE_NAME", "IMAGESET_PATH", "withIosIcons", "config", "withDangerousMod", "setIconsAsync", "modRequest", "projectRoot", "withXcodeProject", "icon", "getIcons", "projectName", "path", "extname", "iconName", "basename", "setIconName", "modResults", "addIconFileToProject", "exports", "iosSpecificIcons", "ios", "paths", "light", "dark", "tinted", "filter", "Boolean", "iconPath", "WarningAggregator", "addWarningIOS", "iosNamedProjectRoot", "getIosNamedProjectPath", "addLiquidGlassIcon", "fs", "promises", "mkdir", "join", "recursive", "imagesJson", "baseIconPath", "baseIcon", "generateUniversalIconAsync", "cache<PERSON>ey", "platform", "push", "darkIcon", "appearance", "tintedIcon", "writeContentsJsonAsync", "images", "getAppleIconName", "size", "scale", "name", "filename", "source", "generateImageAsync", "cacheType", "src", "width", "height", "removeTransparency", "resizeMode", "backgroundColor", "undefined", "createSquareAsync", "assetPath", "writeFile", "idiom", "appearances", "value", "sourceIconPath", "targetIconPath", "existsSync", "cp", "project", "configurations", "pbxXCBuildConfigurationSection", "Object", "values", "buildSettings", "ASSETCATALOG_COMPILER_APPICON_NAME", "addResourceFileToGroup", "filepath", "groupName", "isBuildFile", "verbose"], "sources": ["../../../src/plugins/icons/withIosIcons.ts"], "sourcesContent": ["import {\n  ConfigPlugin,\n  IOSConfig,\n  WarningAggregator,\n  withDangerousMod,\n  withXcodeProject,\n} from '@expo/config-plugins';\nimport { ExpoConfig, IOSIcons } from '@expo/config-types';\nimport { createSquareAsync, generateImageAsync } from '@expo/image-utils';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { ContentsJson, ContentsJsonImage, writeContentsJsonAsync } from './AssetContents';\n\nconst { getProjectName } = IOSConfig.XcodeUtils;\n\nconst IMAGE_CACHE_NAME = 'icons';\nconst IMAGESET_PATH = 'Images.xcassets/AppIcon.appiconset';\n\nexport const withIosIcons: ConfigPlugin = (config) => {\n  config = withDangerousMod(config, [\n    'ios',\n    async (config) => {\n      await setIconsAsync(config, config.modRequest.projectRoot);\n      return config;\n    },\n  ]);\n\n  config = withXcodeProject(config, (config) => {\n    const icon = getIcons(config);\n    const projectName = config.modRequest.projectName;\n\n    if (icon && typeof icon === 'string' && path.extname(icon) === '.icon' && projectName) {\n      const iconName = path.basename(icon, '.icon');\n      setIconName(config.modResults, iconName);\n      addIconFileToProject(config.modResults, projectName, iconName);\n    }\n    return config;\n  });\n\n  return config;\n};\n\nexport function getIcons(config: Pick<ExpoConfig, 'icon' | 'ios'>): IOSIcons | string | null {\n  const iosSpecificIcons = config.ios?.icon;\n\n  if (iosSpecificIcons) {\n    // For backwards compatibility, the icon can be a string\n    if (typeof iosSpecificIcons === 'string') {\n      return iosSpecificIcons || config.icon || null;\n    }\n\n    if (typeof iosSpecificIcons === 'object') {\n      const paths = [iosSpecificIcons.light, iosSpecificIcons.dark, iosSpecificIcons.tinted].filter(\n        Boolean\n      );\n      for (const iconPath of paths) {\n        if (typeof iconPath === 'string' && path.extname(iconPath) === '.icon') {\n          WarningAggregator.addWarningIOS(\n            'icon',\n            `Liquid glass icons (.icon) should be provided as a string to the \"ios.icon\" property, not as an object. Found: \"${iconPath}\"`\n          );\n        }\n      }\n    }\n\n    // in iOS 18 introduced the ability to specify dark and tinted icons, which users can specify as an object\n    if (!iosSpecificIcons.light && !iosSpecificIcons.dark && !iosSpecificIcons.tinted) {\n      return config.icon || null;\n    }\n\n    return iosSpecificIcons;\n  }\n\n  // Top level icon property should not be used to specify a `.icon` folder\n  if (config.icon && typeof config.icon === 'string' && path.extname(config.icon) === '.icon') {\n    WarningAggregator.addWarningIOS(\n      'icon',\n      `Liquid glass icons (.icon) should be provided via the \"ios.icon\" property, not the root \"icon\" property. Found: \"${config.icon}\"`\n    );\n  }\n\n  if (config.icon) {\n    return config.icon;\n  }\n\n  return null;\n}\n\nexport async function setIconsAsync(config: ExpoConfig, projectRoot: string) {\n  const icon = getIcons(config);\n\n  if (\n    !icon ||\n    (typeof icon === 'string' && !icon) ||\n    (typeof icon === 'object' && !icon?.light && !icon?.dark && !icon?.tinted)\n  ) {\n    WarningAggregator.addWarningIOS('icon', 'No icon is defined in the Expo config.');\n  }\n\n  // Something like projectRoot/ios/MyApp/\n  const iosNamedProjectRoot = getIosNamedProjectPath(projectRoot);\n\n  if (typeof icon === 'string' && path.extname(icon) === '.icon') {\n    return await addLiquidGlassIcon(icon, projectRoot, iosNamedProjectRoot);\n  }\n\n  // Ensure the Images.xcassets/AppIcon.appiconset path exists\n  await fs.promises.mkdir(path.join(iosNamedProjectRoot, IMAGESET_PATH), { recursive: true });\n\n  const imagesJson: ContentsJson['images'] = [];\n\n  const baseIconPath = typeof icon === 'object' ? icon?.light || icon?.dark || icon?.tinted : icon;\n\n  // Store the image JSON data for assigning via the Contents.json\n  const baseIcon = await generateUniversalIconAsync(projectRoot, {\n    icon: baseIconPath,\n    cacheKey: 'universal-icon',\n    iosNamedProjectRoot,\n    platform: 'ios',\n  });\n\n  imagesJson.push(baseIcon);\n\n  if (typeof icon === 'object') {\n    if (icon?.dark) {\n      const darkIcon = await generateUniversalIconAsync(projectRoot, {\n        icon: icon.dark,\n        cacheKey: 'universal-icon-dark',\n        iosNamedProjectRoot,\n        platform: 'ios',\n        appearance: 'dark',\n      });\n\n      imagesJson.push(darkIcon);\n    }\n\n    if (icon?.tinted) {\n      const tintedIcon = await generateUniversalIconAsync(projectRoot, {\n        icon: icon.tinted,\n        cacheKey: 'universal-icon-tinted',\n        iosNamedProjectRoot,\n        platform: 'ios',\n        appearance: 'tinted',\n      });\n\n      imagesJson.push(tintedIcon);\n    }\n  }\n\n  // Finally, write the Contents.json\n  await writeContentsJsonAsync(path.join(iosNamedProjectRoot, IMAGESET_PATH), {\n    images: imagesJson,\n  });\n}\n\n/**\n * Return the project's named iOS path: ios/MyProject/\n *\n * @param projectRoot Expo project root path.\n */\nfunction getIosNamedProjectPath(projectRoot: string): string {\n  const projectName = getProjectName(projectRoot);\n  return path.join(projectRoot, 'ios', projectName);\n}\n\nfunction getAppleIconName(size: number, scale: number, appearance?: 'dark' | 'tinted'): string {\n  let name = 'App-Icon';\n\n  if (appearance) {\n    name = `${name}-${appearance}`;\n  }\n\n  name = `${name}-${size}x${size}@${scale}x.png`;\n\n  return name;\n}\n\nexport async function generateUniversalIconAsync(\n  projectRoot: string,\n  {\n    icon,\n    cacheKey,\n    iosNamedProjectRoot,\n    platform,\n    appearance,\n  }: {\n    platform: 'watchos' | 'ios';\n    icon?: string | null;\n    appearance?: 'dark' | 'tinted';\n    iosNamedProjectRoot: string;\n    cacheKey: string;\n  }\n): Promise<ContentsJsonImage> {\n  const size = 1024;\n  const filename = getAppleIconName(size, 1, appearance);\n\n  let source: Buffer;\n\n  if (icon) {\n    // Using this method will cache the images in `.expo` based on the properties used to generate them.\n    // this method also supports remote URLs and using the global sharp instance.\n    source = (\n      await generateImageAsync(\n        { projectRoot, cacheType: IMAGE_CACHE_NAME + cacheKey },\n        {\n          src: icon,\n          name: filename,\n          width: size,\n          height: size,\n          // Transparency needs to be preserved in dark variant, but can safely be removed in \"light\" and \"tinted\" variants.\n          removeTransparency: appearance !== 'dark',\n          // The icon should be square, but if it's not then it will be cropped.\n          resizeMode: 'cover',\n          // Force the background color to solid white to prevent any transparency. (for \"any\" and \"tinted\" variants)\n          // TODO: Maybe use a more adaptive option based on the icon color?\n          backgroundColor: appearance !== 'dark' ? '#ffffff' : undefined,\n        }\n      )\n    ).source;\n  } else {\n    // Create a white square image if no icon exists to mitigate the chance of a submission failure to the app store.\n    source = await createSquareAsync({ size });\n  }\n  // Write image buffer to the file system.\n  const assetPath = path.join(iosNamedProjectRoot, IMAGESET_PATH, filename);\n  await fs.promises.writeFile(assetPath, source);\n\n  return {\n    filename,\n    idiom: 'universal',\n    platform,\n    size: `${size}x${size}`,\n    ...(appearance ? { appearances: [{ appearance: 'luminosity', value: appearance }] } : {}),\n  };\n}\n\nasync function addLiquidGlassIcon(\n  iconPath: string,\n  projectRoot: string,\n  iosNamedProjectRoot: string\n): Promise<void> {\n  const iconName = path.basename(iconPath, '.icon');\n  const sourceIconPath = path.join(projectRoot, iconPath);\n  const targetIconPath = path.join(iosNamedProjectRoot, `${iconName}.icon`);\n\n  if (!fs.existsSync(sourceIconPath)) {\n    WarningAggregator.addWarningIOS(\n      'icon',\n      `Liquid glass icon file not found at path: ${iconPath}`\n    );\n    return;\n  }\n\n  await fs.promises.cp(sourceIconPath, targetIconPath, { recursive: true });\n}\n\n/**\n * Adds the .icons name to the project\n */\nfunction setIconName(project: any, iconName: string): void {\n  const configurations = project.pbxXCBuildConfigurationSection();\n\n  for (const config of Object.values(configurations)) {\n    if ((config as any)?.buildSettings) {\n      (config as any).buildSettings.ASSETCATALOG_COMPILER_APPICON_NAME = iconName;\n    }\n  }\n}\n\n/**\n * Adds the .icon file to the project\n */\nfunction addIconFileToProject(project: any, projectName: string, iconName: string): void {\n  const iconPath = `${iconName}.icon`;\n\n  IOSConfig.XcodeUtils.addResourceFileToGroup({\n    filepath: `${projectName}/${iconPath}`,\n    groupName: projectName,\n    project,\n    isBuildFile: true,\n    verbose: true,\n  });\n}\n"], "mappings": ";;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA,SAAAE,YAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,eAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,cAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA0F,SAAAI,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE1F,MAAM;EAAEG;AAAe,CAAC,GAAGC,0BAAS,CAACC,UAAU;AAE/C,MAAMC,gBAAgB,GAAG,OAAO;AAChC,MAAMC,aAAa,GAAG,oCAAoC;AAEnD,MAAMC,YAA0B,GAAIC,MAAM,IAAK;EACpDA,MAAM,GAAG,IAAAC,iCAAgB,EAACD,MAAM,EAAE,CAChC,KAAK,EACL,MAAOA,MAAM,IAAK;IAChB,MAAME,aAAa,CAACF,MAAM,EAAEA,MAAM,CAACG,UAAU,CAACC,WAAW,CAAC;IAC1D,OAAOJ,MAAM;EACf,CAAC,CACF,CAAC;EAEFA,MAAM,GAAG,IAAAK,iCAAgB,EAACL,MAAM,EAAGA,MAAM,IAAK;IAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACP,MAAM,CAAC;IAC7B,MAAMQ,WAAW,GAAGR,MAAM,CAACG,UAAU,CAACK,WAAW;IAEjD,IAAIF,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIG,eAAI,CAACC,OAAO,CAACJ,IAAI,CAAC,KAAK,OAAO,IAAIE,WAAW,EAAE;MACrF,MAAMG,QAAQ,GAAGF,eAAI,CAACG,QAAQ,CAACN,IAAI,EAAE,OAAO,CAAC;MAC7CO,WAAW,CAACb,MAAM,CAACc,UAAU,EAAEH,QAAQ,CAAC;MACxCI,oBAAoB,CAACf,MAAM,CAACc,UAAU,EAAEN,WAAW,EAAEG,QAAQ,CAAC;IAChE;IACA,OAAOX,MAAM;EACf,CAAC,CAAC;EAEF,OAAOA,MAAM;AACf,CAAC;AAACgB,OAAA,CAAAjB,YAAA,GAAAA,YAAA;AAEK,SAASQ,QAAQA,CAACP,MAAwC,EAA4B;EAC3F,MAAMiB,gBAAgB,GAAGjB,MAAM,CAACkB,GAAG,EAAEZ,IAAI;EAEzC,IAAIW,gBAAgB,EAAE;IACpB;IACA,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,EAAE;MACxC,OAAOA,gBAAgB,IAAIjB,MAAM,CAACM,IAAI,IAAI,IAAI;IAChD;IAEA,IAAI,OAAOW,gBAAgB,KAAK,QAAQ,EAAE;MACxC,MAAME,KAAK,GAAG,CAACF,gBAAgB,CAACG,KAAK,EAAEH,gBAAgB,CAACI,IAAI,EAAEJ,gBAAgB,CAACK,MAAM,CAAC,CAACC,MAAM,CAC3FC,OACF,CAAC;MACD,KAAK,MAAMC,QAAQ,IAAIN,KAAK,EAAE;QAC5B,IAAI,OAAOM,QAAQ,KAAK,QAAQ,IAAIhB,eAAI,CAACC,OAAO,CAACe,QAAQ,CAAC,KAAK,OAAO,EAAE;UACtEC,kCAAiB,CAACC,aAAa,CAC7B,MAAM,EACN,mHAAmHF,QAAQ,GAC7H,CAAC;QACH;MACF;IACF;;IAEA;IACA,IAAI,CAACR,gBAAgB,CAACG,KAAK,IAAI,CAACH,gBAAgB,CAACI,IAAI,IAAI,CAACJ,gBAAgB,CAACK,MAAM,EAAE;MACjF,OAAOtB,MAAM,CAACM,IAAI,IAAI,IAAI;IAC5B;IAEA,OAAOW,gBAAgB;EACzB;;EAEA;EACA,IAAIjB,MAAM,CAACM,IAAI,IAAI,OAAON,MAAM,CAACM,IAAI,KAAK,QAAQ,IAAIG,eAAI,CAACC,OAAO,CAACV,MAAM,CAACM,IAAI,CAAC,KAAK,OAAO,EAAE;IAC3FoB,kCAAiB,CAACC,aAAa,CAC7B,MAAM,EACN,oHAAoH3B,MAAM,CAACM,IAAI,GACjI,CAAC;EACH;EAEA,IAAIN,MAAM,CAACM,IAAI,EAAE;IACf,OAAON,MAAM,CAACM,IAAI;EACpB;EAEA,OAAO,IAAI;AACb;AAEO,eAAeJ,aAAaA,CAACF,MAAkB,EAAEI,WAAmB,EAAE;EAC3E,MAAME,IAAI,GAAGC,QAAQ,CAACP,MAAM,CAAC;EAE7B,IACE,CAACM,IAAI,IACJ,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAK,IAClC,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,EAAEc,KAAK,IAAI,CAACd,IAAI,EAAEe,IAAI,IAAI,CAACf,IAAI,EAAEgB,MAAO,EAC1E;IACAI,kCAAiB,CAACC,aAAa,CAAC,MAAM,EAAE,wCAAwC,CAAC;EACnF;;EAEA;EACA,MAAMC,mBAAmB,GAAGC,sBAAsB,CAACzB,WAAW,CAAC;EAE/D,IAAI,OAAOE,IAAI,KAAK,QAAQ,IAAIG,eAAI,CAACC,OAAO,CAACJ,IAAI,CAAC,KAAK,OAAO,EAAE;IAC9D,OAAO,MAAMwB,kBAAkB,CAACxB,IAAI,EAAEF,WAAW,EAAEwB,mBAAmB,CAAC;EACzE;;EAEA;EACA,MAAMG,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACxB,eAAI,CAACyB,IAAI,CAACN,mBAAmB,EAAE9B,aAAa,CAAC,EAAE;IAAEqC,SAAS,EAAE;EAAK,CAAC,CAAC;EAE3F,MAAMC,UAAkC,GAAG,EAAE;EAE7C,MAAMC,YAAY,GAAG,OAAO/B,IAAI,KAAK,QAAQ,GAAGA,IAAI,EAAEc,KAAK,IAAId,IAAI,EAAEe,IAAI,IAAIf,IAAI,EAAEgB,MAAM,GAAGhB,IAAI;;EAEhG;EACA,MAAMgC,QAAQ,GAAG,MAAMC,0BAA0B,CAACnC,WAAW,EAAE;IAC7DE,IAAI,EAAE+B,YAAY;IAClBG,QAAQ,EAAE,gBAAgB;IAC1BZ,mBAAmB;IACnBa,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEFL,UAAU,CAACM,IAAI,CAACJ,QAAQ,CAAC;EAEzB,IAAI,OAAOhC,IAAI,KAAK,QAAQ,EAAE;IAC5B,IAAIA,IAAI,EAAEe,IAAI,EAAE;MACd,MAAMsB,QAAQ,GAAG,MAAMJ,0BAA0B,CAACnC,WAAW,EAAE;QAC7DE,IAAI,EAAEA,IAAI,CAACe,IAAI;QACfmB,QAAQ,EAAE,qBAAqB;QAC/BZ,mBAAmB;QACnBa,QAAQ,EAAE,KAAK;QACfG,UAAU,EAAE;MACd,CAAC,CAAC;MAEFR,UAAU,CAACM,IAAI,CAACC,QAAQ,CAAC;IAC3B;IAEA,IAAIrC,IAAI,EAAEgB,MAAM,EAAE;MAChB,MAAMuB,UAAU,GAAG,MAAMN,0BAA0B,CAACnC,WAAW,EAAE;QAC/DE,IAAI,EAAEA,IAAI,CAACgB,MAAM;QACjBkB,QAAQ,EAAE,uBAAuB;QACjCZ,mBAAmB;QACnBa,QAAQ,EAAE,KAAK;QACfG,UAAU,EAAE;MACd,CAAC,CAAC;MAEFR,UAAU,CAACM,IAAI,CAACG,UAAU,CAAC;IAC7B;EACF;;EAEA;EACA,MAAM,IAAAC,uCAAsB,EAACrC,eAAI,CAACyB,IAAI,CAACN,mBAAmB,EAAE9B,aAAa,CAAC,EAAE;IAC1EiD,MAAM,EAAEX;EACV,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASP,sBAAsBA,CAACzB,WAAmB,EAAU;EAC3D,MAAMI,WAAW,GAAGd,cAAc,CAACU,WAAW,CAAC;EAC/C,OAAOK,eAAI,CAACyB,IAAI,CAAC9B,WAAW,EAAE,KAAK,EAAEI,WAAW,CAAC;AACnD;AAEA,SAASwC,gBAAgBA,CAACC,IAAY,EAAEC,KAAa,EAAEN,UAA8B,EAAU;EAC7F,IAAIO,IAAI,GAAG,UAAU;EAErB,IAAIP,UAAU,EAAE;IACdO,IAAI,GAAG,GAAGA,IAAI,IAAIP,UAAU,EAAE;EAChC;EAEAO,IAAI,GAAG,GAAGA,IAAI,IAAIF,IAAI,IAAIA,IAAI,IAAIC,KAAK,OAAO;EAE9C,OAAOC,IAAI;AACb;AAEO,eAAeZ,0BAA0BA,CAC9CnC,WAAmB,EACnB;EACEE,IAAI;EACJkC,QAAQ;EACRZ,mBAAmB;EACnBa,QAAQ;EACRG;AAOF,CAAC,EAC2B;EAC5B,MAAMK,IAAI,GAAG,IAAI;EACjB,MAAMG,QAAQ,GAAGJ,gBAAgB,CAACC,IAAI,EAAE,CAAC,EAAEL,UAAU,CAAC;EAEtD,IAAIS,MAAc;EAElB,IAAI/C,IAAI,EAAE;IACR;IACA;IACA+C,MAAM,GAAG,CACP,MAAM,IAAAC,gCAAkB,EACtB;MAAElD,WAAW;MAAEmD,SAAS,EAAE1D,gBAAgB,GAAG2C;IAAS,CAAC,EACvD;MACEgB,GAAG,EAAElD,IAAI;MACT6C,IAAI,EAAEC,QAAQ;MACdK,KAAK,EAAER,IAAI;MACXS,MAAM,EAAET,IAAI;MACZ;MACAU,kBAAkB,EAAEf,UAAU,KAAK,MAAM;MACzC;MACAgB,UAAU,EAAE,OAAO;MACnB;MACA;MACAC,eAAe,EAAEjB,UAAU,KAAK,MAAM,GAAG,SAAS,GAAGkB;IACvD,CACF,CAAC,EACDT,MAAM;EACV,CAAC,MAAM;IACL;IACAA,MAAM,GAAG,MAAM,IAAAU,+BAAiB,EAAC;MAAEd;IAAK,CAAC,CAAC;EAC5C;EACA;EACA,MAAMe,SAAS,GAAGvD,eAAI,CAACyB,IAAI,CAACN,mBAAmB,EAAE9B,aAAa,EAAEsD,QAAQ,CAAC;EACzE,MAAMrB,aAAE,CAACC,QAAQ,CAACiC,SAAS,CAACD,SAAS,EAAEX,MAAM,CAAC;EAE9C,OAAO;IACLD,QAAQ;IACRc,KAAK,EAAE,WAAW;IAClBzB,QAAQ;IACRQ,IAAI,EAAE,GAAGA,IAAI,IAAIA,IAAI,EAAE;IACvB,IAAIL,UAAU,GAAG;MAAEuB,WAAW,EAAE,CAAC;QAAEvB,UAAU,EAAE,YAAY;QAAEwB,KAAK,EAAExB;MAAW,CAAC;IAAE,CAAC,GAAG,CAAC,CAAC;EAC1F,CAAC;AACH;AAEA,eAAed,kBAAkBA,CAC/BL,QAAgB,EAChBrB,WAAmB,EACnBwB,mBAA2B,EACZ;EACf,MAAMjB,QAAQ,GAAGF,eAAI,CAACG,QAAQ,CAACa,QAAQ,EAAE,OAAO,CAAC;EACjD,MAAM4C,cAAc,GAAG5D,eAAI,CAACyB,IAAI,CAAC9B,WAAW,EAAEqB,QAAQ,CAAC;EACvD,MAAM6C,cAAc,GAAG7D,eAAI,CAACyB,IAAI,CAACN,mBAAmB,EAAE,GAAGjB,QAAQ,OAAO,CAAC;EAEzE,IAAI,CAACoB,aAAE,CAACwC,UAAU,CAACF,cAAc,CAAC,EAAE;IAClC3C,kCAAiB,CAACC,aAAa,CAC7B,MAAM,EACN,6CAA6CF,QAAQ,EACvD,CAAC;IACD;EACF;EAEA,MAAMM,aAAE,CAACC,QAAQ,CAACwC,EAAE,CAACH,cAAc,EAAEC,cAAc,EAAE;IAAEnC,SAAS,EAAE;EAAK,CAAC,CAAC;AAC3E;;AAEA;AACA;AACA;AACA,SAAStB,WAAWA,CAAC4D,OAAY,EAAE9D,QAAgB,EAAQ;EACzD,MAAM+D,cAAc,GAAGD,OAAO,CAACE,8BAA8B,CAAC,CAAC;EAE/D,KAAK,MAAM3E,MAAM,IAAI4E,MAAM,CAACC,MAAM,CAACH,cAAc,CAAC,EAAE;IAClD,IAAK1E,MAAM,EAAU8E,aAAa,EAAE;MACjC9E,MAAM,CAAS8E,aAAa,CAACC,kCAAkC,GAAGpE,QAAQ;IAC7E;EACF;AACF;;AAEA;AACA;AACA;AACA,SAASI,oBAAoBA,CAAC0D,OAAY,EAAEjE,WAAmB,EAAEG,QAAgB,EAAQ;EACvF,MAAMc,QAAQ,GAAG,GAAGd,QAAQ,OAAO;EAEnChB,0BAAS,CAACC,UAAU,CAACoF,sBAAsB,CAAC;IAC1CC,QAAQ,EAAE,GAAGzE,WAAW,IAAIiB,QAAQ,EAAE;IACtCyD,SAAS,EAAE1E,WAAW;IACtBiE,OAAO;IACPU,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ", "ignoreList": []}