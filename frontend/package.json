{"name": "VidyaArenaApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "web": "webpack serve --mode development", "build:web": "webpack --mode production"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native/new-app-screen": "0.81.4", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "axios": "^1.12.1", "react": "19.1.0", "react-dom": "^19.1.1", "react-native": "0.81.4", "react-native-gesture-handler": "^2.28.0", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-native-web": "^0.21.1"}, "devDependencies": {"@babel/core": "^7.28.4", "@babel/preset-env": "^7.28.3", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "20.0.0", "@react-native-community/cli-platform-android": "20.0.0", "@react-native-community/cli-platform-ios": "20.0.0", "@react-native/babel-preset": "0.81.4", "@react-native/eslint-config": "0.81.4", "@react-native/metro-config": "0.81.4", "@react-native/typescript-config": "0.81.4", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "babel-loader": "^10.0.0", "eslint": "^8.19.0", "html-webpack-plugin": "^5.6.4", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "^5.8.3", "webpack": "^5.101.3", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "engines": {"node": ">=20"}}